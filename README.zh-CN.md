<div align="center">
    <a href="https://arco.design/mobile/react" target="_blank">
        <img alt="Arco Design Logo" width="200" src="https://avatars.githubusercontent.com/u/64576149?s=200&v=4"/>
    </a>
</div>
<div align="center">
    <h1>Arco Design Mobile</h1>
</div>

<div align="center">

基于 [Arco Design](https://arco.design/mobile/react) 的 React UI 组件库。


![](https://img.shields.io/badge/-Less-%23CC6699?style=flat-square&logo=less&logoColor=ffffff)
![](https://img.shields.io/badge/-Typescript-blue?logo=typescript&logoColor=white)
![](https://img.shields.io/badge/-React.js-blue?logo=react&logoColor=white)


![](https://img.shields.io/npm/v/@arco-design/mobile-react.svg?style=flat-square)
![](https://img.shields.io/npm/dm/@arco-design/mobile-react.svg?style=flat-square)
[![license](https://img.shields.io/badge/license-MIT-blue.svg)](https://github.com/arco-design/arco-design-mobile/blob/main/LICENSE)

</div>

<div align="center">

[English](./README.md) | 简体中文

</div>

# 特性

- 基于 TypeScript 提供 50+ 开箱即用的组件
- 简洁克制的 UI 设计 & 精确到像素的还原
- 追求极致的手指交互效果
- 重要组件线上大流量验证
- 细粒度且灵活的属性配置
- 支持服务端渲染
- 支持国际化
- 支持按需引入
- 支持主题配置

# 安装

[npm package](https://www.npmjs.com/package/@arco-design/mobile-react)

```bash
// with npm
npm install @arco-design/mobile-react

// with yarn
yarn add @arco-design/mobile-react
```

# 示例

```typescript
import React from 'react';
import ReactDOM from 'react-dom';
import Button from '@arco-design/mobile-react/esm/button';
import '@arco-design/mobile-react/esm/button/style';

function App() {
  return (
    <Button>
      Hello World
    </Button>
  );
}

ReactDOM.render(<App />, document.getElementById('app'));
```

# 相关链接

* [官方文档](https://arco.design/mobile/react)
* [组件文档](https://arco.design/mobile/react/arco-design/pc/)
* [Figma 设计资源](https://www.figma.com/community/file/1143750379727993941)
* [Awesome Arco](https://github.com/arco-design/awesome-arco)

# 浏览器兼容性

| <img src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/browser-ios.png" alt="Safari" width="24px" height="24px" /><br/>Safari on iOS | <img src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/browser-android.png" alt="Opera" width="20px" height="24px" /><br/>Android Webview |
| --------- | --------- |
| 9 | 4.4 |

# 参与贡献

贡献之前请先阅读 [行为准则](./CODE_OF_CONDUCT.md) 和 [贡献指南](./CONTRIBUTING.zh-CN.md).

感谢所有为 Arco Design Mobile 做过贡献的人！

<a href="https://github.com/arco-design/arco-design-mobile/graphs/contributors"><img src="https://contrib.rocks/image?repo=arco-design/arco-design-mobile" style="height: 50px" /></a>

以及曾经的战友们：

<a href="https://github.com/wuyadream"><img src="https://github.com/wuyadream.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/Ariussssss"><img src="https://github.com/Ariussssss.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/WindyZYY"><img src="https://github.com/WindyZYY.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/10000lance"><img src="https://github.com/10000lance.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/GitHubzhangshuai"><img src="https://github.com/GitHubzhangshuai.png" style="height: 50px; border-radius: 50%" /></a>

# License

[MIT 协议](./LICENSE)

<br/><br/>

<div align="center">
    <a href="https://star-history.com/#arco-design/arco-design-mobile&Date"><img src="https://api.star-history.com/svg?repos=arco-design/arco-design-mobile&type=Date" style="max-width: 100%; width: 550px"/></a>
</div>
