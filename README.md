<div align="center">
    <a href="https://arco.design/mobile/react" target="_blank">
        <img alt="Arco Design Logo" width="200" src="https://avatars.githubusercontent.com/u/64576149?s=200&v=4"/>
    </a>
</div>
<div align="center">
    <h1>Arco Design Mobile</h1>
</div>

<div align="center">

A comprehensive React UI components library based on the [Arco Design](https://arco.design/mobile/react) system.

![](https://img.shields.io/badge/-Less-%23CC6699?style=flat-square&logo=less&logoColor=ffffff)
![](https://img.shields.io/badge/-Typescript-blue?logo=typescript&logoColor=white)
![](https://img.shields.io/badge/-React.js-blue?logo=react&logoColor=white)


![](https://img.shields.io/npm/v/@arco-design/mobile-react.svg?style=flat-square)
![](https://img.shields.io/npm/dm/@arco-design/mobile-react.svg?style=flat-square)
[![license](https://img.shields.io/badge/license-MIT-blue.svg)](https://github.com/arco-design/arco-design-mobile/blob/main/LICENSE)

</div>

<div align="center">

English | [简体中文](./README.zh-CN.md)

</div>

# Features

- Provides 50+ easy-to-use components based on TypeScript
- Simple and restrained UI design & pixel-accurate restoration
- Pursue the ultimate finger interaction effect
- Online high-traffic verification of important components
- Fine-grained and flexible property configuration
- Support server-side rendering
- Support internationalization
- Support on-demand introduction
- Support theme configuration


# Installation

Available as an [npm package](https://www.npmjs.com/package/@arco-design/mobile-react)

```bash
// with npm
npm install @arco-design/mobile-react

// with yarn
yarn add @arco-design/mobile-react
```

# Examples

```typescript
import React from 'react';
import ReactDOM from 'react-dom';
import Button from '@arco-design/mobile-react/esm/button';
import '@arco-design/mobile-react/esm/button/style';

function App() {
  return (
    <Button>
      Hello World
    </Button>
  );
}

ReactDOM.render(<App />, document.getElementById('app'));
```

# Useful Links

* [Documentation website](https://arco.design/mobile/react)
* [Components documentation](https://arco.design/mobile/react/arco-design/pc/)
* [Figma component library](https://www.figma.com/community/file/1143750379727993941)
* [Awesome Arco](https://github.com/arco-design/awesome-arco)

# Browser Support

| <img src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/browser-ios.png" alt="Safari" width="24px" height="24px" /><br/>Safari on iOS | <img src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/browser-android.png" alt="Opera" width="20px" height="24px" /><br/>Android Webview |
| --------- | --------- |
| 9 | 4.4 |

# Contributing

Developers interested in contributing should read the [Code of Conduct](./CODE_OF_CONDUCT.md) and the [Contributing Guide](./CONTRIBUTING.md).

Thank you to all the people who already contributed to Arco Design Mobile!

<a href="https://github.com/arco-design/arco-design-mobile/graphs/contributors"><img src="https://contrib.rocks/image?repo=arco-design/arco-design-mobile" style="height: 50px" /></a>

And our former comrades:

<a href="https://github.com/wuyadream"><img src="https://github.com/wuyadream.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/Ariussssss"><img src="https://github.com/Ariussssss.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/WindyZYY"><img src="https://github.com/WindyZYY.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/10000lance"><img src="https://github.com/10000lance.png" style="height: 50px; border-radius: 50%" /></a>
<a href="https://github.com/GitHubzhangshuai"><img src="https://github.com/GitHubzhangshuai.png" style="height: 50px; border-radius: 50%" /></a>

# License

This project is [MIT licensed](./LICENSE).

<br/><br/>

<div align="center">
    <a href="https://star-history.com/#arco-design/arco-design-mobile&Date"><img src="https://api.star-history.com/svg?repos=arco-design/arco-design-mobile&type=Date" style="max-width: 100%; width: 550px"/></a>
</div>
