# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.20.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.20.0...@arco-design/mobile-utils@2.20.1) (2024-11-21)

**Note:** Version bump only for package @arco-design/mobile-utils





# [2.20.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.19.0...@arco-design/mobile-utils@2.20.0) (2024-10-24)


### Features

* `Steps` support reverse order ([#287](https://github.com/arco-design/arco-design-mobile/issues/287)) ([8ed5614](https://github.com/arco-design/arco-design-mobile/commit/8ed5614d385d20bfb5fa1ada83d7f56e9f1c5150))





# [2.19.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.18.6...@arco-design/mobile-utils@2.19.0) (2024-08-16)


### Features

* build output support esnext ([0308755](https://github.com/arco-design/arco-design-mobile/commit/030875530d1068e801962c6fc57b9b3e1916772e))





## [2.18.6](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.18.5...@arco-design/mobile-utils@2.18.6) (2024-07-09)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.18.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.18.4...@arco-design/mobile-utils@2.18.5) (2024-07-01)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.18.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.18.3...@arco-design/mobile-utils@2.18.4) (2024-06-26)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.18.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.18.2...@arco-design/mobile-utils@2.18.3) (2024-05-11)


### Bug Fixes

* `Form` optimize "initialValues" and picker form item logic & add `DatePicker` default linked container ([#261](https://github.com/arco-design/arco-design-mobile/issues/261)) ([790ba3a](https://github.com/arco-design/arco-design-mobile/commit/790ba3a9dc4b289a20ddd18b5c11e2855bc6a8bd))





## [2.18.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.18.1...@arco-design/mobile-utils@2.18.2) (2024-04-19)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.18.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.18.0...@arco-design/mobile-utils@2.18.1) (2024-04-18)


### Bug Fixes

* support codeSandBox preview & adjust rtl mixins ([#254](https://github.com/arco-design/arco-design-mobile/issues/254)) ([5a7fb3a](https://github.com/arco-design/arco-design-mobile/commit/5a7fb3a4dd0ca8b1721036e5696b39e212905b5c))





# [2.18.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.10...@arco-design/mobile-utils@2.18.0) (2024-04-09)


### Bug Fixes

* `Form` support picker components ([#249](https://github.com/arco-design/arco-design-mobile/issues/249)) ([3b1f6f7](https://github.com/arco-design/arco-design-mobile/commit/3b1f6f7460ac2417fd7c099c8f26904be972750e))


### Features

* add component `Uploader` & abstract common logic of `ImagePicker` ([#251](https://github.com/arco-design/arco-design-mobile/issues/251)) ([9f6a795](https://github.com/arco-design/arco-design-mobile/commit/9f6a79573cfe5f74589ae244732db8c13ef07f0b))





## [2.17.10](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.9...@arco-design/mobile-utils@2.17.10) (2024-03-04)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.17.9](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.8...@arco-design/mobile-utils@2.17.9) (2024-02-27)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.17.8](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.7...@arco-design/mobile-utils@2.17.8) (2024-01-31)


### Bug Fixes

* `Ellipsis` optimize line height caculation ([#227](https://github.com/arco-design/arco-design-mobile/issues/227)) ([c791a8e](https://github.com/arco-design/arco-design-mobile/commit/c791a8e36309ce4887297fe711348f427e12954d))
* optimize string & boolean validator of `Form` ([#222](https://github.com/arco-design/arco-design-mobile/issues/222)) ([02c7158](https://github.com/arco-design/arco-design-mobile/commit/02c715895a32aa6e4bf364586445cc8ee31fd5a3))





## [2.17.7](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.6...@arco-design/mobile-utils@2.17.7) (2023-12-27)


### Bug Fixes

* style problem caused by the "tabIndex" attribute of lower versions of iOS ([#217](https://github.com/arco-design/arco-design-mobile/issues/217)) ([6a9406e](https://github.com/arco-design/arco-design-mobile/commit/6a9406ea0dfbdb50075a4c036757e6d7988c7d92))





## [2.17.6](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.5...@arco-design/mobile-utils@2.17.6) (2023-12-26)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.17.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.4...@arco-design/mobile-utils@2.17.5) (2023-12-19)


### Bug Fixes

* `ImagePreview` disable duplicate preventDefault ([#212](https://github.com/arco-design/arco-design-mobile/issues/212)) ([d91d251](https://github.com/arco-design/arco-design-mobile/commit/d91d251a64bd64ae914e88c119dda442bd72d722))
* rtl for `Progress`, `Pagination`, `Picker`, `SearchBar`, `Dialog`, `IndexBar` ([#213](https://github.com/arco-design/arco-design-mobile/issues/213)) ([498a777](https://github.com/arco-design/arco-design-mobile/commit/498a777992fc44410944ddc17bd2090c9b06a2f2))





## [2.17.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.3...@arco-design/mobile-utils@2.17.4) (2023-12-14)


### Bug Fixes

* support rtl for `DropdownMenu` ([#204](https://github.com/arco-design/arco-design-mobile/issues/204)) ([453dddb](https://github.com/arco-design/arco-design-mobile/commit/453dddb60b8f64e02654a8c3ece79bdc90e08fa6))





## [2.17.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.2...@arco-design/mobile-utils@2.17.3) (2023-11-24)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.17.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.1...@arco-design/mobile-utils@2.17.2) (2023-11-09)


### Bug Fixes

* `Input` & `Textarea` support appearance style ([#198](https://github.com/arco-design/arco-design-mobile/issues/198)) ([2872190](https://github.com/arco-design/arco-design-mobile/commit/28721909727cb7acd6d4853f176881110e90face))
* `NavBar` add aria-label to back arrow ([#196](https://github.com/arco-design/arco-design-mobile/issues/196)) ([ad97b65](https://github.com/arco-design/arco-design-mobile/commit/ad97b652d142a4d2331fbc4699c7d15d61153e50))





## [2.17.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.17.0...@arco-design/mobile-utils@2.17.1) (2023-11-07)

**Note:** Version bump only for package @arco-design/mobile-utils





# [2.17.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.9...@arco-design/mobile-utils@2.17.0) (2023-10-19)


### Features

* support dark mode ([#163](https://github.com/arco-design/arco-design-mobile/issues/163)) ([1693b1f](https://github.com/arco-design/arco-design-mobile/commit/1693b1f13bde86a075b6059dae290b9832c5cb99))
* support tool function & mixin analysis ([#147](https://github.com/arco-design/arco-design-mobile/issues/147)) ([f5b4dec](https://github.com/arco-design/arco-design-mobile/commit/f5b4decfb9b4ceadce8277be694f7f5a9a399d66))





## [2.16.9](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.8...@arco-design/mobile-utils@2.16.9) (2023-10-09)


### Bug Fixes

* incorrectly mounted elements when calling masking components using methods ([5740648](https://github.com/arco-design/arco-design-mobile/commit/5740648160fb6ff6d97a40da71c28d16034b4346))





## [2.16.8](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.7...@arco-design/mobile-utils@2.16.8) (2023-10-07)


### Bug Fixes

* `LoadMore` support "getDataWithEndReachCheck" ([#180](https://github.com/arco-design/arco-design-mobile/issues/180)) ([25baa3c](https://github.com/arco-design/arco-design-mobile/commit/25baa3cae665edb7fa4f316d80886f497c38f7a3))
* `Tabs` scroll offset in ssr when defaultActiveTab is greater than 0 ([#175](https://github.com/arco-design/arco-design-mobile/issues/175)) ([98aa1d7](https://github.com/arco-design/arco-design-mobile/commit/98aa1d7c5abe8851778f2aa5c2308fe807d5e4d9))





## [2.16.7](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.6...@arco-design/mobile-utils@2.16.7) (2023-09-13)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.16.6](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.5...@arco-design/mobile-utils@2.16.6) (2023-09-12)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.16.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.4...@arco-design/mobile-utils@2.16.5) (2023-08-28)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.16.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.3...@arco-design/mobile-utils@2.16.4) (2023-08-21)


### Bug Fixes

* fix wrong logic after click clear icon of `Input`, `Textarea` and `SearchBar` ([#156](https://github.com/arco-design/arco-design-mobile/issues/156)) ([b741263](https://github.com/arco-design/arco-design-mobile/commit/b741263fd1a1b9c3da9c106f7487714e33e042f2))





## [2.16.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.2...@arco-design/mobile-utils@2.16.3) (2023-08-17)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.16.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.1...@arco-design/mobile-utils@2.16.2) (2023-08-07)


### Bug Fixes

* `Form` bind this in form-item ([#148](https://github.com/arco-design/arco-design-mobile/issues/148)) ([5616d53](https://github.com/arco-design/arco-design-mobile/commit/5616d537b921b009df61addccf966c5e9363a0cb))





## [2.16.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.16.0...@arco-design/mobile-utils@2.16.1) (2023-08-02)


### Bug Fixes

* add [@types](https://github.com/types) to peerDependencies ([42f3d5a](https://github.com/arco-design/arco-design-mobile/commit/42f3d5ab19144702d7c371c6cbd1aa031a690abe))





# [2.16.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.15.5...@arco-design/mobile-utils@2.16.0) (2023-07-14)


### Bug Fixes

* `Collapse` update with the latest opened ([#140](https://github.com/arco-design/arco-design-mobile/issues/140)) ([b963787](https://github.com/arco-design/arco-design-mobile/commit/b96378761557f4d90f09f789f662e9d3588c3cbd))
* `ImagePreview` fix scroll through ([ea3f9bc](https://github.com/arco-design/arco-design-mobile/commit/ea3f9bc5d0980f70c81e2de99084e0a11187b3c1))


### Features

* RTL support for `Badge`, `Button`, `Cell`, `Checkbox`, `Form`, `Radio`, `Rate`, `Switch` and `Tabs` ([#135](https://github.com/arco-design/arco-design-mobile/issues/135)) ([97de976](https://github.com/arco-design/arco-design-mobile/commit/97de976ba514ec0f48103bd4f0c535ebceb8981a))





## [2.15.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.15.4...@arco-design/mobile-utils@2.15.5) (2023-07-04)


### Bug Fixes

* error caught when using "getComputedStyle" ([#129](https://github.com/arco-design/arco-design-mobile/issues/129)) ([daa8f67](https://github.com/arco-design/arco-design-mobile/commit/daa8f67961d9d2751a14c0c3f7759b54fe0579cb))





## [2.15.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.15.3...@arco-design/mobile-utils@2.15.4) (2023-05-19)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.15.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.15.2...@arco-design/mobile-utils@2.15.3) (2023-05-16)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.15.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.15.1...@arco-design/mobile-utils@2.15.2) (2023-05-10)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.15.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.15.0...@arco-design/mobile-utils@2.15.1) (2023-04-26)

**Note:** Version bump only for package @arco-design/mobile-utils





# [2.15.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.14.0...@arco-design/mobile-utils@2.15.0) (2023-04-25)


### Bug Fixes

* `PickerView` support the complete data currently selected in "onPickerChange" & code optimization ([#60](https://github.com/arco-design/arco-design-mobile/issues/60)) ([b90bbd2](https://github.com/arco-design/arco-design-mobile/commit/b90bbd24bd1fee554ef095144b25b6f36132fa0f))


### Features

* add new component `Keyboard` ([#79](https://github.com/arco-design/arco-design-mobile/issues/79)) ([44cea7f](https://github.com/arco-design/arco-design-mobile/commit/44cea7fe8e4febde454a83edfda9a546409213ed))





# [2.14.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.13.4...@arco-design/mobile-utils@2.14.0) (2023-04-11)


### Features

* `Carousel` & `NoticeBar` & `Input` & `Cell` rtl adaption ([bbd2fbb](https://github.com/arco-design/arco-design-mobile/commit/bbd2fbb3689a807f0d37b7ac51ac131e37224ae6))





## [2.13.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.13.3...@arco-design/mobile-utils@2.13.4) (2023-02-27)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.13.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.13.2...@arco-design/mobile-utils@2.13.3) (2023-02-10)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.13.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.13.1...@arco-design/mobile-utils@2.13.2) (2023-02-08)


### Bug Fixes

* wrap style division in parentheses ([ebe7a8f](https://github.com/arco-design/arco-design-mobile/commit/ebe7a8f8ba6f51ac75b45488b672e48ca9c2e0bb))





## [2.13.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.13.0...@arco-design/mobile-utils@2.13.1) (2023-02-01)


### Bug Fixes

* checkbox & radio exported type ([901681c](https://github.com/arco-design/arco-design-mobile/commit/901681ccfd4788b3e50954dbde6e4475ac1dbdc4))





# [2.13.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.11...@arco-design/mobile-utils@2.13.0) (2023-02-01)


### Bug Fixes

* `Carousel` optimize the method changeIndex ([0088985](https://github.com/arco-design/arco-design-mobile/commit/00889858f2826987db4930a84e297ac0399443c5))


### Features

* add new component `Form` ([#77](https://github.com/arco-design/arco-design-mobile/issues/77)) ([54b5bda](https://github.com/arco-design/arco-design-mobile/commit/54b5bda1c66b8318b59c9031fb0634c93dd94c7b))





## [2.12.11](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.10...@arco-design/mobile-utils@2.12.11) (2023-01-10)


### Bug Fixes

*  `Ellipsis` innerHTML keep wrap ([#80](https://github.com/arco-design/arco-design-mobile/issues/80)) ([29afadc](https://github.com/arco-design/arco-design-mobile/commit/29afadc571bba16fd2b8641622de1f2d0fbf5ba4))





## [2.12.10](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.9...@arco-design/mobile-utils@2.12.10) (2023-01-04)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.12.9](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.8...@arco-design/mobile-utils@2.12.9) (2022-12-20)


### Bug Fixes

* move common style to body ([#75](https://github.com/arco-design/arco-design-mobile/issues/75)) ([537c00f](https://github.com/arco-design/arco-design-mobile/commit/537c00ff869128e189d6b25b34061661e810f777))
* support accessibility mode ([#65](https://github.com/arco-design/arco-design-mobile/issues/65)) ([fa7789c](https://github.com/arco-design/arco-design-mobile/commit/fa7789c1866341244cbffcbef1d1be375880bd82))





## [2.12.8](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.7...@arco-design/mobile-utils@2.12.8) (2022-11-29)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.12.7](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.6...@arco-design/mobile-utils@2.12.7) (2022-11-23)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.12.6](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.4...@arco-design/mobile-utils@2.12.6) (2022-11-09)


### Bug Fixes

* `Steps` optimize style & support custom alignment ([617c570](https://github.com/arco-design/arco-design-mobile/commit/617c57003135215787897b48c07d2b674c482828))
* optimize .text-medium mixin ([ab62ab2](https://github.com/arco-design/arco-design-mobile/commit/ab62ab2cdb51174cbd2106467bbf1a3db4694982))





## [2.12.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.3...@arco-design/mobile-utils@2.12.4) (2022-10-28)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.12.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.2...@arco-design/mobile-utils@2.12.3) (2022-09-30)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.12.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.1...@arco-design/mobile-utils@2.12.2) (2022-09-07)

**Note:** Version bump only for package @arco-design/mobile-utils





## [2.12.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.12.0...@arco-design/mobile-utils@2.12.1) (2022-09-01)

**Note:** Version bump only for package @arco-design/mobile-utils





# [2.12.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.10.1...@arco-design/mobile-utils@2.12.0) (2022-08-29)


### Features

* add new component `ImagePicker` ([#24](https://github.com/arco-design/arco-design-mobile/issues/24)) ([73f6156](https://github.com/arco-design/arco-design-mobile/commit/73f615651f46dc670f29733f6b99ec56795fc48b))
* add new component `SearchBar` ([#22](https://github.com/arco-design/arco-design-mobile/issues/22)) ([36cf4d4](https://github.com/arco-design/arco-design-mobile/commit/36cf4d4d61506358cd84b41738d817db8399c04f))





# [2.11.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.10.1...@arco-design/mobile-utils@2.11.0) (2022-08-12)


### Features

* add new component `ImagePicker` ([#24](https://github.com/arco-design/arco-design-mobile/issues/24)) ([73f6156](https://github.com/arco-design/arco-design-mobile/commit/73f615651f46dc670f29733f6b99ec56795fc48b))





## [2.10.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-utils@2.10.0...@arco-design/mobile-utils@2.10.1) (2022-07-22)

**Note:** Version bump only for package @arco-design/mobile-utils





# 2.10.0 (2022-07-07)


### Features

* first publish for open source ([c11f528](https://github.com/arco-design/arco-design-mobile/commit/c11f528880afe3807f8d96e7667fd5b630a47f7e))
