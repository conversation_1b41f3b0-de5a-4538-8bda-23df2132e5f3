export { default as IconSquareChecked } from './IconSquareChecked';
export { default as IconTriUp } from './IconTriUp';
export { default as IconMore } from './IconMore';
export { default as IconSound } from './IconSound';
export { default as IconEyeVisible } from './IconEyeVisible';
export { default as IconEyelashInvisible } from './IconEyelashInvisible';
export { default as IconWarnCircleFill } from './IconWarnCircleFill';
export { default as IconSquareDisabled } from './IconSquareDisabled';
export { default as IconCircleChecked } from './IconCircleChecked';
export { default as IconSquareUnchecked } from './IconSquareUnchecked';
export { default as IconUser } from './IconUser';
export { default as IconEyeInvisible } from './IconEyeInvisible';
export { default as IconCircleUnchecked } from './IconCircleUnchecked';
export { default as IconCircleDisabled } from './IconCircleDisabled';
export { default as IconSetting } from './IconSetting';
export { default as IconMinus } from './IconMinus';
export { default as IconDelete } from './IconDelete';
export { default as IconPlay } from './IconPlay';
export { default as IconHeart } from './IconHeart';
export { default as IconStarFill } from './IconStarFill';
export { default as IconSearch } from './IconSearch';
export { default as IconEdit } from './IconEdit';
export { default as IconSad } from './IconSad';
export { default as IconLikeCircle } from './IconLikeCircle';
export { default as IconNotice } from './IconNotice';
export { default as IconHome } from './IconHome';
export { default as IconArrowIn } from './IconArrowIn';
export { default as IconCheck } from './IconCheck';
export { default as IconNoticeOff } from './IconNoticeOff';
export { default as IconSmileFill } from './IconSmileFill';
export { default as IconStar } from './IconStar';
export { default as IconRefresh } from './IconRefresh';
export { default as IconScan } from './IconScan';
export { default as IconPicture } from './IconPicture';
export { default as IconTriDown } from './IconTriDown';
export { default as IconClose } from './IconClose';
export { default as IconArrowUp } from './IconArrowUp';
export { default as IconArrowBack } from './IconArrowBack';
export { default as IconArrowDown } from './IconArrowDown';
export { default as IconAdd } from './IconAdd';
export { default as IconUserFill } from './IconUserFill';
export { default as IconClear } from './IconClear';
export { default as IconQuestionCircle } from './IconQuestionCircle';
export { default as IconSuccessCircle } from './IconSuccessCircle';
export { default as IconWarnCircle } from './IconWarnCircle';
export { default as IconErrorCircle } from './IconErrorCircle';
export { default as IconCheckBold } from './IconCheckBold';
export { default as IconCloseBold } from './IconCloseBold';
export { default as IconShop } from './IconShop';
export { default as IconShopping } from './IconShopping';
export { default as IconSubway } from './IconSubway';
export { default as IconGift } from './IconGift';
export { default as IconStarHalf } from './IconStarHalf';
export { default as IconKeyboard } from './IconKeyboard';
export { default as IconKeyboardDelete } from './IconKeyboardDelete';
export { default as IconDownload } from './IconDownload';
export { default as IconFile } from './IconFile';
export { default as IconUpload } from './IconUpload';

export * from './type';
