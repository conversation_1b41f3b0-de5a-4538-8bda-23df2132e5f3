import React, { SVGAttributes, CSSProperties } from 'react';
import { ContextLayout } from '../../context-provider';

export interface IconProps extends SVGAttributes<SVGElement> {
    className?: string;
    useCurrentColor?: boolean;
    style?: CSSProperties;
}

export default function IconHeart(props: IconProps) {
    const { className = '', useCurrentColor = true, style, ...other } = props;

    return (
        <ContextLayout>
            {({ prefixCls }) => (
                <svg
                    className={`${prefixCls}-icon ${prefixCls}-icon-heart ${className}`}
                    width="1em"
                    height="1em"
                    style={style}
                    fill={useCurrentColor ? 'currentColor' : '#000'}
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                    {...other}
                >
                    <defs />
                    <path d="M691.2 128c135.314 0 245.029 124.343 245.029 277.943 0 91.428-40.229 153.6-73.143 204.8C771.657 760.686 548.57 921.6 537.6 928.914c-10.971 7.315-21.943 10.972-32.914 10.972-10.972 0-21.943-3.657-32.915-10.972-10.971-7.314-230.4-168.228-321.828-318.171-32.914-51.2-73.143-113.372-73.143-204.8C76.8 252.343 186.514 128 321.829 128c47.542 0 84.114 14.629 120.685 40.229 21.943 14.628 40.229 36.571 58.515 62.171 18.285-25.6 36.571-47.543 58.514-62.171C607.086 142.629 647.314 128 691.2 128zm0 84.114c-51.2 0-109.714 47.543-179.2 138.972-69.486-91.429-131.657-138.972-179.2-138.972-87.771 0-160.914 84.115-160.914 190.172 0 47.543 14.628 84.114 43.885 135.314l3.658 7.314c3.657 3.657 7.314 10.972 10.971 18.286 3.657 7.314 10.971 18.286 18.286 25.6 25.6 36.571 65.828 80.457 109.714 124.343 36.571 32.914 76.8 69.486 117.029 98.743 10.971 7.314 25.6 21.943 36.571 29.257 10.971-10.972 25.6-18.286 40.229-29.257 40.228-32.915 80.457-65.829 117.028-102.4C713.143 665.6 753.371 625.37 778.971 588.8c7.315-10.971 14.629-18.286 18.286-29.257 7.314-10.972 10.972-18.286 14.629-25.6 29.257-51.2 43.885-87.772 43.885-135.314-3.657-102.4-76.8-186.515-164.571-186.515z" />
                </svg>
            )}
        </ContextLayout>
    );
}
