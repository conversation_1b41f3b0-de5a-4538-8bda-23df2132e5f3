{"description": "主动操作后显示的反馈信息横条，可采用方法调用或者组件调用的方式", "descriptionTags": {"en": "The feedback information bar displayed after active operation can be called by method or by component.", "type": "反馈", "type_en": "<PERSON><PERSON><PERSON>", "name": "消息通知", "name_en": "Notify"}, "displayName": "Notify", "methods": [{"description": "展示常规通知", "docblock": "展示常规通知\n@desc {en} Show regular notification\n@param config\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "", "type": "string | NotifyProps", "optional": false}], "return": {"name": "", "description": "", "type": "{ update: (config: NotifyProps) => void; close: () => void }", "optional": false}, "desc": {"en": "Show regular notification"}}, "modifiers": [], "name": "info", "params": [{"description": null, "name": "originConfig", "type": {"name": "string | Pick<WithGlobalContext<NotifyProps & RefAttributes<NotifyRef>>, \"getContainer\" | \"onClose\" | \"content\" | ... 7 more ... | \"context\">"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}, {"description": "展示成功通知", "docblock": "展示成功通知\n@desc {en} Show success notification\n@param config\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "", "type": "string | NotifyProps", "optional": false}], "return": {"name": "", "description": "", "type": "{ update: (config: NotifyProps) => void; close: () => void }", "optional": false}, "desc": {"en": "Show success notification"}}, "modifiers": [], "name": "success", "params": [{"description": null, "name": "originConfig", "type": {"name": "string | Pick<WithGlobalContext<NotifyProps & RefAttributes<NotifyRef>>, \"getContainer\" | \"onClose\" | \"content\" | ... 7 more ... | \"context\">"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}, {"description": "展示错误的通知", "docblock": "展示错误的通知\n@desc {en} Show error notification\n@param config\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "", "type": "string | NotifyProps", "optional": false}], "return": {"name": "", "description": "", "type": "{ update: (config: NotifyProps) => void; close: () => void }", "optional": false}, "desc": {"en": "Show error notification"}}, "modifiers": [], "name": "error", "params": [{"description": null, "name": "originConfig", "type": {"name": "string | Pick<WithGlobalContext<NotifyProps & RefAttributes<NotifyRef>>, \"getContainer\" | \"onClose\" | \"content\" | ... 7 more ... | \"context\">"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}, {"description": "展示警告的通知", "docblock": "展示警告的通知\n@desc {en} Show warning notification\n@param config\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "", "type": "string | NotifyProps", "optional": false}], "return": {"name": "", "description": "", "type": "{ update: (config: NotifyProps) => void; close: () => void }", "optional": false}, "desc": {"en": "Show warning notification"}}, "modifiers": [], "name": "warn", "params": [{"description": null, "name": "originConfig", "type": {"name": "string | Pick<WithGlobalContext<NotifyProps & RefAttributes<NotifyRef>>, \"getContainer\" | \"onClose\" | \"content\" | ... 7 more ... | \"context\">"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}], "props": {"className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "string"}}, "style": {"defaultValue": null, "description": "自定义样式\n@en Custom style", "name": "style", "tags": {"en": "Custom style"}, "descWithTags": "自定义样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "CSSProperties"}}, "content": {"defaultValue": null, "description": "需要展示的内容\n@en Notification content", "name": "content", "tags": {"en": "Notification content"}, "descWithTags": "需要展示的内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "ReactNode"}}, "type": {"defaultValue": {"value": "\"info\""}, "description": "通知类型\n@en Notification type", "name": "type", "tags": {"en": "Notification type", "default": "\"info\""}, "descWithTags": "通知类型", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "enum", "raw": "NotifyType", "value": [{"value": "\"success\""}, {"value": "\"error\""}, {"value": "\"warn\""}, {"value": "\"info\""}]}}, "visible": {"defaultValue": {"value": "false"}, "description": "是否显示通知\n@en Whether to show notification", "name": "visible", "tags": {"en": "Whether to show notification", "default": "false"}, "descWithTags": "是否显示通知", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "boolean"}}, "close": {"defaultValue": null, "description": "控制通知关闭的事件\n@en Close function", "name": "close", "tags": {"en": "Close function"}, "descWithTags": "控制通知关闭的事件", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "() => void"}}, "onClose": {"defaultValue": null, "description": "通知关闭时的回调函数\n@en Callback after closing", "name": "onClose", "tags": {"en": "Callback after closing"}, "descWithTags": "通知关闭时的回调函数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "() => void"}}, "getContainer": {"defaultValue": null, "description": "将通知放入某个模块\n@en Get mounted container", "name": "getContainer", "tags": {"en": "Get mounted container"}, "descWithTags": "将通知放入某个模块", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "() => HTMLElement"}}, "transitionDuration": {"defaultValue": {"value": "300"}, "description": "动画的执行时间 (单位ms)\n@en Animation execution time (unit: ms)", "name": "transitionDuration", "tags": {"en": "Animation execution time (unit: ms)", "default": "300"}, "descWithTags": "动画的执行时间 (单位ms)", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "number"}}, "duration": {"defaultValue": {"value": "3000"}, "description": "通知自动关闭时延 (单位ms) 设置为0时不会自动关闭\n@en The delay of notification automatic closing (unit: ms). Will not automatically close when it is set to 0", "name": "duration", "tags": {"en": "The delay of notification automatic closing (unit: ms). Will not automatically close when it is set to 0", "default": "3000"}, "descWithTags": "通知自动关闭时延 (单位ms) 设置为0时不会自动关闭", "parent": {"fileName": "arcom-github/packages/arcodesign/components/notify/type.ts", "name": "NotifyProps"}, "required": false, "type": {"name": "number"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<NotifyRef>"}}}, "deps": {"NotifyType": "\"success\" | \"error\" | \"warn\" | \"info\"", "NotifyRef": {"dom": {"name": "dom", "required": true, "description": "最外层dom元素\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层dom元素"}, "updateLayout": {"name": "updateLayout", "required": true, "description": "更新元素布局\n@en Update DOM layout", "defaultValue": null, "type": {"name": "() => void"}, "tags": {"en": "Update DOM layout"}, "descWithTags": "更新元素布局"}}, "GlobalContextParams": {"prefixCls": {"name": "prefixCls", "required": false, "description": "组件类名前缀\n@en Component classname prefix", "defaultValue": {"value": "\"arco\""}, "type": {"name": "string"}, "tags": {"en": "Component classname prefix", "default": "\"arco\""}, "descWithTags": "组件类名前缀"}, "system": {"name": "system", "required": false, "description": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用\n@en Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "defaultValue": {"value": "\"\""}, "type": {"name": "enum", "raw": "\"\" | \"pc\" | \"android\" | \"ios\"", "value": [{"value": "\"\""}, {"value": "\"pc\""}, {"value": "\"android\""}, {"value": "\"ios\""}]}, "tags": {"en": "Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "default": "\"\""}, "descWithTags": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用"}, "useDarkMode": {"name": "useDarkMode", "required": false, "description": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式\n@en Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "default": "false"}, "descWithTags": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式"}, "isDarkMode": {"name": "isDarkMode", "required": false, "description": "是否处于暗黑模式，指定后以指定的值为准\n@en Whether it is in dark mode, the value shall prevail after being specified", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether it is in dark mode, the value shall prevail after being specified", "default": "false"}, "descWithTags": "是否处于暗黑模式，指定后以指定的值为准"}, "darkModeSelector": {"name": "darkModeSelector", "required": false, "description": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名\n@en When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "defaultValue": {"value": "\"arco-theme-dark\""}, "type": {"name": "string"}, "tags": {"en": "When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "default": "\"arco-theme-dark\""}, "descWithTags": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名"}, "theme": {"name": "theme", "required": false, "description": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1\n@en Theme variable. The css variable will be replaced online after input. The less variable needs to be set\n@use-css-vars : 1", "defaultValue": null, "type": {"name": "Record<string, string>"}, "tags": {"en": "Theme variable. The css variable will be replaced online after input. The less variable needs to be set", "use-css-vars": ": 1"}, "descWithTags": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1"}, "locale": {"name": "locale", "required": false, "description": "国际化语言包配置\n@en Internationalized language configuration", "defaultValue": null, "type": {"name": "ILocale"}, "tags": {"en": "Internationalized language configuration"}, "descWithTags": "国际化语言包配置"}, "useRtl": {"name": "useRtl", "required": false, "description": "是否使用Rtl模式\n@en Whether to use rtl", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to use rtl", "default": "false"}, "descWithTags": "是否使用Rtl模式"}, "onDarkModeChange": {"name": "onDarkModeChange", "required": false, "description": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效\n@en Triggered when the system's native dark mode changes, valid when useDarkMode=true", "defaultValue": null, "type": {"name": "(isDark: boolean) => void"}, "tags": {"en": "Triggered when the system's native dark mode changes, valid when useDarkMode=true"}, "descWithTags": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效"}}, "ILocale": {"locale": {"name": "locale", "required": true, "description": "语言类型\n@en Language Type", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Language Type"}, "descWithTags": "语言类型"}, "LoadMore": {"name": "LoadMore", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadMoreText: string; loadingText: string; prepareText: string; noDataText: string; failLoadText: string; prepareScrollText: string; prepareClickText: string; }"}, "tags": {}, "descWithTags": ""}, "Picker": {"name": "Picker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "Tag": {"name": "Tag", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ addTag: string; }"}, "tags": {}, "descWithTags": ""}, "Dialog": {"name": "Dialog", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "SwipeLoad": {"name": "SwipeLoad", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ normalText: string; activeText: string; }"}, "tags": {}, "descWithTags": ""}, "PullRefresh": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadingText: string; pullingText: string; finishText: string; loosingText: string; }"}, "tags": {}, "descWithTags": ""}, "DropdownMenu": {"name": "DropdownMenu", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ select: string; }"}, "tags": {}, "descWithTags": ""}, "Pagination": {"name": "Pagination", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ previousPage: string; nextPage: string; }"}, "tags": {}, "descWithTags": ""}, "Image": {"name": "Image", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "ImagePicker": {"name": "ImagePicker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "SearchBar": {"name": "SearchBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ placeholder: string; cancelBtn: string; }"}, "tags": {}, "descWithTags": ""}, "Stepper": {"name": "Stepper", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ minusButtonName: string; addButtonName: string; }"}, "tags": {}, "descWithTags": ""}, "Keyboard": {"name": "Keyboard", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ confirm: string; }"}, "tags": {}, "descWithTags": ""}, "Form": {"name": "Form", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ required: string; type: { email: string; url: string; string: string; number: string; array: string; object: string; boolean: string; }; number: { min: string; max: string; equal: string; range: string; positive: string; negative: string; }; ... 4 more ...; pickerDefaultHint: string; }"}, "tags": {}, "descWithTags": ""}, "NavBar": {"name": "NavBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ backBtnAriaLabel: string; }"}, "tags": {}, "descWithTags": ""}}}, "depComps": {}, "typeNameInfo": {"props": "NotifyProps", "ref": "NotifyRef"}, "isDefaultExport": true}