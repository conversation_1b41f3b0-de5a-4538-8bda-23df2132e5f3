@import '../../../style/mixin.less';

.@{prefix}-notify {
    width: 100%;
    overflow: hidden;
    position: relative;
    transition-property: height;
    .use-var(color, notify-font-color);
    .use-var(font-size, notify-font-size);
    &-content {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0;
        top: 0;
        transition-property: transform;
        .use-var(min-height, notify-min-height);
    }
    &-info {
        background: transparent;
        .use-var(color, notify-info-font-color);
    }
    &-success {
        .use-var(background, notify-success-background);
    }
    &-error {
        .use-var(background, notify-error-background);
    }
    &-warn {
        .use-var(background, notify-warn-background);
    }
    &-content-transition-Y0 {
        transform: translateY(0);
    }
    &-content-transition-Y100 {
        transform: translateY(-100%);
    }
}
