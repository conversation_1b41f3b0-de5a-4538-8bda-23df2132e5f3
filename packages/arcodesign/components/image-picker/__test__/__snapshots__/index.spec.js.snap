// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`image-picker demo test image-picker demo: columns.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-0"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-0"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-0)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-0)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-1"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-1"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-1)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-1)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-2"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-2"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-2)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-2)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: define-render1.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-3"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-3"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-3)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-3)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="custom-delete custom-delete-bg"
                  >
                    <svg
                      fill="none"
                      height="10"
                      viewBox="0 0 10 10"
                      width="10"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect
                        fill="white"
                        height="1.11647"
                        rx="0.2"
                        transform="matrix(0.705389 0.70882 -0.705389 0.70882 1.28711 0.500977)"
                        width="11.5787"
                      />
                      <rect
                        fill="white"
                        height="1.11647"
                        rx="0.2"
                        transform="matrix(0.705389 -0.70882 0.705389 0.70882 0.545898 8.70703)"
                        width="11.5787"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div>
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        opacity="0.8"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M9.69432 4.49173C9.87623 4.04355 10.3142 3.75 10.8011 3.75H19.1536C19.6303 3.75 20.0612 4.03159 20.2496 4.46618L20.9659 6.11842H26.8125C27.5374 6.11842 28.125 6.64861 28.125 7.30263V25.0658C28.125 25.7198 27.5374 26.25 26.8125 26.25H3.1875C2.46263 26.25 1.875 25.7198 1.875 25.0658V7.30263C1.875 6.64861 2.46263 6.11842 3.1875 6.11842H9.03409L9.69432 4.49173ZM15 21.8385C18.1066 21.8385 20.625 19.3201 20.625 16.2135C20.625 13.1069 18.1066 10.5885 15 10.5885C11.8934 10.5885 9.375 13.1069 9.375 16.2135C9.375 19.3201 11.8934 21.8385 15 21.8385ZM15 19.9635C17.0711 19.9635 18.75 18.2845 18.75 16.2135C18.75 14.1424 17.0711 12.4635 15 12.4635C12.9289 12.4635 11.25 14.1424 11.25 16.2135C11.25 18.2845 12.9289 19.9635 15 19.9635Z"
                          fill="#86909C"
                          fill-rule="evenodd"
                        />
                      </g>
                    </svg>
                    <p
                      style="font-size: 12px;"
                    >
                      Photo / Video
                    </p>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="demo-space"
    />
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-4"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-4"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-4)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-4)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="custom-delete"
                  >
                    <svg
                      fill="none"
                      height="10"
                      viewBox="0 0 10 10"
                      width="10"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect
                        fill="white"
                        height="1.11647"
                        rx="0.2"
                        transform="matrix(0.705389 0.70882 -0.705389 0.70882 1.28711 0.500977)"
                        width="11.5787"
                      />
                      <rect
                        fill="white"
                        height="1.11647"
                        rx="0.2"
                        transform="matrix(0.705389 -0.70882 0.705389 0.70882 0.545898 8.70703)"
                        width="11.5787"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div>
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        opacity="0.8"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M9.69432 4.49173C9.87623 4.04355 10.3142 3.75 10.8011 3.75H19.1536C19.6303 3.75 20.0612 4.03159 20.2496 4.46618L20.9659 6.11842H26.8125C27.5374 6.11842 28.125 6.64861 28.125 7.30263V25.0658C28.125 25.7198 27.5374 26.25 26.8125 26.25H3.1875C2.46263 26.25 1.875 25.7198 1.875 25.0658V7.30263C1.875 6.64861 2.46263 6.11842 3.1875 6.11842H9.03409L9.69432 4.49173ZM15 21.8385C18.1066 21.8385 20.625 19.3201 20.625 16.2135C20.625 13.1069 18.1066 10.5885 15 10.5885C11.8934 10.5885 9.375 13.1069 9.375 16.2135C9.375 19.3201 11.8934 21.8385 15 21.8385ZM15 19.9635C17.0711 19.9635 18.75 18.2845 18.75 16.2135C18.75 14.1424 17.0711 12.4635 15 12.4635C12.9289 12.4635 11.25 14.1424 11.25 16.2135C11.25 18.2845 12.9289 19.9635 15 19.9635Z"
                          fill="#86909C"
                          fill-rule="evenodd"
                        />
                      </g>
                    </svg>
                    <p
                      style="font-size: 12px;"
                    >
                      Photo / Video
                    </p>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: define-render2.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="loading1"
                      >
                        Loading...
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container error animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-error-container"
                    >
                      <div
                        class="arco-image-picker-error"
                      >
                        <p>
                          加载失败
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="demo-space"
    />
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-5"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-5"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-5)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-5)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container error animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-error-container"
                    >
                      <div
                        class="load-error1"
                      >
                        <div>
                          <svg
                            fill="none"
                            height="25"
                            viewBox="0 0 25 25"
                            width="25"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M12.5 24C6.15458 24 1 18.8454 1 12.5C1 6.15458 6.15458 1 12.5 1C18.8454 1 24 6.15458 24 12.5C24 18.8454 18.8454 24 12.5 24Z"
                              stroke="white"
                            />
                            <path
                              d="M12.5298 14.6972C9.45045 14.736 8.2352 17.2113 8.1837 17.3181C8.07042 17.5511 8.1837 17.8229 8.43087 17.9199C8.67804 18.0267 8.96641 17.9199 9.06939 17.687C9.07969 17.6676 10.0684 15.5805 12.5401 15.6H12.5813C14.9088 15.6 15.9078 17.5996 15.949 17.687C16.0622 17.9102 16.3403 18.017 16.5875 17.9102C16.8346 17.8035 16.9376 17.5414 16.8243 17.3084C16.7729 17.2016 15.537 14.6875 12.571 14.6875C12.571 14.6972 12.5504 14.6972 12.5298 14.6972Z"
                              fill="white"
                              stroke="white"
                              stroke-width="0.2"
                            />
                            <path
                              clip-rule="evenodd"
                              d="M16.8698 8.13965C17.4725 8.13965 17.9611 8.62825 17.9611 9.23097V10.868C17.9611 11.4707 17.4725 11.9593 16.8698 11.9593C16.267 11.9593 15.7784 11.4707 15.7784 10.868V9.23097C15.7784 8.62825 16.267 8.13965 16.8698 8.13965ZM8.13918 8.13965C8.7419 8.13965 9.2305 8.62825 9.2305 9.23097V10.868C9.2305 11.4707 8.7419 11.9593 8.13918 11.9593C7.53645 11.9593 7.04785 11.4707 7.04785 10.868V9.23097C7.04785 8.62825 7.53645 8.13965 8.13918 8.13965Z"
                              fill="white"
                              fill-rule="evenodd"
                            />
                          </svg>
                          <p>
                            Load failed
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: define-select.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-image-picker"
  >
    <div
      class="arco-image-picker-container"
    >
      <div
        class="arco-grid"
      >
        <div
          class="arco-grid-rows"
        >
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 8px;"
          >
            <div
              class="arco-image-picker-image"
            >
              <div
                class="arco-image-picker-image-container"
              >
                <div
                  class="arco-image all-border-box pc"
                >
                  <div
                    class="image-content image-bottom-overlap"
                  >
                    <div
                      class="image-placeholder"
                    />
                  </div>
                  <div
                    class="image-container loading animate"
                    style="transition-duration: 200ms;"
                  />
                  <div
                    class="image-content image-loading-container"
                  >
                    <div
                      class="image-loading"
                    >
                      <div
                        class="arco-loading all-border-box circle loading-icon"
                        style="animation-duration: 1000ms; width: 20px; height: 20px;"
                      >
                        <svg
                          viewBox="0 0 20 20"
                        >
                          <defs>
                            <lineargradient
                              id="grad1-inner-6"
                              x1="0%"
                              x2="100%"
                              y1="0%"
                              y2="0%"
                            >
                              <stop
                                class="loading-circle-middle stop-color-with-config"
                                offset="0%"
                              />
                              <stop
                                class="loading-circle-start stop-color-with-config"
                                offset="100%"
                              />
                            </lineargradient>
                            <lineargradient
                              id="grad2-inner-6"
                              x1="0%"
                              x2="100%"
                              y1="0%"
                              y2="0%"
                            >
                              <stop
                                class="loading-circle-middle stop-color-with-config"
                                offset="0%"
                              />
                              <stop
                                class="loading-circle-end stop-color-with-config"
                                offset="100%"
                              />
                            </lineargradient>
                          </defs>
                          <circle
                            cx="10"
                            cy="10"
                            fill="none"
                            r="8.5"
                            stroke="url(#grad1-inner-6)"
                            stroke-dasharray="26.703537555513243"
                            stroke-dashoffset="26.703537555513243"
                            stroke-width="3"
                          />
                          <circle
                            cx="10"
                            cy="10"
                            fill="none"
                            r="8.5"
                            stroke="url(#grad2-inner-6)"
                            stroke-dasharray="26.703537555513243"
                            stroke-width="3"
                          />
                          <circle
                            class="loading-circle-filleted fill-color-with-config"
                            cx="18.5"
                            cy="10"
                            r="1.5"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="arco-image-picker-image-mask"
                />
              </div>
              <div
                class="arco-image-picker-close"
              >
                <div
                  class="arco-image-picker-close-icon"
                >
                  <svg
                    class="arco-icon arco-icon-close "
                    height="1em"
                    viewBox="0 0 1024 1024"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <i
              class="vertical-border"
            />
          </div>
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 8px;"
          >
            <div
              class="arco-image-picker-add"
            >
              <div
                class="arco-image-picker-add-container"
              >
                <div
                  class="arco-image-picker-add-icon"
                >
                  <svg
                    fill="none"
                    height="30"
                    viewBox="0 0 30 30"
                    width="30"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                      fill="#C9CDD4"
                      fill-rule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <i
              class="vertical-border"
            />
          </div>
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 0px;"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: disable.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-image-picker arco-image-picker-disabled"
  >
    <div
      class="arco-image-picker-container"
    >
      <div
        class="arco-grid"
      >
        <div
          class="arco-grid-rows"
        >
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 8px;"
          >
            <div
              class="arco-image-picker-image"
            >
              <div
                class="arco-image-picker-image-container"
              >
                <div
                  class="arco-image all-border-box pc"
                >
                  <div
                    class="image-content image-bottom-overlap"
                  >
                    <div
                      class="image-placeholder"
                    />
                  </div>
                  <div
                    class="image-container loading animate"
                    style="transition-duration: 200ms;"
                  />
                  <div
                    class="image-content image-loading-container"
                  >
                    <div
                      class="image-loading"
                    >
                      <div
                        class="arco-loading all-border-box circle loading-icon"
                        style="animation-duration: 1000ms; width: 20px; height: 20px;"
                      >
                        <svg
                          viewBox="0 0 20 20"
                        >
                          <defs>
                            <lineargradient
                              id="grad1-inner-7"
                              x1="0%"
                              x2="100%"
                              y1="0%"
                              y2="0%"
                            >
                              <stop
                                class="loading-circle-middle stop-color-with-config"
                                offset="0%"
                              />
                              <stop
                                class="loading-circle-start stop-color-with-config"
                                offset="100%"
                              />
                            </lineargradient>
                            <lineargradient
                              id="grad2-inner-7"
                              x1="0%"
                              x2="100%"
                              y1="0%"
                              y2="0%"
                            >
                              <stop
                                class="loading-circle-middle stop-color-with-config"
                                offset="0%"
                              />
                              <stop
                                class="loading-circle-end stop-color-with-config"
                                offset="100%"
                              />
                            </lineargradient>
                          </defs>
                          <circle
                            cx="10"
                            cy="10"
                            fill="none"
                            r="8.5"
                            stroke="url(#grad1-inner-7)"
                            stroke-dasharray="26.703537555513243"
                            stroke-dashoffset="26.703537555513243"
                            stroke-width="3"
                          />
                          <circle
                            cx="10"
                            cy="10"
                            fill="none"
                            r="8.5"
                            stroke="url(#grad2-inner-7)"
                            stroke-dasharray="26.703537555513243"
                            stroke-width="3"
                          />
                          <circle
                            class="loading-circle-filleted fill-color-with-config"
                            cx="18.5"
                            cy="10"
                            r="1.5"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="arco-image-picker-image-mask"
                />
              </div>
              <div
                class="arco-image-picker-close"
              >
                <div
                  class="arco-image-picker-close-icon"
                >
                  <svg
                    class="arco-icon arco-icon-close "
                    height="1em"
                    viewBox="0 0 1024 1024"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <i
              class="vertical-border"
            />
          </div>
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 8px;"
          >
            <div
              class="arco-image-picker-add arco-image-picker-add-disabled"
            >
              <div
                class="arco-image-picker-add-container"
              >
                <div
                  class="arco-image-picker-add-icon"
                >
                  <svg
                    fill="none"
                    height="30"
                    viewBox="0 0 30 30"
                    width="30"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                      fill="#C9CDD4"
                      fill-rule="evenodd"
                    />
                  </svg>
                </div>
                <input
                  accept="image/*"
                  type="file"
                />
              </div>
            </div>
            <i
              class="vertical-border"
            />
          </div>
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 0px;"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: hide-select-close.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="demo-space"
    />
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-8"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-8"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-8)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-8)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="demo-space"
    />
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-9"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-9"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-9)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-9)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: image-event.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-10"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-10"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-10)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-10)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-11"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-11"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-11)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-11)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: image-props.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-12"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-12"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-12)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-12)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-13"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-13"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-13)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-13)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-image-picker"
  >
    <div
      class="arco-image-picker-container"
    >
      <div
        class="arco-grid"
      >
        <div
          class="arco-grid-rows"
        >
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 8px;"
          >
            <div
              class="arco-image-picker-image"
            >
              <div
                class="arco-image-picker-image-container"
              >
                <div
                  class="arco-image all-border-box pc"
                >
                  <div
                    class="image-content image-bottom-overlap"
                  >
                    <div
                      class="image-placeholder"
                    />
                  </div>
                  <div
                    class="image-container loading animate"
                    style="transition-duration: 200ms;"
                  />
                  <div
                    class="image-content image-loading-container"
                  >
                    <div
                      class="image-loading"
                    >
                      <div
                        class="arco-loading all-border-box circle loading-icon"
                        style="animation-duration: 1000ms; width: 20px; height: 20px;"
                      >
                        <svg
                          viewBox="0 0 20 20"
                        >
                          <defs>
                            <lineargradient
                              id="grad1-inner-14"
                              x1="0%"
                              x2="100%"
                              y1="0%"
                              y2="0%"
                            >
                              <stop
                                class="loading-circle-middle stop-color-with-config"
                                offset="0%"
                              />
                              <stop
                                class="loading-circle-start stop-color-with-config"
                                offset="100%"
                              />
                            </lineargradient>
                            <lineargradient
                              id="grad2-inner-14"
                              x1="0%"
                              x2="100%"
                              y1="0%"
                              y2="0%"
                            >
                              <stop
                                class="loading-circle-middle stop-color-with-config"
                                offset="0%"
                              />
                              <stop
                                class="loading-circle-end stop-color-with-config"
                                offset="100%"
                              />
                            </lineargradient>
                          </defs>
                          <circle
                            cx="10"
                            cy="10"
                            fill="none"
                            r="8.5"
                            stroke="url(#grad1-inner-14)"
                            stroke-dasharray="26.703537555513243"
                            stroke-dashoffset="26.703537555513243"
                            stroke-width="3"
                          />
                          <circle
                            cx="10"
                            cy="10"
                            fill="none"
                            r="8.5"
                            stroke="url(#grad2-inner-14)"
                            stroke-dasharray="26.703537555513243"
                            stroke-width="3"
                          />
                          <circle
                            class="loading-circle-filleted fill-color-with-config"
                            cx="18.5"
                            cy="10"
                            r="1.5"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="arco-image-picker-image-mask"
                />
              </div>
              <div
                class="arco-image-picker-close"
              >
                <div
                  class="arco-image-picker-close-icon"
                >
                  <svg
                    class="arco-icon arco-icon-close "
                    height="1em"
                    viewBox="0 0 1024 1024"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <i
              class="vertical-border"
            />
          </div>
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 8px;"
          >
            <div
              class="arco-image-picker-add"
            >
              <div
                class="arco-image-picker-add-container"
              >
                <div
                  class="arco-image-picker-add-icon"
                >
                  <svg
                    fill="none"
                    height="30"
                    viewBox="0 0 30 30"
                    width="30"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                      fill="#C9CDD4"
                      fill-rule="evenodd"
                    />
                  </svg>
                </div>
                <input
                  accept="image/*"
                  type="file"
                />
              </div>
            </div>
            <i
              class="vertical-border"
            />
          </div>
          <div
            class="arco-grid-rows-item"
            style="margin-bottom: 0px; margin-right: 0px;"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: limit-size.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-15"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-15"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-15)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-15)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-16"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-16"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-16)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-16)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    multiple=""
                    type="file"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="demo-space"
    />
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-17"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-17"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-17)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-17)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-18"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-18"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-18)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-18)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    multiple=""
                    type="file"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image-picker demo test image-picker demo: upload.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="demo-space"
    />
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-19"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-19"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-19)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-19)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="demo-space"
    />
    <div
      class="arco-image-picker"
    >
      <div
        class="arco-image-picker-container"
      >
        <div
          class="arco-grid"
        >
          <div
            class="arco-grid-rows"
          >
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-image"
              >
                <div
                  class="arco-image-picker-image-container"
                >
                  <div
                    class="arco-image all-border-box pc"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                    <div
                      class="image-content image-loading-container"
                    >
                      <div
                        class="image-loading"
                      >
                        <div
                          class="arco-loading all-border-box circle loading-icon"
                          style="animation-duration: 1000ms; width: 20px; height: 20px;"
                        >
                          <svg
                            viewBox="0 0 20 20"
                          >
                            <defs>
                              <lineargradient
                                id="grad1-inner-20"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-start stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                              <lineargradient
                                id="grad2-inner-20"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="0%"
                              >
                                <stop
                                  class="loading-circle-middle stop-color-with-config"
                                  offset="0%"
                                />
                                <stop
                                  class="loading-circle-end stop-color-with-config"
                                  offset="100%"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad1-inner-20)"
                              stroke-dasharray="26.703537555513243"
                              stroke-dashoffset="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              cx="10"
                              cy="10"
                              fill="none"
                              r="8.5"
                              stroke="url(#grad2-inner-20)"
                              stroke-dasharray="26.703537555513243"
                              stroke-width="3"
                            />
                            <circle
                              class="loading-circle-filleted fill-color-with-config"
                              cx="18.5"
                              cy="10"
                              r="1.5"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="arco-image-picker-image-mask"
                  />
                </div>
                <div
                  class="arco-image-picker-close"
                >
                  <div
                    class="arco-image-picker-close-icon"
                  >
                    <svg
                      class="arco-icon arco-icon-close "
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 8px;"
            >
              <div
                class="arco-image-picker-add"
              >
                <div
                  class="arco-image-picker-add-container"
                >
                  <div
                    class="arco-image-picker-add-icon"
                  >
                    <svg
                      fill="none"
                      height="30"
                      viewBox="0 0 30 30"
                      width="30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                        fill="#C9CDD4"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    accept="image/*"
                    type="file"
                  />
                </div>
              </div>
              <i
                class="vertical-border"
              />
            </div>
            <div
              class="arco-grid-rows-item"
              style="margin-bottom: 0px; margin-right: 0px;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
