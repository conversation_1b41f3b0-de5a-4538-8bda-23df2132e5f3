{"description": "下拉刷新数据组件。", "descriptionTags": {"en": "PullRefresh component that pulls down to refresh data.", "name": "下拉刷新", "name_en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "其他", "type_en": "Others"}, "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": [], "props": {"type": {"defaultValue": {"value": "跟随系统"}, "description": "下拉刷新组件不同的实现方式，android - 常规的外层容器 transform，ios - 利用 ios scrollTop 负值实现下拉及回弹(性能优于transform)，仅ios可用。指定该属性值后优先级高于 useIosOptimize\n@en Different implementation methods of PullRefresh, android - conventional outer container transform, ios - use ios scrollTop negative value to achieve pull-down and rebound (performance is better than transform), only available for ios. After specifying value, the priority is higher than useIosOptimize\n@default_en Follow the system", "name": "type", "tags": {"en": "Different implementation methods of PullRefresh, android - conventional outer container transform, ios - use ios scrollTop negative value to achieve pull-down and rebound (performance is better than transform), only available for ios. After specifying value, the priority is higher than useIosOptimize", "default": "跟随系统", "default_en": "Follow the system"}, "descWithTags": "下拉刷新组件不同的实现方式，android - 常规的外层容器 transform，ios - 利用 ios scrollTop 负值实现下拉及回弹(性能优于transform)，仅ios可用。指定该属性值后优先级高于 useIosOptimize", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/index.tsx", "name": "PullRefreshProps"}, "required": false, "type": {"name": "enum", "raw": "\"ios\" | \"android\"", "value": [{"value": "\"ios\""}, {"value": "\"android\""}]}}, "useIosOptimize": {"defaultValue": {"value": "false"}, "description": "在 ios 上是否使用 scrollTop 负值实现下拉及回弹，而不是 transform，关闭后所有机型都使用 transform 的方式\n@en Whether to use the negative value of scrollTop on ios to achieve pull-down and rebound instead of transform. After closing, all models use transform", "name": "useIosOptimize", "tags": {"en": "Whether to use the negative value of scrollTop on ios to achieve pull-down and rebound instead of transform. After closing, all models use transform", "default": "false"}, "descWithTags": "在 ios 上是否使用 scrollTop 负值实现下拉及回弹，而不是 transform，关闭后所有机型都使用 transform 的方式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/index.tsx", "name": "PullRefreshProps"}, "required": false, "type": {"name": "boolean"}}, "children": {"defaultValue": null, "description": "子元素\n@en Children element", "name": "children", "tags": {"en": "Children element"}, "descWithTags": "子元素", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": true, "type": {"name": "ReactNode"}}, "className": {"defaultValue": null, "description": "样式类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "样式类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "string"}}, "style": {"defaultValue": null, "description": "自定义样式\n@en Custom style", "name": "style", "tags": {"en": "Custom style"}, "descWithTags": "自定义样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "CSSProperties"}}, "disabled": {"defaultValue": {"value": "false"}, "description": "是否禁用\n@en Whether to be disabled", "name": "disabled", "tags": {"en": "Whether to be disabled", "default": "false"}, "descWithTags": "是否禁用", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "boolean"}}, "finishDelay": {"defaultValue": {"value": "300"}, "description": "加载完成后，展示加载完成的时间(ms)\n@en After the loading is completed, the time to display the loading completion (ms)", "name": "finishDelay", "tags": {"en": "After the loading is completed, the time to display the loading completion (ms)", "default": "300"}, "descWithTags": "加载完成后，展示加载完成的时间(ms)", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "number"}}, "loosingText": {"defaultValue": null, "description": "下拉到可释放时的展示元素（type 为 ios 失效）\n@en Displayed element when pulled down to releasable (Invalid in type iOS)", "name": "loosingText", "tags": {"en": "Displayed element when pulled down to releasable (Invalid in type iOS)"}, "descWithTags": "下拉到可释放时的展示元素（type 为 ios 失效）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "ReactNode"}}, "loadingText": {"defaultValue": null, "description": "加载中的展示元素\n@en Loading displayed element", "name": "loadingText", "tags": {"en": "Loading displayed element"}, "descWithTags": "加载中的展示元素", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "ReactNode"}}, "pullingText": {"defaultValue": null, "description": "下拉时的展示元素\n@en Displayed element when pulling down", "name": "pullingText", "tags": {"en": "Displayed element when pulling down"}, "descWithTags": "下拉时的展示元素", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "ReactNode"}}, "finishText": {"defaultValue": null, "description": "加载完成的展示元素\n@en Loaded displayed element", "name": "finishText", "tags": {"en": "Loaded displayed element"}, "descWithTags": "加载完成的展示元素", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "ReactNode"}}, "initialText": {"defaultValue": {"value": "pullingText属性值"}, "description": "初始状态还未下拉时的展示元素\n@en The display element in the initial state when  has not been pulled down\n@defalt_en pullingText value", "name": "initialText", "tags": {"en": "The display element in the initial state when  has not been pulled down", "default": "pullingText属性值", "defalt_en": "pullingText value"}, "descWithTags": "初始状态还未下拉时的展示元素", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "ReactNode"}}, "dampRate": {"defaultValue": {"value": "4"}, "description": "阻尼系数（type 为 ios 失效）\n@en\n@en Damping coefficient (Invalid in type iOS)", "name": "dampRate", "tags": {"en": "Damping coefficient (Invalid in type iOS)", "default": "4"}, "descWithTags": "阻尼系数（type 为 ios 失效）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "number"}}, "onRefresh": {"defaultValue": null, "description": "刷新触发事件\n@en Callback when refreshing", "name": "onRefresh", "tags": {"en": "Callback when refreshing"}, "descWithTags": "刷新触发事件", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "() => Promise<void>"}}, "useHideAsNestedScroll": {"defaultValue": {"value": "true"}, "description": "惯性滑动时隐藏展示元素\n@en Whether to hide the display element during inertial sliding", "name": "useHideAsNestedScroll", "tags": {"en": "Whether to hide the display element during inertial sliding", "default": "true"}, "descWithTags": "惯性滑动时隐藏展示元素", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "boolean"}}, "loosingMinHeight": {"defaultValue": {"value": "loosingText 的高度"}, "description": "释放可刷线的最小距离(px)\n@en Minimum distance to release brushable lines (px)\n@default_en loosingText Height", "name": "loosingMinHeight", "tags": {"en": "Minimum distance to release brushable lines (px)", "default": "loosingText 的高度", "default_en": "loosingText Height"}, "descWithTags": "释放可刷线的最小距离(px)", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "number"}}, "allowPullWhenNotTop": {"defaultValue": {"value": "false"}, "description": "未滚到顶部时向下滑动也允许触发下拉刷新，可能影响ios回弹动画效果\n@en Swiping down when not scrolling to the top also allows a pull-down refresh to be triggered, which may affect the ios bounce animation effect", "name": "allowPullWhenNotTop", "tags": {"en": "Swiping down when not scrolling to the top also allows a pull-down refresh to be triggered, which may affect the ios bounce animation effect", "default": "false"}, "descWithTags": "未滚到顶部时向下滑动也允许触发下拉刷新，可能影响ios回弹动画效果", "parent": {"fileName": "arcom-github/packages/arcodesign/components/pull-refresh/model.ts", "name": "PullRefreshBasicProps"}, "required": false, "type": {"name": "boolean"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<PullRefreshRef>"}}}, "deps": {"PullRefreshRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}, "refresh": {"name": "refresh", "required": true, "description": "主动触发刷新，执行完成动画后异步返回\n@en Actively trigger the refresh, and return asynchronously after the animation is completed", "defaultValue": null, "type": {"name": "() => Promise<void>"}, "tags": {"en": "Actively trigger the refresh, and return asynchronously after the animation is completed"}, "descWithTags": "主动触发刷新，执行完成动画后异步返回"}, "updateIOSHeight": {"name": "updateIOSHeight", "required": true, "description": "手动更新 IOS 容器自动高度\n@en Manually update IOS container height", "defaultValue": null, "type": {"name": "() => void"}, "tags": {"en": "Manually update IOS container height"}, "descWithTags": "手动更新 IOS 容器自动高度"}}}, "depComps": {}, "typeNameInfo": {"props": "PullRefreshProps", "ref": "PullRefreshRef"}, "isDefaultExport": true}