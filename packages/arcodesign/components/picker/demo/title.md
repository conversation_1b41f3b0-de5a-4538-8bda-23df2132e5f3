## 标题样式 @en{Title Style}

#### 2

```js
import { Picker, Cell } from '@arco-design/mobile-react';

export default function PickerDemo() {
    const [visible, setVisible] = React.useState(false);
    const pickerRef = React.useRef();

    const data = [{
        label: 'Beijing',
        value: 'Beijing',
        children: [{
            label: 'Beijing',
            value: 'Beijing',
        }]
    }
    , {
        label: 'Liaoning',
        value: 'Liaoning',
        children: [{
            label: 'Shenyang',
            value: 'Shenyang',
            children: [{
                    label: '<PERSON>he',
                    value: '<PERSON><PERSON>'
                },
                {
                    label: 'Hunnan',
                    value: 'Hunnan'
                },
                {
                    label: 'Shenbei New',
                    value: 'Shenbei New'
                }
            ]
        }, {
            label: '<PERSON><PERSON>',
            value: '<PERSON><PERSON>',
            children: [{
                    label: '<PERSON><PERSON>',
                    value: '<PERSON><PERSON>'
                },
                {
                    label: 'Dongming',
                    value: 'Dongming'
                },
                {
                    label: 'Huanren',
                    value: 'Huanren'
                }
            ]
        }]
    }, {
        label: 'Yunnan',
        value: 'Yunnan',
        children: [{
            label: 'Kunming',
            value: 'Kun<PERSON>',
            children: [{
                label: 'Wuhua',
                value: 'Wuhua'
            }, {
                label: '<PERSON><PERSON><PERSON>',
                value: '<PERSON>uan<PERSON>'
            }, {
                label: 'Chenggong',
                value: 'Cheng<PERSON>'
            }]
        }]
    },
    ];


    return (
        <>
            <Cell.Group bordered={false}>
                <Cell
                    label="Title style"
                    showArrow
                    onClick={() => {setVisible(true);}}
                />
            </Cell.Group>
            <Picker
                ref={pickerRef}
                title="Select region"
                visible={visible}
                cascade={true}
                data={data}
                maskClosable={true}
                onHide={() => {
                    setVisible(false);
                }}
                onOk={(value, data) => {
                    console.log('on ok', value, data);
                }}
                onPickerChange={() => {
                    if (pickerRef.current) {
                        console.info('-----demo getAllColumnValues', pickerRef.current.getAllColumnValues());
                    }
                }}
                value={[]}
                cols={3}
                needBottomOffset={true}
            />
        </>
    );
}
```
