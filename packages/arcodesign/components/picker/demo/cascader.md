## 级联选择 @en{Cascade selection}

#### 3

```js
import { Picker, Cell } from '@arco-design/mobile-react';

export default function PickerDemo() {
    const [visible, setVisible] = React.useState(false);
    const [pickerValue, setPickerValue] = React.useState(['Beijing', 'Beijing']);
    const pickerRef = React.useRef();

    const data = [{
        label: 'Beijing',
        value: 'Beijing',
        children: [{
            label: 'Beijing',
            value: 'Beijing',
        }]
    }
    , {
        label: 'Liaoning',
        value: 'Liaoning',
        children: [{
            label: 'Shenyang',
            value: 'Shenyang',
            children: [{
                    label: '<PERSON><PERSON>',
                    value: '<PERSON><PERSON>'
                },
                {
                    label: 'Hunnan',
                    value: 'Hunnan'
                },
                {
                    label: 'Shen<PERSON>',
                    value: 'Shenbei'
                }
            ]
        }, {
            label: '<PERSON><PERSON>',
            value: 'Ben<PERSON>',
            children: [{
                    label: '<PERSON><PERSON>',
                    value: 'Xi<PERSON>'
                },
                {
                    label: 'Dongming',
                    value: '<PERSON><PERSON>'
                },
                {
                    label: '<PERSON><PERSON><PERSON>',
                    value: '<PERSON><PERSON><PERSON>'
                }
            ]
        }]
    }, {
        label: 'Yunnan',
        value: 'Yunnan',
        children: [{
            label: 'Kunming',
            value: '<PERSON>n<PERSON>',
            children: [{
                label: 'Wuhua',
                value: 'Wuhua'
            }, {
                label: '<PERSON>uan<PERSON>',
                value: 'Guandu'
            }, {
                label: 'Chenggong',
                value: 'Chenggong'
            }]
        }]
    },
    ];


    return (
        <>
            <Cell.Group bordered={false}>
                <Cell
                    label="Choose a location"
                    showArrow
                    onClick={() => {setVisible(true);}}
                >{pickerValue.join('-')}</Cell>
            </Cell.Group>
            <Picker
                ref={pickerRef}
                visible={visible}
                cascade={true}
                data={data}
                maskClosable={true}
                hideEmptyCols={true}
                onHide={() => {
                    console.log('------cell status', pickerRef.current.getCellMovingStatus());
                    setVisible(false);
                }}
                onOk={(value, data) => {
                    console.log('on ok', value, data);
                    setPickerValue(value)
                }}
                onPickerChange={() => {
                    if (pickerRef.current) {
                        console.info('-----demo getAllColumnValues', pickerRef.current.getAllColumnValues());
                    }
                }}
                value={pickerValue}
                cols={3}
                needBottomOffset={true}
            />
        </>
    );
}
```
