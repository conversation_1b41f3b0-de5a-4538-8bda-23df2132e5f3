{"description": "选择器组件，形式是弹起的浮层。", "descriptionTags": {"en": "The selector component, in the form of a popup layer.", "type": "数据录入", "type_en": "Data Entry", "name": "选择器", "name_en": "Picker", "displayName": "Picker"}, "displayName": "Picker", "methods": [], "props": {"data": {"defaultValue": null, "description": "数据源，非级联时data数组的长度决定picker列数，级联时以cols决定 pick列数\n@en Data source, the length of the data list determines the number of picker columns when not cascading, and is determined by cols when cascading", "name": "data", "tags": {"en": "Data source, the length of the data list determines the number of picker columns when not cascading, and is determined by cols when cascading"}, "descWithTags": "数据源，非级联时data数组的长度决定picker列数，级联时以cols决定 pick列数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": true, "type": {"name": "PickerData[] | PickerData[][] | ValueType[][]"}}, "cascade": {"defaultValue": {"value": "true"}, "description": "是否联动\n@en Whether to cascade", "name": "cascade", "tags": {"en": "Whether to cascade", "default": "true"}, "descWithTags": "是否联动", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "boolean"}}, "cols": {"defaultValue": {"value": "3"}, "description": "列数(最大为5；cascade=true时才使用)\n@en Number of columns (maximum 5; only used when cascade=true)", "name": "cols", "tags": {"en": "Number of columns (maximum 5; only used when cascade=true)", "default": "3"}, "descWithTags": "列数(最大为5；cascade=true时才使用)", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "number"}}, "rows": {"defaultValue": {"value": "5"}, "description": "行数(一列可选项的行数)，必须是奇数，最小为3个\n@en The number of rows (the number of rows in a column of optional items), must be an odd number, the minimum is 3", "name": "rows", "tags": {"en": "The number of rows (the number of rows in a column of optional items), must be an odd number, the minimum is 3", "default": "5"}, "descWithTags": "行数(一列可选项的行数)，必须是奇数，最小为3个", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "number"}}, "disabled": {"defaultValue": {"value": "false"}, "description": "是否不可用\n@en Whether t be disabled", "name": "disabled", "tags": {"en": "Whether t be disabled", "default": "false"}, "descWithTags": "是否不可用", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "boolean"}}, "value": {"defaultValue": null, "description": "值, 格式是[value1, value2, value3], 对应数据源的相应级层value，如果不传默认选每一列的第一个值\n@en Value, the format is [value1, value2, value3], corresponding to the corresponding level value of the data source, if not passed, the first value of each column is selected by default", "name": "value", "tags": {"en": "Value, the format is [value1, value2, value3], corresponding to the corresponding level value of the data source, if not passed, the first value of each column is selected by default"}, "descWithTags": "值, 格式是[value1, value2, value3], 对应数据源的相应级层value，如果不传默认选每一列的第一个值", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "ValueType[]"}}, "onHide": {"defaultValue": null, "description": "点击遮罩层或取消、确定按钮的隐藏回调\n@en Callback for clicking on mask layer or cancel button, OK button", "name": "onHide", "tags": {"en": "Callback for clicking on mask layer or cancel button, OK button"}, "descWithTags": "点击遮罩层或取消、确定按钮的隐藏回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "(scene?: string) => void"}}, "onChange": {"defaultValue": null, "description": "选中后的回调\n@en Callback after selection", "name": "onChange", "tags": {"en": "Callback after selection"}, "descWithTags": "选中后的回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "(selectedValue: ValueType[]) => void"}}, "onPickerChange": {"defaultValue": null, "description": "每列数据选择变化后的回调函数\n@en The callback after each column data selection changes", "name": "onPickerChange", "tags": {"en": "The callback after each column data selection changes"}, "descWithTags": "每列数据选择变化后的回调函数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "(value: ValueType[], index: number, data: PickerData[]) => void"}}, "itemStyle": {"defaultValue": null, "description": "每列样式\n@en Stylesheet per column", "name": "itemStyle", "tags": {"en": "Stylesheet per column"}, "descWithTags": "每列样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "CSSProperties"}}, "visible": {"defaultValue": {"value": "false"}, "description": "是否展示选择器\n@en whether to show the picker", "name": "visible", "tags": {"en": "whether to show the picker", "default": "false"}, "descWithTags": "是否展示选择器", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "boolean"}}, "okText": {"defaultValue": {"value": "\"确定\""}, "description": "弹窗确认已选值的文案\n@en Text of confirmed selected value of the popup\n@default_en \"OK\"", "name": "okText", "tags": {"en": "Text of confirmed selected value of the popup", "default": "\"确定\"", "default_en": "\"OK\""}, "descWithTags": "弹窗确认已选值的文案", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "string"}}, "dismissText": {"defaultValue": {"value": "\"取消\""}, "description": "弹窗取消的文案\n@en Popup canceled text\n@default_en \"Cancel\"", "name": "dismissText", "tags": {"en": "<PERSON><PERSON> canceled text", "default": "\"取消\"", "default_en": "\"Cancel\""}, "descWithTags": "弹窗取消的文案", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "string"}}, "onOk": {"defaultValue": null, "description": "点击选中时执行的回调\n@en Callback when clicking on Ok", "name": "onOk", "tags": {"en": "Callback when clicking on Ok"}, "descWithTags": "点击选中时执行的回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "(value: ValueType[], data: PickerData[]) => void"}}, "onDismiss": {"defaultValue": null, "description": "点击取消时执行的回调\n@en Callback when clicking to cancel", "name": "on<PERSON><PERSON><PERSON>", "tags": {"en": "Callback when clicking to cancel"}, "descWithTags": "点击取消时执行的回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "() => void"}}, "clickable": {"defaultValue": {"value": "true"}, "description": "是否可通过点击操作选择内容\n@en Whether content can be selected by clicking", "name": "clickable", "tags": {"en": "Whether content can be selected by clicking", "default": "true"}, "descWithTags": "是否可通过点击操作选择内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "boolean"}}, "hideEmptyCols": {"defaultValue": {"value": "false"}, "description": "是否隐藏无数据的空列，常用于级联选择\n@en Whether to hide empty columns without data, often used for cascading selection", "name": "hideEmptyCols", "tags": {"en": "Whether to hide empty columns without data, often used for cascading selection", "default": "false"}, "descWithTags": "是否隐藏无数据的空列，常用于级联选择", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "boolean"}}, "title": {"defaultValue": {"value": "\"\""}, "description": "选择器标题\n@en Picker title", "name": "title", "tags": {"en": "Picker title", "default": "\"\""}, "descWithTags": "选择器标题", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "string"}}, "maskClosable": {"defaultValue": {"value": "false"}, "description": "点击蒙层是否关闭菜单\n@en Whether to click the mask to close the menu", "name": "maskClosable", "tags": {"en": "Whether to click the mask to close the menu", "default": "false"}, "descWithTags": "点击蒙层是否关闭菜单", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "boolean"}}, "touchToStop": {"defaultValue": {"value": "false"}, "description": "是否通过长按停止滑动，传入数字 x 表示触摸超过 x 毫秒算长按，传 true 表示 x=100，长按事件与 click 事件互斥\n@en Whether to stop sliding by long-pressing, inputing in the number x means that the touch exceeds x milliseconds to count as long-pressing, inputing true means that x=100, the long-press event and the click event are mutually exclusive", "name": "touchToStop", "tags": {"en": "Whether to stop sliding by long-pressing, inputing in the number x means that the touch exceeds x milliseconds to count as long-pressing, inputing true means that x=100, the long-press event and the click event are mutually exclusive", "default": "false"}, "descWithTags": "是否通过长按停止滑动，传入数字 x 表示触摸超过 x 毫秒算长按，传 true 表示 x=100，长按事件与 click 事件互斥", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "number | boolean"}}, "gestureOutOfControl": {"defaultValue": {"value": "true"}, "description": "是否禁用滚动容器手势判断，禁用后交给业务方自己判断\n@en Whether to disable the scrolling container gesture judgment, leave it to users to judge", "name": "gestureOutOfControl", "tags": {"en": "Whether to disable the scrolling container gesture judgment, leave it to users to judge", "default": "true"}, "descWithTags": "是否禁用滚动容器手势判断，禁用后交给业务方自己判断", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "boolean"}}, "renderLinkedContainer": {"defaultValue": null, "description": "将选择器的展现隐藏状态及选中值的展示与某个容器关联，传入后将同时渲染该容器和选择器组件，此时选择器组件的 visible 和 onHide 属性可不传，点击该容器会唤起选择器\n@en Associate the hidden state of the picker and the display of the selected value with a container. After passing it in, the container and the picker component will be rendered at the same time. At this time, the visible and onHide attributes of the picker component are optional values. Clicking the container will evoke the picker", "name": "renderLinkedContainer", "tags": {"en": "Associate the hidden state of the picker and the display of the selected value with a container. After passing it in, the container and the picker component will be rendered at the same time. At this time, the visible and onHide attributes of the picker component are optional values. Clicking the container will evoke the picker"}, "descWithTags": "将选择器的展现隐藏状态及选中值的展示与某个容器关联，传入后将同时渲染该容器和选择器组件，此时选择器组件的 visible 和 onHide 属性可不传，点击该容器会唤起选择器", "parent": {"fileName": "arcom-github/packages/arcodesign/components/picker/type.ts", "name": "PickerProps"}, "required": false, "type": {"name": "(value: ValueType[], data: PickerData[]) => ReactNode"}}, "needBottomOffset": {"defaultValue": {"value": "false"}, "description": "从底部滑出的菜单内容是否适配ipx底部\n@en Whether the content of the menu that slides out from the bottom fits the bottom of ipx", "name": "needBottomOffset", "tags": {"en": "Whether the content of the menu that slides out from the bottom fits the bottom of ipx", "default": "false"}, "descWithTags": "从底部滑出的菜单内容是否适配ipx底部", "parent": {"fileName": "arcom-github/packages/arcodesign/components/popup/index.tsx", "name": "PopupProps"}, "required": false, "type": {"name": "boolean"}}, "translateZ": {"defaultValue": {"value": "false"}, "description": "[即将废弃] 开启translateZ强制提升\n@en [To be deprecated] Enable translateZ forced promotion\n@deprecated", "name": "translateZ", "tags": {"en": "[To be deprecated] Enable translateZ forced promotion", "default": "false", "deprecated": ""}, "descWithTags": "[即将废弃] 开启translateZ强制提升", "parent": {"fileName": "arcom-github/packages/arcodesign/components/popup/index.tsx", "name": "PopupProps"}, "required": false, "type": {"name": "boolean"}}, "maskTransitionTimeout": {"defaultValue": {"value": "{ enter: 450, exit: 240 }"}, "description": "菜单蒙层动画时长\n@en Menu mask animation duration", "name": "maskTransitionTimeout", "tags": {"en": "Menu mask animation duration", "default": "{ enter: 450, exit: 240 }"}, "descWithTags": "菜单蒙层动画时长", "parent": {"fileName": "arcom-github/packages/arcodesign/components/popup/index.tsx", "name": "PopupProps"}, "required": false, "type": {"name": "number | { appear?: number; enter?: number; exit?: number; }"}}, "contentTransitionTimeout": {"defaultValue": {"value": "{ enter: 450, exit: 240 }"}, "description": "菜单内容动画时长\n@en Menu content animation duration", "name": "contentTransitionTimeout", "tags": {"en": "Menu content animation duration", "default": "{ enter: 450, exit: 240 }"}, "descWithTags": "菜单内容动画时长", "parent": {"fileName": "arcom-github/packages/arcodesign/components/popup/index.tsx", "name": "PopupProps"}, "required": false, "type": {"name": "number | { appear?: number; enter?: number; exit?: number; }"}}, "contentTransitionType": {"defaultValue": {"value": "\\`slide-from-${props.direction}\\`"}, "description": "内容过渡动画类名\n@en Content transition animation classname", "name": "contentTransitionType", "tags": {"en": "Content transition animation classname", "default": "\\`slide-from-${props.direction}\\`"}, "descWithTags": "内容过渡动画类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/popup/index.tsx", "name": "PopupProps"}, "required": false, "type": {"name": "string"}}, "className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "maskClass": {"defaultValue": null, "description": "自定义蒙层类名\n@en Custom mask classname", "name": "maskClass", "tags": {"en": "Custom mask classname"}, "descWithTags": "自定义蒙层类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "maskStyle": {"defaultValue": null, "description": "自定义蒙层样式\n@en Custom mask stylesheet", "name": "maskStyle", "tags": {"en": "Custom mask stylesheet"}, "descWithTags": "自定义蒙层样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "CSSProperties"}}, "contentClass": {"defaultValue": null, "description": "自定义内容类名\n@en Custom content classname", "name": "contentClass", "tags": {"en": "Custom content classname"}, "descWithTags": "自定义内容类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "contentStyle": {"defaultValue": null, "description": "自定义内容样式\n@en Custom content stylesheet", "name": "contentStyle", "tags": {"en": "Custom content stylesheet"}, "descWithTags": "自定义内容样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "CSSProperties"}}, "maskTransitionType": {"defaultValue": {"value": "\"fade\""}, "description": "蒙层过渡动画类名\n@en Mask transition animation classname", "name": "maskTransitionType", "tags": {"en": "Mask transition animation classname", "default": "\"fade\""}, "descWithTags": "蒙层过渡动画类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "animatingClosable": {"defaultValue": {"value": "false"}, "description": "执行进场动画时点击蒙层是否可关闭菜单\n@en Whether the menu can be closed by clicking on the mask when performing the entry animation", "name": "animatingClosable", "tags": {"en": "Whether the menu can be closed by clicking on the mask when performing the entry animation", "default": "false"}, "descWithTags": "执行进场动画时点击蒙层是否可关闭菜单", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "mountOnEnter": {"defaultValue": {"value": "true"}, "description": "是否在打开菜单时再加载内容\n@en Whether to reload content when the menu is opened", "name": "mountOnEnter", "tags": {"en": "Whether to reload content when the menu is opened", "default": "true"}, "descWithTags": "是否在打开菜单时再加载内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "unmountOnExit": {"defaultValue": {"value": "true"}, "description": "是否在退出时卸载内容\n@en Whether to unmount content on exit", "name": "unmountOnExit", "tags": {"en": "Whether to unmount content on exit", "default": "true"}, "descWithTags": "是否在退出时卸载内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "preventBodyScroll": {"defaultValue": {"value": "true"}, "description": "弹窗打开时是否禁止body的滚动\n@en Whether to prohibit the scrolling of the body when the popup is opened", "name": "preventBodyScroll", "tags": {"en": "Whether to prohibit the scrolling of the body when the popup is opened", "default": "true"}, "descWithTags": "弹窗打开时是否禁止body的滚动", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "initialBodyOverflow": {"defaultValue": {"value": "第一个全屏组件（弹窗、toast等）打开时页面overflow值"}, "description": "页面初始 overflow 状态，即关闭弹窗时 overflow 应该还原的状态\n@en The initial overflow state of the page, that is, the state in which overflow should be restored when the popup is closed\n@default_en The page overflow value when the first fullscreen component (popup, toast, etc.) is opened", "name": "initialBodyOverflow", "tags": {"en": "The initial overflow state of the page, that is, the state in which overflow should be restored when the popup is closed", "default": "第一个全屏组件（弹窗、toast等）打开时页面overflow值", "default_en": "The page overflow value when the first fullscreen component (popup, toast, etc.) is opened"}, "descWithTags": "页面初始 overflow 状态，即关闭弹窗时 overflow 应该还原的状态", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "onClose": {"defaultValue": null, "description": "关闭后回调（动画执行完毕）\n@en Callback after closing (animation is completed)", "name": "onClose", "tags": {"en": "Callback after closing (animation is completed)"}, "descWithTags": "关闭后回调（动画执行完毕）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "(scene?: string) => void"}}, "onOpen": {"defaultValue": null, "description": "打开后回调（动画执行完毕）\n@en Callback after opening (animation is completed)", "name": "onOpen", "tags": {"en": "Callback after opening (animation is completed)"}, "descWithTags": "打开后回调（动画执行完毕）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "() => void"}}, "onMaskClick": {"defaultValue": null, "description": "点击蒙层回调，maskClosable=false时也会触发\n@en Callback when clicking the mask , also triggered when maskClosable=false", "name": "onMaskClick", "tags": {"en": "Callback when clicking the mask , also triggered when maskClosable=false"}, "descWithTags": "点击蒙层回调，maskClosable=false时也会触发", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "() => void"}}, "onTouchMove": {"defaultValue": null, "description": "弹窗的touchmove回调\n@en Touch event callbacks for masking", "name": "onTouchMove", "tags": {"en": "Touch event callbacks for masking"}, "descWithTags": "弹窗的touchmove回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "(e: <PERSON><PERSON><PERSON>, prevented: boolean, direction: \"x\" | \"y\") => void"}}, "onPreventTouchMove": {"defaultValue": null, "description": "非滚动区域或滚动到顶部及底部时的触摸事件回调\n@en Touch event callbacks for non-scrolling areas or when scrolling to the top and bottom", "name": "onPreventTouchMove", "tags": {"en": "Touch event callbacks for non-scrolling areas or when scrolling to the top and bottom"}, "descWithTags": "非滚动区域或滚动到顶部及底部时的触摸事件回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "(e: <PERSON><PERSON><PERSON>, direction: \"x\" | \"y\") => void"}}, "getContainer": {"defaultValue": null, "description": "获取挂载容器\n@en Get mounted container", "name": "getContainer", "tags": {"en": "Get mounted container"}, "descWithTags": "获取挂载容器", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "() => HTMLElement"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<PickerRef>"}}}, "deps": {"PickerData": {"value": {"name": "value", "required": true, "description": "每一列展示的每项文案对应的值\n@en The value for each item displayed in each column", "defaultValue": null, "type": {"name": "ValueType"}, "tags": {"en": "The value for each item displayed in each column"}, "descWithTags": "每一列展示的每项文案对应的值"}, "label": {"name": "label", "required": true, "description": "每一列展示的文案\n@en Text displayed in each column", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Text displayed in each column"}, "descWithTags": "每一列展示的文案"}, "children": {"name": "children", "required": false, "description": "级联状态下，该列对应的下一列数据\n@en In the cascade state, the next column of data corresponding to this column", "defaultValue": null, "type": {"name": "PickerData[]"}, "tags": {"en": "In the cascade state, the next column of data corresponding to this column"}, "descWithTags": "级联状态下，该列对应的下一列数据"}}, "ValueType": "string | number", "PickerRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}, "getCellMovingStatus": {"name": "getCellMovingStatus", "required": true, "description": "每一列的滑动状态\n@en Sliding state of each column", "defaultValue": null, "type": {"name": "() => PickerCellMovingStatus[]"}, "tags": {"en": "Sliding state of each column"}, "descWithTags": "每一列的滑动状态"}, "updateLayout": {"name": "updateLayout", "required": true, "description": "手动更新元素布局\n@en Manually update the element layout", "defaultValue": null, "type": {"name": "() => void"}, "tags": {"en": "Manually update the element layout"}, "descWithTags": "手动更新元素布局"}, "getAllColumnValues": {"name": "getAllColumnValues", "required": true, "description": "获取所有列的值\n@en Get all column values", "defaultValue": null, "type": {"name": "() => ValueType[]"}, "tags": {"en": "Get all column values"}, "descWithTags": "获取所有列的值"}, "getColumnValue": {"name": "getColumnValue", "required": true, "description": "获取第 n 列的值\n@en Get the value of the nth column", "defaultValue": null, "type": {"name": "(index: number) => ValueType"}, "tags": {"en": "Get the value of the nth column"}, "descWithTags": "获取第 n 列的值"}, "scrollToCurrentIndex": {"name": "scrollToCurrentIndex", "required": true, "description": "直接跳到当前最近一行（调用时将中断滚动）\n@en Jump directly to the current most recent line (will break scrolling when called)", "defaultValue": null, "type": {"name": "() => void"}, "tags": {"en": "Jump directly to the current most recent line (will break scrolling when called)"}, "descWithTags": "直接跳到当前最近一行（调用时将中断滚动）"}}, "PickerCellMovingStatus": "\"normal\" | \"moving\" | \"scrolling\""}, "depComps": {}, "typeNameInfo": {"props": "PickerProps", "ref": "Picker<PERSON><PERSON>"}, "isDefaultExport": true}