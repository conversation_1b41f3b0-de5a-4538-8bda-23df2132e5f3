{"description": "图片预览组件，支持循环轮播、双指/双击缩放、缩略图放大效果。", "descriptionTags": {"en": "The image preview, supports circular rotation, two-finger/double-tap zoom, and thumbnail zoom effects.", "type": "信息展示", "type_en": "Data Display", "name": "图片预览", "name_en": "ImagePreview"}, "displayName": "ImagePreview", "methods": [{"description": "打开图片预览", "docblock": "打开图片预览\n@desc {en} Open image preview\n@param config configuration\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "configuration", "type": "ImagePreviewProps", "optional": false}], "return": {"name": "", "description": "", "type": "{ close: () => void; update: (newConfig: ImagePreviewProps) => void; }", "optional": false}, "desc": {"en": "Open image preview"}}, "modifiers": [], "name": "open", "params": [{"description": null, "name": "config", "type": {"name": "Pick<WithGlobalContext<ImagePreviewProps & RefAttributes<ImagePreviewRef>>, \"animateDurationSlide\" | ... 46 more ... | \"context\">"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}], "props": {"style": {"defaultValue": null, "description": "自定义样式\n@en Custom stylesheet", "name": "style", "tags": {"en": "Custom stylesheet"}, "descWithTags": "自定义样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "CSSProperties"}}, "className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "string"}}, "images": {"defaultValue": null, "description": "图片信息数组\n@en Image List", "name": "images", "tags": {"en": "Image List"}, "descWithTags": "图片信息数组", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": true, "type": {"name": "PreviewImageProps[]"}}, "openIndex": {"defaultValue": null, "description": "打开时展示的index，在images范围内会展示弹窗，否则隐藏弹窗\n@en The index displayed when it is opened, the popup will be displayed within the scope of images, otherwise the popup will be hidden", "name": "openIndex", "tags": {"en": "The index displayed when it is opened, the popup will be displayed within the scope of images, otherwise the popup will be hidden"}, "descWithTags": "打开时展示的index，在images范围内会展示弹窗，否则隐藏弹窗", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": true, "type": {"name": "number"}}, "loop": {"defaultValue": {"value": "false"}, "description": "是否可循环滑动\n@en Whether it can be swiped circularly", "name": "loop", "tags": {"en": "Whether it can be swiped circularly", "default": "false"}, "descWithTags": "是否可循环滑动", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "boolean"}}, "fit": {"defaultValue": {"value": "\"preview-y\""}, "description": "图片布局方式，preview-y为宽度撑满高度溢出滚动，preview-x为高度撑满宽度溢出滚动\n@en Image layout, preview-y is overflow scrolling with full width and height, preview-x is overflow scrolling with full width and height", "name": "fit", "tags": {"en": "Image layout, preview-y is overflow scrolling with full width and height, preview-x is overflow scrolling with full width and height", "default": "\"preview-y\""}, "descWithTags": "图片布局方式，preview-y为宽度撑满高度溢出滚动，preview-x为高度撑满宽度溢出滚动", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "enum", "raw": "\"preview-y\" | \"preview-x\"", "value": [{"value": "\"preview-y\""}, {"value": "\"preview-x\""}]}}, "displayDuration": {"defaultValue": {"value": "300"}, "description": "打开和关闭时的过渡动画\n@en Transition animation on opening and closing", "name": "displayDuration", "tags": {"en": "Transition animation on opening and closing", "default": "300"}, "descWithTags": "打开和关闭时的过渡动画", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "number"}}, "replaceFallbackWhenLoaded": {"defaultValue": null, "description": "是否在原图加载完成后将过渡图动画替换为原图动画\n@en Whether to replace the transition image animation with the original image animation after the original image is loaded", "name": "replaceFallbackWhenLoaded", "tags": {"en": "Whether to replace the transition image animation with the original image animation after the original image is loaded"}, "descWithTags": "是否在原图加载完成后将过渡图动画替换为原图动画", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "boolean"}}, "noselect": {"defaultValue": {"value": "true"}, "description": "图片不可选中，屏蔽系统默认事件\n@en The image cannot be selected, and the system default event is blocked", "name": "noselect", "tags": {"en": "The image cannot be selected, and the system default event is blocked", "default": "true"}, "descWithTags": "图片不可选中，屏蔽系统默认事件", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "boolean"}}, "spaceBetween": {"defaultValue": null, "description": "图片横向间距\n@en Horizontal spacing of images", "name": "spaceBetween", "tags": {"en": "Horizontal spacing of images"}, "descWithTags": "图片横向间距", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "number"}}, "loadingArea": {"defaultValue": null, "description": "自定义展示加载中内容\n@en Custom display loading content", "name": "loadingArea", "tags": {"en": "Custom display loading content"}, "descWithTags": "自定义展示加载中内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "ReactNode"}}, "errorArea": {"defaultValue": null, "description": "自定义展示加载失败内容\n@en Custom display loading failure content", "name": "errorArea", "tags": {"en": "Custom display loading failure content"}, "descWithTags": "自定义展示加载失败内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "ReactNode"}}, "showLoading": {"defaultValue": {"value": "true"}, "description": "是否展示图片加载中提示\n@en Whether to display the image loading prompt", "name": "showLoading", "tags": {"en": "Whether to display the image loading prompt", "default": "true"}, "descWithTags": "是否展示图片加载中提示", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "boolean"}}, "showError": {"defaultValue": {"value": "true"}, "description": "是否展示图片加载失败提示\n@en Whether to display the image loading failure prompt", "name": "showError", "tags": {"en": "Whether to display the image loading failure prompt", "default": "true"}, "descWithTags": "是否展示图片加载失败提示", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "boolean"}}, "retryTime": {"defaultValue": null, "description": "失败时自动重试次数\n@en Number of automatic retries on failure", "name": "retryTime", "tags": {"en": "Number of automatic retries on failure"}, "descWithTags": "失败时自动重试次数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "number"}}, "staticLabel": {"defaultValue": null, "description": "是否直接渲染<img>标签，不走加载图片流程\n@en Whether to render the <img> tag directly without going through the image loading process", "name": "staticLabel", "tags": {"en": "Whether to render the <img> tag directly without going through the image loading process"}, "descWithTags": "是否直接渲染<img>标签，不走加载图片流程", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "boolean"}}, "scrollBezier": {"defaultValue": null, "description": "长图滚动变化曲线\n@en Scrolling change curve of long image", "name": "scrollBezier", "tags": {"en": "Scrolling change curve of long image"}, "descWithTags": "长图滚动变化曲线", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "[number, number, number, number]"}}, "lazyloadCount": {"defaultValue": {"value": "1"}, "description": "只加载当前页相邻的n个内容，为0时会销毁所有相邻内容\n@en Only load n content adjacent to the current page, when it is 0, all adjacent content will be destroyed", "name": "lazyloadCount", "tags": {"en": "Only load n content adjacent to the current page, when it is 0, all adjacent content will be destroyed", "default": "1"}, "descWithTags": "只加载当前页相邻的n个内容，为0时会销毁所有相邻内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "number"}}, "swipeToClose": {"defaultValue": {"value": "true"}, "description": "当图片滚动到边缘时，继续滑动是否关闭预览\n@en When the image is scrolled to the edge, whether to close the preview when continuing to swipe", "name": "swipeToClose", "tags": {"en": "When the image is scrolled to the edge, whether to close the preview when continuing to swipe", "default": "true"}, "descWithTags": "当图片滚动到边缘时，继续滑动是否关闭预览", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "boolean"}}, "indicatorPos": {"defaultValue": {"value": "\"start\""}, "description": "轮播索引位置\n@en Carousel indicator position", "name": "indicatorPos", "tags": {"en": "Carousel indicator position", "default": "\"start\""}, "descWithTags": "轮播索引位置", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "enum", "raw": "\"start\" | \"center\" | \"end\"", "value": [{"value": "\"start\""}, {"value": "\"center\""}, {"value": "\"end\""}]}}, "extra": {"defaultValue": null, "description": "渲染自定义元素，如自定义关闭按钮\n@en Render custom elements such as custom close buttons", "name": "extra", "tags": {"en": "Render custom elements such as custom close buttons"}, "descWithTags": "渲染自定义元素，如自定义关闭按钮", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "ReactNode"}}, "getMinScale": {"defaultValue": null, "description": "图片捏合时最小缩放倍数，松手后仍会恢复到1的状态，默认为0.7\n@en The minimum zoom factor when the image is pinched, it will still return to the state of 1 after letting go, the default is 0.7", "name": "getMinScale", "tags": {"en": "The minimum zoom factor when the image is pinched, it will still return to the state of 1 after letting go, the default is 0.7"}, "descWithTags": "图片捏合时最小缩放倍数，松手后仍会恢复到1的状态，默认为0.7", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(image: HTMLImageElement, imageIndex: number) => number"}}, "getMaxScale": {"defaultValue": null, "description": "图片最大缩放倍数，默认根据图片尺寸调节\n@en The maximum zoom factor of the image, the default is adjusted according to the picture size", "name": "getMaxScale", "tags": {"en": "The maximum zoom factor of the image, the default is adjusted according to the picture size"}, "descWithTags": "图片最大缩放倍数，默认根据图片尺寸调节", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(image: HTMLImageElement, imageIndex: number) => number"}}, "getDoubleClickScale": {"defaultValue": null, "description": "当双击图片时，图片应缩放的倍数\n@en The zoom factor of the image when double-clicking the image", "name": "getDoubleClickScale", "tags": {"en": "The zoom factor of the image when double-clicking the image"}, "descWithTags": "当双击图片时，图片应缩放的倍数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(currentScale: number, maxScale: number, image: HTMLImageElement, imageIndex: number) => number"}}, "getContainer": {"defaultValue": null, "description": "获取挂载容器\n@en Get mounted container", "name": "getContainer", "tags": {"en": "Get mounted container"}, "descWithTags": "获取挂载容器", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "() => HTMLElement"}}, "renderIndicator": {"defaultValue": null, "description": "自定义索引内容\n@en Custom indicator content", "name": "renderIndicator", "tags": {"en": "Custom indicator content"}, "descWithTags": "自定义索引内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(currentIndex: number, total: number, lastIndex: number) => ReactNode"}}, "getThumbBounds": {"defaultValue": null, "description": "获取缩略图定位\n@en Get the thumbnail image Positioning", "name": "getThumbBounds", "tags": {"en": "Get the thumbnail image Positioning"}, "descWithTags": "获取缩略图定位", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(index: number) => ClientRect"}}, "onChange": {"defaultValue": null, "description": "索引发生改变时回调\n@en Callback when index changes", "name": "onChange", "tags": {"en": "Callback when index changes"}, "descWithTags": "索引发生改变时回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(index: number) => void"}}, "onAfterChange": {"defaultValue": null, "description": "索引切换，动画完成后触发\n@en Callback after animation is completed when the index toggles", "name": "onAfterChange", "tags": {"en": "Callback after animation is completed when the index toggles"}, "descWithTags": "索引切换，动画完成后触发", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(index: number) => void"}}, "close": {"defaultValue": null, "description": "关闭弹窗\n@en close popup", "name": "close", "tags": {"en": "close popup"}, "descWithTags": "关闭弹窗", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": true, "type": {"name": "(e: MouseEvent<HTMLDivElement, MouseEvent> | TouchEvent) => void"}}, "onClose": {"defaultValue": null, "description": "关闭弹窗回调（动画执行完成后）\n@en Callback when closing the popup (after the animation is completed)", "name": "onClose", "tags": {"en": "Callback when closing the popup (after the animation is completed)"}, "descWithTags": "关闭弹窗回调（动画执行完成后）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "() => void"}}, "onImageClick": {"defaultValue": null, "description": "点击图片回调，如果返回true则阻止关闭弹窗\n@en Callback when clicking the image, if it returns true, it will prevent the popup from closing", "name": "onImageClick", "tags": {"en": "Callback when clicking the image, if it returns true, it will prevent the popup from closing"}, "descWithTags": "点击图片回调，如果返回true则阻止关闭弹窗", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(index: number, e: MouseEvent<HTMLDivElement, MouseEvent>) => boolean | void"}}, "onImageDoubleClick": {"defaultValue": null, "description": "双击图片回调\n@en Callback when double clicking the image", "name": "onImageDoubleClick", "tags": {"en": "Callback when double clicking the image"}, "descWithTags": "双击图片回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(index: number, e: MouseEvent<HTMLDivElement, MouseEvent>) => void"}}, "onImageLongTap": {"defaultValue": null, "description": "长按图片回调\n@en Callback when long pressing image", "name": "onImageLongTap", "tags": {"en": "Callback when long pressing image"}, "descWithTags": "长按图片回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(index: number, image: HTMLImageElement, e: TouchEvent) => void"}}, "onTouchStart": {"defaultValue": null, "description": "弹窗内容touchstart事件\n@en Popup content touchstart event", "name": "onTouchStart", "tags": {"en": "Popup content touchstart event"}, "descWithTags": "弹窗内容touchstart事件", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(e: TouchEvent, index: number) => boolean | void"}}, "onTouchMove": {"defaultValue": null, "description": "弹窗内容touchmove事件\n@en Popup content touchmove event", "name": "onTouchMove", "tags": {"en": "Popup content touchmove event"}, "descWithTags": "弹窗内容touchmove事件", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(e: TouchEvent, index: number) => boolean | void"}}, "onTouchEnd": {"defaultValue": null, "description": "弹窗内容touchend / touchcancel事件\n@en Popup content touchend / touchcancel events", "name": "onTouchEnd", "tags": {"en": "Popup content touchend / touchcancel events"}, "descWithTags": "弹窗内容touchend / touchcancel事件", "parent": {"fileName": "arcom-github/packages/arcodesign/components/image-preview/index.tsx", "name": "ImagePreviewProps"}, "required": false, "type": {"name": "(e: TouchEvent, index: number) => boolean | void"}}, "animateDurationSlide": {"defaultValue": {"value": "300"}, "description": "手动切换轮播滑块时，当手指释放后，动画的执行时间(ms)\n@en Animation duration(ms) after the finger is released When manually switching the carousel slider", "name": "animateDurationSlide", "tags": {"en": "Animation duration(ms) after the finger is released When manually switching the carousel slider", "default": "300"}, "descWithTags": "手动切换轮播滑块时，当手指释放后，动画的执行时间(ms)", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "number"}}, "showIndicator": {"defaultValue": {"value": "true"}, "description": "是否展示轮播索引\n@en Whether to show the indicator", "name": "showIndicator", "tags": {"en": "Whether to show the indicator", "default": "true"}, "descWithTags": "是否展示轮播索引", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "boolean"}}, "hideSingleIndicator": {"defaultValue": {"value": "true"}, "description": "children 只有一个时隐藏轮播索引\n@en Whether to hide the indicator if just one child", "name": "hideSingleIndicator", "tags": {"en": "Whether to hide the indicator if just one child", "default": "true"}, "descWithTags": "children 只有一个时隐藏轮播索引", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "boolean"}}, "percentToChange": {"defaultValue": {"value": "0.3"}, "description": "滑动切换距离阈值(宽度比例)，范围为[0, 1]，如果该属性和`distanceToChange`属性均设置，则实际计算结果更大的生效\n@en Sliding switching distance threshold (width ratio), the range is [0, 1]. If the property and the `distanceToChange` property are both set, the actual calculation result will take effect with a larger value.", "name": "percentToChange", "tags": {"en": "Sliding switching distance threshold (width ratio), the range is [0, 1]. If the property and the `distanceToChange` property are both set, the actual calculation result will take effect with a larger value.", "default": "0.3"}, "descWithTags": "滑动切换距离阈值(宽度比例)，范围为[0, 1]，如果该属性和`distanceToChange`属性均设置，则实际计算结果更大的生效", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "number"}}, "distanceToChange": {"defaultValue": {"value": "10"}, "description": "滑动切换距离阈值(固定px宽度)，如果该属性和`percentToChange`属性均设置，则实际计算结果更大的生效\n@en Sliding switching distance threshold (fixed px width), if both this property and the `percentToChange` property are set, the actual calculation result will take effect with a larger one", "name": "distanceToChange", "tags": {"en": "Sliding switching distance threshold (fixed px width), if both this property and the `percentToChange` property are set, the actual calculation result will take effect with a larger one", "default": "10"}, "descWithTags": "滑动切换距离阈值(固定px宽度)，如果该属性和`percentToChange`属性均设置，则实际计算结果更大的生效", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "number"}}, "speedToChange": {"defaultValue": {"value": "200"}, "description": "滑动切换速度阈值(手指从按下到抬起之间的滑动速度，单位为px/s)，与滑动切换距离阈值同时设置时，满足其中一个即生效\n@en The sliding switching speed threshold (the sliding speed of the finger from pressing to lifting, in px/s), when it is set at the same time as the sliding switching distance threshold, it will take effect if one of them is satisfied.", "name": "speedToChange", "tags": {"en": "The sliding switching speed threshold (the sliding speed of the finger from pressing to lifting, in px/s), when it is set at the same time as the sliding switching distance threshold, it will take effect if one of them is satisfied.", "default": "200"}, "descWithTags": "滑动切换速度阈值(手指从按下到抬起之间的滑动速度，单位为px/s)，与滑动切换距离阈值同时设置时，满足其中一个即生效", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "number"}}, "swipeable": {"defaultValue": {"value": "true"}, "description": "是否响应手势滑动\n@en Whether to respond to gesture swipe", "name": "swipeable", "tags": {"en": "Whether to respond to gesture swipe", "default": "true"}, "descWithTags": "是否响应手势滑动", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "boolean"}}, "offsetBetween": {"defaultValue": {"value": "0"}, "description": "前后两端露出距离，设置值时不能循环轮播\n@en The exposed distance of the front and rear. When the value is set, the carousel cannot be rotated.", "name": "offsetBetween", "tags": {"en": "The exposed distance of the front and rear. When the value is set, the carousel cannot be rotated.", "default": "0"}, "descWithTags": "前后两端露出距离，设置值时不能循环轮播", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "number | { left?: number; right?: number; }"}}, "renderExtra": {"defaultValue": null, "description": "在轮播图内部渲染额外元素，该元素不随轮播滑动，但处于手指可交互热区\n@en Render an additional element inside the carousel, which does not slide with the carousel, but is in a finger-interactive hotspot", "name": "renderExtra", "tags": {"en": "Render an additional element inside the carousel, which does not slide with the carousel, but is in a finger-interactive hotspot"}, "descWithTags": "在轮播图内部渲染额外元素，该元素不随轮播滑动，但处于手指可交互热区", "parent": {"fileName": "arcom-github/packages/arcodesign/components/carousel/index.tsx", "name": "CarouselProps"}, "required": false, "type": {"name": "(currentIndex: number) => ReactNode"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<ImagePreviewRef>"}}}, "deps": {"PreviewImageProps": {"src": {"name": "src", "required": true, "description": "图片地址\n@en Image resource", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Image resource"}, "descWithTags": "图片地址"}, "fit": {"name": "fit", "required": false, "description": "图片布局方式，preview-y为宽度撑满高度溢出滚动，preview-x为高度撑满宽度溢出滚动\n@en Image layout, preview-y is overflow scrolling with full width and height, preview-x is overflow scrolling with full width and height", "defaultValue": null, "type": {"name": "enum", "raw": "\"preview-y\" | \"preview-x\"", "value": [{"value": "\"preview-y\""}, {"value": "\"preview-x\""}]}, "tags": {"en": "Image layout, preview-y is overflow scrolling with full width and height, preview-x is overflow scrolling with full width and height"}, "descWithTags": "图片布局方式，preview-y为宽度撑满高度溢出滚动，preview-x为高度撑满宽度溢出滚动"}, "fallbackSrc": {"name": "fallbackSrc", "required": false, "description": "过渡图url\n@en Transition image url", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Transition image url"}, "descWithTags": "过渡图url"}, "transitionEndDelay": {"name": "transitionEndDelay", "required": false, "description": "过渡图到原图放大动效完成后，移除过渡图的延迟时间(ms)，一般当原图过大时有调整需求\n@en After the transition image to the original image enlargement effect is completed, the delay time (ms) before the transition image is removed", "defaultValue": {"value": "30"}, "type": {"name": "number"}, "tags": {"en": "After the transition image to the original image enlargement effect is completed, the delay time (ms) before the transition image is removed", "default": "30"}, "descWithTags": "过渡图到原图放大动效完成后，移除过渡图的延迟时间(ms)，一般当原图过大时有调整需求"}, "thumbPosition": {"name": "thumbPosition", "required": false, "description": "缩略图填充方式（backgroundPosition），默认top center\n@en Thumbnail fill mode (backgroundPosition), default value is top center", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Thumbnail fill mode (backgroundPosition), default value is top center"}, "descWithTags": "缩略图填充方式（backgroundPosition），默认top center"}, "extraNode": {"name": "extraNode", "required": false, "description": "自定义DOM\n@en Custom dom", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Custom dom"}, "descWithTags": "自定义DOM"}}, "ImagePreviewRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}, "imageDoms": {"name": "imageDoms", "required": true, "description": "原生图片 DOM 列表\n@en Native image DOM array", "defaultValue": null, "type": {"name": "HTMLImageElement[]"}, "tags": {"en": "Native image DOM array"}, "descWithTags": "原生图片 DOM 列表"}}, "GlobalContextParams": {"prefixCls": {"name": "prefixCls", "required": false, "description": "组件类名前缀\n@en Component classname prefix", "defaultValue": {"value": "\"arco\""}, "type": {"name": "string"}, "tags": {"en": "Component classname prefix", "default": "\"arco\""}, "descWithTags": "组件类名前缀"}, "system": {"name": "system", "required": false, "description": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用\n@en Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "defaultValue": {"value": "\"\""}, "type": {"name": "enum", "raw": "\"\" | \"pc\" | \"android\" | \"ios\"", "value": [{"value": "\"\""}, {"value": "\"pc\""}, {"value": "\"android\""}, {"value": "\"ios\""}]}, "tags": {"en": "Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "default": "\"\""}, "descWithTags": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用"}, "useDarkMode": {"name": "useDarkMode", "required": false, "description": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式\n@en Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "default": "false"}, "descWithTags": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式"}, "isDarkMode": {"name": "isDarkMode", "required": false, "description": "是否处于暗黑模式，指定后以指定的值为准\n@en Whether it is in dark mode, the value shall prevail after being specified", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether it is in dark mode, the value shall prevail after being specified", "default": "false"}, "descWithTags": "是否处于暗黑模式，指定后以指定的值为准"}, "darkModeSelector": {"name": "darkModeSelector", "required": false, "description": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名\n@en When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "defaultValue": {"value": "\"arco-theme-dark\""}, "type": {"name": "string"}, "tags": {"en": "When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "default": "\"arco-theme-dark\""}, "descWithTags": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名"}, "theme": {"name": "theme", "required": false, "description": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1\n@en Theme variable. The css variable will be replaced online after input. The less variable needs to be set\n@use-css-vars : 1", "defaultValue": null, "type": {"name": "Record<string, string>"}, "tags": {"en": "Theme variable. The css variable will be replaced online after input. The less variable needs to be set", "use-css-vars": ": 1"}, "descWithTags": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1"}, "locale": {"name": "locale", "required": false, "description": "国际化语言包配置\n@en Internationalized language configuration", "defaultValue": null, "type": {"name": "ILocale"}, "tags": {"en": "Internationalized language configuration"}, "descWithTags": "国际化语言包配置"}, "useRtl": {"name": "useRtl", "required": false, "description": "是否使用Rtl模式\n@en Whether to use rtl", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to use rtl", "default": "false"}, "descWithTags": "是否使用Rtl模式"}, "onDarkModeChange": {"name": "onDarkModeChange", "required": false, "description": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效\n@en Triggered when the system's native dark mode changes, valid when useDarkMode=true", "defaultValue": null, "type": {"name": "(isDark: boolean) => void"}, "tags": {"en": "Triggered when the system's native dark mode changes, valid when useDarkMode=true"}, "descWithTags": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效"}}, "ILocale": {"locale": {"name": "locale", "required": true, "description": "语言类型\n@en Language Type", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Language Type"}, "descWithTags": "语言类型"}, "LoadMore": {"name": "LoadMore", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadMoreText: string; loadingText: string; prepareText: string; noDataText: string; failLoadText: string; prepareScrollText: string; prepareClickText: string; }"}, "tags": {}, "descWithTags": ""}, "Picker": {"name": "Picker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "Tag": {"name": "Tag", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ addTag: string; }"}, "tags": {}, "descWithTags": ""}, "Dialog": {"name": "Dialog", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "SwipeLoad": {"name": "SwipeLoad", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ normalText: string; activeText: string; }"}, "tags": {}, "descWithTags": ""}, "PullRefresh": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadingText: string; pullingText: string; finishText: string; loosingText: string; }"}, "tags": {}, "descWithTags": ""}, "DropdownMenu": {"name": "DropdownMenu", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ select: string; }"}, "tags": {}, "descWithTags": ""}, "Pagination": {"name": "Pagination", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ previousPage: string; nextPage: string; }"}, "tags": {}, "descWithTags": ""}, "Image": {"name": "Image", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "ImagePicker": {"name": "ImagePicker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "SearchBar": {"name": "SearchBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ placeholder: string; cancelBtn: string; }"}, "tags": {}, "descWithTags": ""}, "Stepper": {"name": "Stepper", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ minusButtonName: string; addButtonName: string; }"}, "tags": {}, "descWithTags": ""}, "Keyboard": {"name": "Keyboard", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ confirm: string; }"}, "tags": {}, "descWithTags": ""}, "Form": {"name": "Form", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ required: string; type: { email: string; url: string; string: string; number: string; array: string; object: string; boolean: string; }; number: { min: string; max: string; equal: string; range: string; positive: string; negative: string; }; ... 4 more ...; pickerDefaultHint: string; }"}, "tags": {}, "descWithTags": ""}, "NavBar": {"name": "NavBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ backBtnAriaLabel: string; }"}, "tags": {}, "descWithTags": ""}}}, "depComps": {}, "typeNameInfo": {"props": "ImagePreviewProps", "ref": "ImagePreviewRef"}, "isDefaultExport": true}