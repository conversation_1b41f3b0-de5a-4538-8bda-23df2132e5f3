// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`loading demo test loading demo: custom-color.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="loading-demo-basic"
  >
    <div
      class="arco-loading all-border-box arc"
      style="animation-duration: 1000ms; width: 20px; height: 20px;"
    >
      <svg
        viewBox="0 0 20 20"
      >
        <circle
          class="arc-bg"
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke-width="2"
        />
        <circle
          class="arc-line stroke-color-with-config"
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke-dasharray="14.137166941154069 42.411500823462205"
          stroke-dashoffset="14.137166941154069"
          stroke-linecap="round"
          stroke-width="2"
          style="stroke: #ff5722;"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box circle"
      style="animation-duration: 1000ms; width: 20px; height: 20px;"
    >
      <svg
        viewBox="0 0 20 20"
      >
        <defs>
          <lineargradient
            id="grad1-inner-1"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
              style="stop-color: rgb(255, 87, 34);"
            />
            <stop
              class="loading-circle-start stop-color-with-config"
              offset="100%"
              style="stop-color: rgb(255, 87, 34);"
            />
          </lineargradient>
          <lineargradient
            id="grad2-inner-1"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
              style="stop-color: rgb(255, 87, 34);"
            />
            <stop
              class="loading-circle-end stop-color-with-config"
              offset="100%"
              style="stop-color: rgb(255, 87, 34);"
            />
          </lineargradient>
        </defs>
        <circle
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke="url(#grad1-inner-1)"
          stroke-dasharray="28.274333882308138"
          stroke-dashoffset="28.274333882308138"
          stroke-width="2"
        />
        <circle
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke="url(#grad2-inner-1)"
          stroke-dasharray="28.274333882308138"
          stroke-width="2"
        />
        <circle
          class="loading-circle-filleted fill-color-with-config"
          cx="19"
          cy="10"
          r="1"
          style="fill: #ff5722;"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box spin"
      style="animation-duration: 1000ms;"
    >
      <span
        class="spin-cell"
        style="opacity: 1; transform: rotate(0turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0; transform: rotate(0.125turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.1; transform: rotate(0.25turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.25; transform: rotate(0.375turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.4; transform: rotate(0.5turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.55; transform: rotate(0.625turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.7; transform: rotate(0.75turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.85; transform: rotate(0.875turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="background-color: rgb(255, 87, 34); border-radius: 2px;"
        />
      </span>
    </div>
    <div
      class="arco-loading all-border-box dot"
      style="animation-duration: 1000ms;"
    >
      <span
        class="dot-cell bg-color-with-config filleted"
        style="opacity: 0.1; background-color: rgb(96, 106, 120);"
      />
      <span
        class="dot-cell bg-color-with-config filleted"
        style="opacity: 0.3; background-color: rgb(96, 106, 120);"
      />
      <span
        class="dot-cell bg-color-with-config filleted"
        style="opacity: 0.5; background-color: rgb(96, 106, 120);"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`loading demo test loading demo: custom-size.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="loading-demo-basic"
  >
    <div
      class="arco-loading all-border-box arc"
      style="animation-duration: 1000ms; width: 16px; height: 16px;"
    >
      <svg
        viewBox="0 0 16 16"
      >
        <circle
          class="arc-bg"
          cx="8"
          cy="8"
          fill="none"
          r="7"
          stroke-width="2"
        />
        <circle
          class="arc-line stroke-color-with-config"
          cx="8"
          cy="8"
          fill="none"
          r="7"
          stroke-dasharray="10.995574287564276 32.98672286269283"
          stroke-dashoffset="10.995574287564276"
          stroke-linecap="round"
          stroke-width="2"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box circle"
      style="animation-duration: 1000ms; width: 31px; height: 31px;"
    >
      <svg
        viewBox="0 0 31 31"
      >
        <defs>
          <lineargradient
            id="grad1-inner-5"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
            />
            <stop
              class="loading-circle-start stop-color-with-config"
              offset="100%"
            />
          </lineargradient>
          <lineargradient
            id="grad2-inner-5"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
            />
            <stop
              class="loading-circle-end stop-color-with-config"
              offset="100%"
            />
          </lineargradient>
        </defs>
        <circle
          cx="15.5"
          cy="15.5"
          fill="none"
          r="15"
          stroke="url(#grad1-inner-5)"
          stroke-dasharray="47.12388980384689"
          stroke-dashoffset="47.12388980384689"
          stroke-width="1"
        />
        <circle
          cx="15.5"
          cy="15.5"
          fill="none"
          r="15"
          stroke="url(#grad2-inner-5)"
          stroke-dasharray="47.12388980384689"
          stroke-width="1"
        />
        <circle
          class="loading-circle-filleted fill-color-with-config"
          cx="30.5"
          cy="15.5"
          r="0.5"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box spin"
      style="animation-duration: 1000ms;"
    >
      <span
        class="spin-cell"
        style="opacity: 1; transform: rotate(0turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.1; transform: rotate(0.125turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.2286; transform: rotate(0.25turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.3572; transform: rotate(0.375turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.4858; transform: rotate(0.5turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.6144; transform: rotate(0.625turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.743; transform: rotate(0.75turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.8716; transform: rotate(0.875turn); width: 1px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 1px;"
        />
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`loading demo test loading demo: filleted.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="loading-demo-basic"
  >
    <div
      class="arco-loading all-border-box arc"
      style="animation-duration: 1000ms; width: 20px; height: 20px;"
    >
      <svg
        viewBox="0 0 20 20"
      >
        <circle
          class="arc-bg"
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke-width="2"
        />
        <circle
          class="arc-line stroke-color-with-config"
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke-dasharray="14.137166941154069 42.411500823462205"
          stroke-dashoffset="14.137166941154069"
          stroke-width="2"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box circle"
      style="animation-duration: 1000ms; width: 20px; height: 20px;"
    >
      <svg
        viewBox="0 0 20 20"
      >
        <defs>
          <lineargradient
            id="grad1-inner-8"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
            />
            <stop
              class="loading-circle-start stop-color-with-config"
              offset="100%"
            />
          </lineargradient>
          <lineargradient
            id="grad2-inner-8"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
            />
            <stop
              class="loading-circle-end stop-color-with-config"
              offset="100%"
            />
          </lineargradient>
        </defs>
        <circle
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke="url(#grad1-inner-8)"
          stroke-dasharray="28.274333882308138"
          stroke-dashoffset="28.274333882308138"
          stroke-width="2"
        />
        <circle
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke="url(#grad2-inner-8)"
          stroke-dasharray="28.274333882308138"
          stroke-width="2"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box spin"
      style="animation-duration: 1000ms;"
    >
      <span
        class="spin-cell"
        style="opacity: 1; transform: rotate(0turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.1; transform: rotate(0.125turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.2286; transform: rotate(0.25turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.3572; transform: rotate(0.375turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.4858; transform: rotate(0.5turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.6144; transform: rotate(0.625turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.743; transform: rotate(0.75turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.8716; transform: rotate(0.875turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
        />
      </span>
    </div>
    <div
      class="arco-loading all-border-box dot"
      style="animation-duration: 1000ms;"
    >
      <span
        class="dot-cell bg-color-with-config"
        style="opacity: 0.2;"
      />
      <span
        class="dot-cell bg-color-with-config"
        style="opacity: 0.6;"
      />
      <span
        class="dot-cell bg-color-with-config"
        style="opacity: 1;"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`loading demo test loading demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="loading-demo-basic"
  >
    <div
      class="arco-loading all-border-box arc"
      style="animation-duration: 1000ms; width: 20px; height: 20px;"
    >
      <svg
        viewBox="0 0 20 20"
      >
        <circle
          class="arc-bg"
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke-width="2"
        />
        <circle
          class="arc-line stroke-color-with-config"
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke-dasharray="14.137166941154069 42.411500823462205"
          stroke-dashoffset="14.137166941154069"
          stroke-linecap="round"
          stroke-width="2"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box circle"
      style="animation-duration: 1000ms; width: 20px; height: 20px;"
    >
      <svg
        viewBox="0 0 20 20"
      >
        <defs>
          <lineargradient
            id="grad1-inner-12"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
            />
            <stop
              class="loading-circle-start stop-color-with-config"
              offset="100%"
            />
          </lineargradient>
          <lineargradient
            id="grad2-inner-12"
            x1="0%"
            x2="100%"
            y1="0%"
            y2="0%"
          >
            <stop
              class="loading-circle-middle stop-color-with-config"
              offset="0%"
            />
            <stop
              class="loading-circle-end stop-color-with-config"
              offset="100%"
            />
          </lineargradient>
        </defs>
        <circle
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke="url(#grad1-inner-12)"
          stroke-dasharray="28.274333882308138"
          stroke-dashoffset="28.274333882308138"
          stroke-width="2"
        />
        <circle
          cx="10"
          cy="10"
          fill="none"
          r="9"
          stroke="url(#grad2-inner-12)"
          stroke-dasharray="28.274333882308138"
          stroke-width="2"
        />
        <circle
          class="loading-circle-filleted fill-color-with-config"
          cx="19"
          cy="10"
          r="1"
        />
      </svg>
    </div>
    <div
      class="arco-loading all-border-box spin"
      style="animation-duration: 1000ms;"
    >
      <span
        class="spin-cell"
        style="opacity: 1; transform: rotate(0turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.1; transform: rotate(0.125turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.2286; transform: rotate(0.25turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.3572; transform: rotate(0.375turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.4858; transform: rotate(0.5turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.6144; transform: rotate(0.625turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.743; transform: rotate(0.75turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
      <span
        class="spin-cell"
        style="opacity: 0.8716; transform: rotate(0.875turn); width: 2px;"
      >
        <span
          class="spin-cell-inner bg-color-with-config"
          style="border-radius: 2px;"
        />
      </span>
    </div>
    <div
      class="arco-loading all-border-box dot"
      style="animation-duration: 1000ms;"
    >
      <span
        class="dot-cell bg-color-with-config filleted"
        style="opacity: 0.2;"
      />
      <span
        class="dot-cell bg-color-with-config filleted"
        style="opacity: 0.6;"
      />
      <span
        class="dot-cell bg-color-with-config filleted"
        style="opacity: 1;"
      />
    </div>
  </div>
</DocumentFragment>
`;
