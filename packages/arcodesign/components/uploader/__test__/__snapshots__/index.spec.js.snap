// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`uploader demo test uploader demo: customize-button.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-uploader"
  >
    <div
      class="arco-uploader-container"
    >
      <div
        class="arco-uploader-add"
      >
        <input
          type="file"
        />
        <a
          class="upload-button"
        >
          点击上传
          <svg
            class="arco-icon arco-icon-upload upload-button-icon"
            fill="currentColor"
            height="1em"
            viewBox="0 0 14 14"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M7,1.5l3.1,3.1L9.2,5.4L7.6,3.7v5.9H6.4V3.7L4.8,5.4L3.9,4.6L7,1.5z M2.9,11.4v-1.2H1.8v2.3h10.5	v-2.3h-1.2v1.2H2.9z"
              fill-rule="evenodd"
            />
          </svg>
        </a>
      </div>
      <div
        class="arco-uploader-list"
      >
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div
                class="arco-uploader-list-item-loaded"
              >
                <svg
                  class="arco-icon arco-icon-check "
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 20 20"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M16.702 4.47a.5.5 0 00-.705.06L8.33 13.596 3.82 9.724a.5.5 0 00-.705.054l-.652.758a.5.5 0 00.054.706L7.361 15.4a.5.5 0 00.054.053l.526.445.22.188a.5.5 0 00.722-.047l8.641-10.218a.5.5 0 00-.059-.705l-.763-.645z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`uploader demo test uploader demo: customize-icon1.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-uploader"
  >
    <div
      class="arco-uploader-container"
    >
      <div
        class="arco-uploader-add"
      >
        <input
          type="file"
        />
        <button
          class="arco-button arco-button-type-primary type-primary arco-button-size-medium arco-button-size-medium-is-semi size-medium arco-uploader-add-button is-semi pc"
          type="button"
        >
          <div
            class="arco-button-icon btn-icon"
          >
            <svg
              class="arco-icon arco-icon-upload "
              fill="currentColor"
              height="1em"
              viewBox="0 0 14 14"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7,1.5l3.1,3.1L9.2,5.4L7.6,3.7v5.9H6.4V3.7L4.8,5.4L3.9,4.6L7,1.5z M2.9,11.4v-1.2H1.8v2.3h10.5	v-2.3h-1.2v1.2H2.9z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <div
            class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
          >
            点击上传
          </div>
        </button>
      </div>
      <div
        class="arco-uploader-list"
      >
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-text"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div
                class="arco-uploader-list-item-loaded"
              >
                <svg
                  class="arco-icon arco-icon-check "
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 20 20"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M16.702 4.47a.5.5 0 00-.705.06L8.33 13.596 3.82 9.724a.5.5 0 00-.705.054l-.652.758a.5.5 0 00.054.706L7.361 15.4a.5.5 0 00.054.053l.526.445.22.188a.5.5 0 00.722-.047l8.641-10.218a.5.5 0 00-.059-.705l-.763-.645z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-close "
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`uploader demo test uploader demo: customize-icon2.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-uploader"
  >
    <div
      class="arco-uploader-container"
    >
      <div
        class="arco-uploader-add"
      >
        <input
          type="file"
        />
        <button
          class="arco-button arco-button-type-primary type-primary arco-button-size-medium arco-button-size-medium-is-semi size-medium arco-uploader-add-button is-semi pc"
          type="button"
        >
          <div
            class="arco-button-icon btn-icon"
          >
            <svg
              class="arco-icon arco-icon-upload "
              fill="currentColor"
              height="1em"
              viewBox="0 0 14 14"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7,1.5l3.1,3.1L9.2,5.4L7.6,3.7v5.9H6.4V3.7L4.8,5.4L3.9,4.6L7,1.5z M2.9,11.4v-1.2H1.8v2.3h10.5	v-2.3h-1.2v1.2H2.9z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <div
            class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
          >
            点击上传
          </div>
        </button>
      </div>
      <div
        class="arco-uploader-list"
      >
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div
                class="arco-uploader-list-item-loaded"
              >
                <svg
                  class="arco-icon arco-icon-download upload-loaded"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M7.3,8.8V1.7h1.3v7.1l1.9-1.9l0.9,0.9L8,11.3L4.5,7.8l0.9-0.9L7.3,8.8z M3.3,13v-1.3H2v2.7h12v-2.7	h-1.3V13H3.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div
                class="arco-uploader-list-item-loading"
              >
                <div
                  aria-valuemax="100"
                  aria-valuemin="0"
                  aria-valuenow="25"
                  class="arco-circle-progress"
                  role="progressbar"
                  style="width: 24px; height: 24px;"
                >
                  <svg
                    style="transform: rotateY(0deg) rotateZ(-90deg);"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="mini"
                      cx="12"
                      cy="12"
                      fill="none"
                      r="8"
                      stroke-width="4"
                    />
                    <circle
                      class="color"
                      cx="12"
                      cy="12"
                      fill="none"
                      r="8"
                      stroke-dasharray="50.26548245743669"
                      stroke-dashoffset="37.69911184307752"
                      stroke-linecap="square"
                      stroke-width="4"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text arco-uploader-list-item-text-error"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div>
                <div
                  class="arco-uploader-list-item-error"
                >
                  <div
                    class="upload-error"
                  >
                    <a>
                      上传失败，点击重试
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`uploader demo test uploader demo: customize-list.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-uploader"
  >
    <div
      class="arco-uploader-container"
    >
      <div
        class="arco-uploader-add"
      >
        <input
          accept="image/*"
          type="file"
        />
        <button
          class="arco-button arco-button-type-primary type-primary arco-button-size-medium arco-button-size-medium-is-semi size-medium arco-uploader-add-button is-semi pc"
          type="button"
        >
          <div
            class="arco-button-icon btn-icon"
          >
            <svg
              class="arco-icon arco-icon-upload "
              fill="currentColor"
              height="1em"
              viewBox="0 0 14 14"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7,1.5l3.1,3.1L9.2,5.4L7.6,3.7v5.9H6.4V3.7L4.8,5.4L3.9,4.6L7,1.5z M2.9,11.4v-1.2H1.8v2.3h10.5	v-2.3h-1.2v1.2H2.9z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <div
            class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
          >
            点击上传
          </div>
        </button>
      </div>
      <div
        class="demo-uploader-list"
      >
        <div
          class="demo-uploader-list-item"
        >
          <div
            class="arco-image all-border-box pc demo-uploader-list-item-image"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
          <div
            class="demo-uploader-list-item-text"
          >
            <span>
              user.png
            </span>
          </div>
          <div
            class="demo-uploader-list-item-status"
          >
            <div
              class="demo-uploader-list-item-loaded"
            >
              <svg
                class="arco-icon arco-icon-check "
                fill="currentColor"
                height="1em"
                viewBox="0 0 20 20"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M16.702 4.47a.5.5 0 00-.705.06L8.33 13.596 3.82 9.724a.5.5 0 00-.705.054l-.652.758a.5.5 0 00.054.706L7.361 15.4a.5.5 0 00.054.053l.526.445.22.188a.5.5 0 00.722-.047l8.641-10.218a.5.5 0 00-.059-.705l-.763-.645z"
                  fill-rule="evenodd"
                />
              </svg>
            </div>
          </div>
          <div
            class="demo-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete "
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
        <div
          class="demo-uploader-list-item"
        >
          <div
            class="arco-image all-border-box pc demo-uploader-list-item-image"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
          <div
            class="demo-uploader-list-item-text demo-uploader-list-item-text-loading"
          >
            <span>
              user.png
            </span>
          </div>
          <div
            class="demo-uploader-list-item-status"
          >
            <div
              class="demo-uploader-list-item-loading"
            >
              <div
                class="arco-loading all-border-box circle"
                style="animation-duration: 1000ms; width: 16px; height: 16px;"
              >
                <svg
                  viewBox="0 0 16 16"
                >
                  <defs>
                    <lineargradient
                      id="grad1-inner-1"
                      x1="0%"
                      x2="100%"
                      y1="0%"
                      y2="0%"
                    >
                      <stop
                        class="loading-circle-middle stop-color-with-config"
                        offset="0%"
                      />
                      <stop
                        class="loading-circle-start stop-color-with-config"
                        offset="100%"
                      />
                    </lineargradient>
                    <lineargradient
                      id="grad2-inner-1"
                      x1="0%"
                      x2="100%"
                      y1="0%"
                      y2="0%"
                    >
                      <stop
                        class="loading-circle-middle stop-color-with-config"
                        offset="0%"
                      />
                      <stop
                        class="loading-circle-end stop-color-with-config"
                        offset="100%"
                      />
                    </lineargradient>
                  </defs>
                  <circle
                    cx="8"
                    cy="8"
                    fill="none"
                    r="7"
                    stroke="url(#grad1-inner-1)"
                    stroke-dasharray="21.991148575128552"
                    stroke-dashoffset="21.991148575128552"
                    stroke-width="2"
                  />
                  <circle
                    cx="8"
                    cy="8"
                    fill="none"
                    r="7"
                    stroke="url(#grad2-inner-1)"
                    stroke-dasharray="21.991148575128552"
                    stroke-width="2"
                  />
                  <circle
                    class="loading-circle-filleted fill-color-with-config"
                    cx="15"
                    cy="8"
                    r="1"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div
            class="demo-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete "
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
        <div
          class="demo-uploader-list-item"
        >
          <div
            class="arco-image all-border-box pc demo-uploader-list-item-image"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
          <div
            class="demo-uploader-list-item-text demo-uploader-list-item-text-error"
          >
            <span>
              user.png
            </span>
            <svg
              class="arco-icon arco-icon-warn-circle demo-uploader-list-item-warning"
              fill="currentColor"
              height="1em"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 24C5.4 24 0 18.6 0 12S5.4 0 12 0s12 5.4 12 12-5.4 12-12 12zm0-22.5C6.2 1.5 1.5 6.2 1.5 12S6.2 22.5 12 22.5 22.5 17.8 22.5 12 17.8 1.5 12 1.5z"
              />
              <path
                d="M11.8 15.2h.3c.4 0 .7-.3.7-.7V7.2c0-.4-.3-.7-.7-.7h-.3c-.4 0-.7.3-.7.7v7.3c0 .4.3.7.7.7zM12.2 16.5h-.3c-.4 0-.7.3-.7.7v.3c0 .4.3.7.7.7h.3c.4 0 .7-.3.7-.7v-.3c0-.4-.3-.7-.7-.7z"
              />
            </svg>
          </div>
          <div
            class="demo-uploader-list-item-status"
          >
            <div>
              <span
                class="demo-uploader-list-item-error"
              >
                重试
              </span>
            </div>
          </div>
          <div
            class="demo-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete "
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`uploader demo test uploader demo: disabled.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-uploader arco-uploader-disabled"
  >
    <div
      class="arco-uploader-container"
    >
      <div
        class="arco-uploader-add"
      >
        <input
          type="file"
        />
        <button
          class="arco-button arco-button-type-primary type-primary arco-button-size-medium arco-button-size-medium-is-semi size-medium arco-uploader-add-button is-semi pc arco-button-type-primary-disabled disabled"
          type="button"
        >
          <div
            class="arco-button-icon btn-icon"
          >
            <svg
              class="arco-icon arco-icon-upload "
              fill="currentColor"
              height="1em"
              viewBox="0 0 14 14"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7,1.5l3.1,3.1L9.2,5.4L7.6,3.7v5.9H6.4V3.7L4.8,5.4L3.9,4.6L7,1.5z M2.9,11.4v-1.2H1.8v2.3h10.5	v-2.3h-1.2v1.2H2.9z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <div
            class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
          >
            点击上传
          </div>
        </button>
      </div>
      <div
        class="arco-uploader-list"
      >
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div
                class="arco-uploader-list-item-loaded"
              >
                <svg
                  class="arco-icon arco-icon-check "
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 20 20"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M16.702 4.47a.5.5 0 00-.705.06L8.33 13.596 3.82 9.724a.5.5 0 00-.705.054l-.652.758a.5.5 0 00.054.706L7.361 15.4a.5.5 0 00.054.053l.526.445.22.188a.5.5 0 00.722-.047l8.641-10.218a.5.5 0 00-.059-.705l-.763-.645z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon arco-uploader-list-item-delete-icon-disabled"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`uploader demo test uploader demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-uploader"
  >
    <div
      class="arco-uploader-container"
    >
      <div
        class="arco-uploader-add"
      >
        <input
          type="file"
        />
        <button
          class="arco-button arco-button-type-primary type-primary arco-button-size-medium arco-button-size-medium-is-semi size-medium arco-uploader-add-button is-semi pc"
          type="button"
        >
          <div
            class="arco-button-icon btn-icon"
          >
            <svg
              class="arco-icon arco-icon-upload "
              fill="currentColor"
              height="1em"
              viewBox="0 0 14 14"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7,1.5l3.1,3.1L9.2,5.4L7.6,3.7v5.9H6.4V3.7L4.8,5.4L3.9,4.6L7,1.5z M2.9,11.4v-1.2H1.8v2.3h10.5	v-2.3h-1.2v1.2H2.9z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <div
            class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
          >
            点击上传
          </div>
        </button>
      </div>
      <div
        class="arco-uploader-list"
      >
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div
                class="arco-uploader-list-item-loaded"
              >
                <svg
                  class="arco-icon arco-icon-check "
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 20 20"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M16.702 4.47a.5.5 0 00-.705.06L8.33 13.596 3.82 9.724a.5.5 0 00-.705.054l-.652.758a.5.5 0 00.054.706L7.361 15.4a.5.5 0 00.054.053l.526.445.22.188a.5.5 0 00.722-.047l8.641-10.218a.5.5 0 00-.059-.705l-.763-.645z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div
                class="arco-uploader-list-item-loading"
              >
                <div
                  class="arco-loading all-border-box circle"
                  style="animation-duration: 1000ms; width: 16px; height: 16px;"
                >
                  <svg
                    viewBox="0 0 16 16"
                  >
                    <defs>
                      <lineargradient
                        id="grad1-inner-2"
                        x1="0%"
                        x2="100%"
                        y1="0%"
                        y2="0%"
                      >
                        <stop
                          class="loading-circle-middle stop-color-with-config"
                          offset="0%"
                        />
                        <stop
                          class="loading-circle-start stop-color-with-config"
                          offset="100%"
                        />
                      </lineargradient>
                      <lineargradient
                        id="grad2-inner-2"
                        x1="0%"
                        x2="100%"
                        y1="0%"
                        y2="0%"
                      >
                        <stop
                          class="loading-circle-middle stop-color-with-config"
                          offset="0%"
                        />
                        <stop
                          class="loading-circle-end stop-color-with-config"
                          offset="100%"
                        />
                      </lineargradient>
                    </defs>
                    <circle
                      cx="8"
                      cy="8"
                      fill="none"
                      r="7"
                      stroke="url(#grad1-inner-2)"
                      stroke-dasharray="21.991148575128552"
                      stroke-dashoffset="21.991148575128552"
                      stroke-width="2"
                    />
                    <circle
                      cx="8"
                      cy="8"
                      fill="none"
                      r="7"
                      stroke="url(#grad2-inner-2)"
                      stroke-dasharray="21.991148575128552"
                      stroke-width="2"
                    />
                    <circle
                      class="loading-circle-filleted fill-color-with-config"
                      cx="15"
                      cy="8"
                      r="1"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
        <div
          class="arco-uploader-list-item"
        >
          <div
            class="arco-uploader-list-item-container"
          >
            <div
              class="arco-uploader-list-item-wrapper"
            >
              <div
                class="arco-uploader-list-item-file"
              >
                <svg
                  class="arco-icon arco-icon-file arco-uploader-list-item-file-icon"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2.3,2.7c0-0.7,0.6-1.3,1.3-1.3h7.3l2.7,2.7v9.3c0,0.7-0.6,1.3-1.3,1.3H3.7c-0.7,0-1.3-0.6-1.3-1.3	V2.7z M10.4,2.7H3.7v10.7h8.7V4.6L10.4,2.7z M10.7,7.7H5.3V6.3h5.3V7.7z M8.7,10.3H5.3V9h3.3V10.3z"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
              <div
                class="arco-uploader-list-item-text arco-uploader-list-item-text-error"
              >
                employeelist.doc
              </div>
            </div>
            <div
              class="arco-uploader-list-item-status"
            >
              <div>
                <div
                  class="arco-uploader-list-item-error"
                >
                  <span>
                    点击重试
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            class="arco-uploader-list-item-delete"
          >
            <svg
              class="arco-icon arco-icon-delete arco-uploader-list-item-delete-icon"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M640 85.333A42.667 42.667 0 01682.667 128l-.022 42.645 228.694.022c9.493 0 12.928.981 16.426 2.858a19.41 19.41 0 018.043 8.064c1.877 3.478 2.859 6.912 2.859 16.427v30.635c0 9.514-.982 12.949-2.859 16.426a19.392 19.392 0 01-8.064 8.064c-3.477 1.878-6.912 2.859-16.427 2.859l-57.984-.021V896a42.667 42.667 0 01-42.666 42.667H213.333A42.667 42.667 0 01170.667 896l-.022-640.021-57.962.021c-9.515 0-12.95-.981-16.427-2.859a19.392 19.392 0 01-8.064-8.064c-1.877-3.477-2.859-6.912-2.859-16.426v-30.635c0-9.515.982-12.95 2.859-16.427a19.392 19.392 0 018.064-8.064c3.477-1.877 6.912-2.858 16.427-2.858l228.629-.022.021-42.645A42.667 42.667 0 01384 85.333h256zM768 256H256v597.333h512V256zM448 384a21.333 21.333 0 0121.333 21.333V704A21.333 21.333 0 01448 725.333h-42.667A21.333 21.333 0 01384 704V405.333A21.333 21.333 0 01405.333 384H448zm170.667 0A21.333 21.333 0 01640 405.333V704a21.333 21.333 0 01-21.333 21.333H576A21.333 21.333 0 01554.667 704V405.333A21.333 21.333 0 01576 384h42.667z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
