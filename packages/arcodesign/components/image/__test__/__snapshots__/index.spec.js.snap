// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`image demo test image demo: fail.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="image-fit-demo"
  >
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container error animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-error-container"
        >
          <div
            class="image-retry-load"
          >
            <svg
              fill="none"
              height="25"
              viewBox="0 0 25 25"
              width="25"
            >
              <path
                d="M12.5 24C6.15458 24 1 18.8454 1 12.5C1 6.15458 6.15458 1 12.5 1C18.8454 1 24 6.15458 24 12.5C24 18.8454 18.8454 24 12.5 24Z"
                stroke="white"
              />
              <path
                d="M12.5298 14.6972C9.45045 14.736 8.2352 17.2113 8.1837 17.3181C8.07042 17.5511 8.1837 17.8229 8.43087 17.9199C8.67804 18.0267 8.96641 17.9199 9.06939 17.687C9.07969 17.6676 10.0684 15.5805 12.5401 15.6H12.5813C14.9088 15.6 15.9078 17.5996 15.949 17.687C16.0622 17.9102 16.3403 18.017 16.5875 17.9102C16.8346 17.8035 16.9376 17.5414 16.8243 17.3084C16.7729 17.2016 15.537 14.6875 12.571 14.6875C12.571 14.6972 12.5504 14.6972 12.5298 14.6972Z"
                fill="white"
                stroke="white"
                stroke-width="0.2"
              />
              <path
                clip-rule="evenodd"
                d="M16.8698 8.13965C17.4725 8.13965 17.9611 8.62825 17.9611 9.23097V10.868C17.9611 11.4707 17.4725 11.9593 16.8698 11.9593C16.267 11.9593 15.7784 11.4707 15.7784 10.868V9.23097C15.7784 8.62825 16.267 8.13965 16.8698 8.13965ZM8.13918 8.13965C8.7419 8.13965 9.2305 8.62825 9.2305 9.23097V10.868C9.2305 11.4707 8.7419 11.9593 8.13918 11.9593C7.53645 11.9593 7.04785 11.4707 7.04785 10.868V9.23097C7.04785 8.62825 7.53645 8.13965 8.13918 8.13965Z"
                fill="white"
                fill-rule="evenodd"
              />
            </svg>
            <div
              style="margin-top: 8px;"
            >
              Load failed
            </div>
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        Icon + Text
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container error animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-error-container"
        >
          <div
            class="image-retry-load"
          >
            Load failed
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        Text
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container error animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-error-container"
        >
          <svg
            fill="none"
            height="31"
            viewBox="0 0 32 31"
            width="32"
          >
            <path
              clip-rule="evenodd"
              d="M26.9964 2.20298C26.9468 1.53032 26.3853 1 25.7 1H2.3L2.20298 1.00357C1.53032 1.05319 1 1.61466 1 2.3V25.7L1.00357 25.797C1.05319 26.4697 1.61466 27 2.3 27H18.252C18.0875 26.3608 18 25.6906 18 25H3V3H25V17.0619C25.3276 17.021 25.6613 17 26 17C26.3387 17 26.6724 17.021 27 17.0619V2.3L26.9964 2.20298ZM21.9194 18.1175V12.0594C21.9194 11.9255 21.8663 11.7971 21.7716 11.7024C21.5745 11.5053 21.2548 11.5053 21.0577 11.7024L14.6748 18.0846L12.2685 15.6781C12.0713 15.481 11.7517 15.481 11.5546 15.6781L6.33889 20.8938C6.26811 20.9645 6.22824 21.0605 6.228 21.1606C6.22749 21.3697 6.39659 21.5396 6.60569 21.5401L12.0044 21.5539C12.0368 21.563 12.071 21.5678 12.1063 21.5678H18.7716C19.456 20.129 20.5573 18.9268 21.9194 18.1175ZM10.5664 10.2118V6.42578H6.78034V10.2118H10.5664Z"
              fill="#C9CDD4"
              fill-rule="evenodd"
            />
            <path
              d="M26 31C29.3137 31 32 28.3137 32 25C32 21.6863 29.3137 19 26 19C22.6863 19 20 21.6863 20 25C20 28.3137 22.6863 31 26 31Z"
              fill="#C9CDD4"
            />
            <path
              clip-rule="evenodd"
              d="M28.7987 27.0083C28.8768 26.9302 28.8768 26.8036 28.7987 26.7255L27.0179 24.9447L28.7861 23.1764C28.8642 23.0983 28.8642 22.9717 28.7861 22.8936L28.0083 22.1157C27.9302 22.0376 27.8036 22.0376 27.7255 22.1157L25.9572 23.884L24.1318 22.0586C24.0537 21.9805 23.9271 21.9805 23.849 22.0586L23.0712 22.8364C22.9931 22.9145 22.9931 23.0411 23.0712 23.1192L24.8966 24.9447L23.0586 26.7827C22.9805 26.8608 22.9805 26.9874 23.0586 27.0655L23.8364 27.8433C23.9145 27.9214 24.0411 27.9214 24.1192 27.8433L25.9572 26.0053L27.7381 27.7861C27.8162 27.8642 27.9428 27.8642 28.0209 27.7861L28.7987 27.0083Z"
              fill="white"
              fill-rule="evenodd"
            />
          </svg>
        </div>
      </div>
      <div
        class="group-text"
      >
        Placeholder Status
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image demo test image demo: fill.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="image-fit-demo fill"
  >
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <div
            class="image-loading"
          >
            <div
              class="arco-loading all-border-box circle loading-icon"
              style="animation-duration: 1000ms; width: 20px; height: 20px;"
            >
              <svg
                viewBox="0 0 20 20"
              >
                <defs>
                  <lineargradient
                    id="grad1-inner-0"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-start stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                  <lineargradient
                    id="grad2-inner-0"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-end stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                </defs>
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad1-inner-0)"
                  stroke-dasharray="26.703537555513243"
                  stroke-dashoffset="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad2-inner-0)"
                  stroke-dasharray="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  class="loading-circle-filleted fill-color-with-config"
                  cx="18.5"
                  cy="10"
                  r="1.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        contain
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <div
            class="image-loading"
          >
            <div
              class="arco-loading all-border-box circle loading-icon"
              style="animation-duration: 1000ms; width: 20px; height: 20px;"
            >
              <svg
                viewBox="0 0 20 20"
              >
                <defs>
                  <lineargradient
                    id="grad1-inner-1"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-start stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                  <lineargradient
                    id="grad2-inner-1"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-end stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                </defs>
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad1-inner-1)"
                  stroke-dasharray="26.703537555513243"
                  stroke-dashoffset="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad2-inner-1)"
                  stroke-dasharray="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  class="loading-circle-filleted fill-color-with-config"
                  cx="18.5"
                  cy="10"
                  r="1.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        cover
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <div
            class="image-loading"
          >
            <div
              class="arco-loading all-border-box circle loading-icon"
              style="animation-duration: 1000ms; width: 20px; height: 20px;"
            >
              <svg
                viewBox="0 0 20 20"
              >
                <defs>
                  <lineargradient
                    id="grad1-inner-2"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-start stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                  <lineargradient
                    id="grad2-inner-2"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-end stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                </defs>
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad1-inner-2)"
                  stroke-dasharray="26.703537555513243"
                  stroke-dashoffset="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad2-inner-2)"
                  stroke-dasharray="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  class="loading-circle-filleted fill-color-with-config"
                  cx="18.5"
                  cy="10"
                  r="1.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        fill
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <div
            class="image-loading"
          >
            <div
              class="arco-loading all-border-box circle loading-icon"
              style="animation-duration: 1000ms; width: 20px; height: 20px;"
            >
              <svg
                viewBox="0 0 20 20"
              >
                <defs>
                  <lineargradient
                    id="grad1-inner-3"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-start stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                  <lineargradient
                    id="grad2-inner-3"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-end stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                </defs>
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad1-inner-3)"
                  stroke-dasharray="26.703537555513243"
                  stroke-dashoffset="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad2-inner-3)"
                  stroke-dasharray="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  class="loading-circle-filleted fill-color-with-config"
                  cx="18.5"
                  cy="10"
                  r="1.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        none
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <div
            class="image-loading"
          >
            <div
              class="arco-loading all-border-box circle loading-icon"
              style="animation-duration: 1000ms; width: 20px; height: 20px;"
            >
              <svg
                viewBox="0 0 20 20"
              >
                <defs>
                  <lineargradient
                    id="grad1-inner-4"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-start stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                  <lineargradient
                    id="grad2-inner-4"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-end stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                </defs>
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad1-inner-4)"
                  stroke-dasharray="26.703537555513243"
                  stroke-dashoffset="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad2-inner-4)"
                  stroke-dasharray="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  class="loading-circle-filleted fill-color-with-config"
                  cx="18.5"
                  cy="10"
                  r="1.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        scale-down
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image demo test image demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="image-fit-demo"
  >
    <div
      class="arco-image all-border-box pc"
    >
      <div
        class="image-content image-bottom-overlap"
      >
        <div
          class="image-placeholder"
        />
      </div>
      <div
        class="image-container loading animate"
        style="transition-duration: 200ms;"
      />
    </div>
    <div
      class="arco-image all-border-box pc"
      style="width: 2.18rem; height: 2.18rem;"
    >
      <div
        class="image-content image-bottom-overlap"
      >
        <div
          class="image-placeholder"
        />
      </div>
      <div
        class="image-container init animate static-label"
        style="transition-duration: 200ms;"
      >
        <img
          alt=""
          class="image-content pc"
          crossorigin="anonymous"
          src="//sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/large_image_1.jpg"
          style="object-fit: cover; object-position: center;"
        />
      </div>
    </div>
    <div
      class="arco-image all-border-box pc"
    >
      <div
        class="image-content image-bottom-overlap"
      >
        <div
          class="image-placeholder"
        />
      </div>
      <div
        class="image-container loading animate"
        style="transition-duration: 200ms;"
      />
      <div
        class="image-content image-loading-container"
      >
        <div
          class="image-loading"
        >
          <div
            class="arco-loading all-border-box circle loading-icon"
            style="animation-duration: 1000ms; width: 20px; height: 20px;"
          >
            <svg
              viewBox="0 0 20 20"
            >
              <defs>
                <lineargradient
                  id="grad1-inner-5"
                  x1="0%"
                  x2="100%"
                  y1="0%"
                  y2="0%"
                >
                  <stop
                    class="loading-circle-middle stop-color-with-config"
                    offset="0%"
                  />
                  <stop
                    class="loading-circle-start stop-color-with-config"
                    offset="100%"
                  />
                </lineargradient>
                <lineargradient
                  id="grad2-inner-5"
                  x1="0%"
                  x2="100%"
                  y1="0%"
                  y2="0%"
                >
                  <stop
                    class="loading-circle-middle stop-color-with-config"
                    offset="0%"
                  />
                  <stop
                    class="loading-circle-end stop-color-with-config"
                    offset="100%"
                  />
                </lineargradient>
              </defs>
              <circle
                cx="10"
                cy="10"
                fill="none"
                r="8.5"
                stroke="url(#grad1-inner-5)"
                stroke-dasharray="26.703537555513243"
                stroke-dashoffset="26.703537555513243"
                stroke-width="3"
              />
              <circle
                cx="10"
                cy="10"
                fill="none"
                r="8.5"
                stroke="url(#grad2-inner-5)"
                stroke-dasharray="26.703537555513243"
                stroke-width="3"
              />
              <circle
                class="loading-circle-filleted fill-color-with-config"
                cx="18.5"
                cy="10"
                r="1.5"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image demo test image demo: lazy.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="image-fit-demo"
  >
    <div
      class="arco-show-monitor"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container init animate"
          style="transition-duration: 200ms;"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`image demo test image demo: loading.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="image-fit-demo"
  >
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <div
            class="image-loading"
          >
            <div
              class="arco-loading all-border-box circle loading-icon"
              style="animation-duration: 1000ms; width: 20px; height: 20px;"
            >
              <svg
                viewBox="0 0 20 20"
              >
                <defs>
                  <lineargradient
                    id="grad1-inner-6"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-start stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                  <lineargradient
                    id="grad2-inner-6"
                    x1="0%"
                    x2="100%"
                    y1="0%"
                    y2="0%"
                  >
                    <stop
                      class="loading-circle-middle stop-color-with-config"
                      offset="0%"
                    />
                    <stop
                      class="loading-circle-end stop-color-with-config"
                      offset="100%"
                    />
                  </lineargradient>
                </defs>
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad1-inner-6)"
                  stroke-dasharray="26.703537555513243"
                  stroke-dashoffset="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  cx="10"
                  cy="10"
                  fill="none"
                  r="8.5"
                  stroke="url(#grad2-inner-6)"
                  stroke-dasharray="26.703537555513243"
                  stroke-width="3"
                />
                <circle
                  class="loading-circle-filleted fill-color-with-config"
                  cx="18.5"
                  cy="10"
                  r="1.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        loading
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <div
            class="image-loading-demo"
          >
            50%
          </div>
        </div>
      </div>
      <div
        class="group-text"
      >
        Text loading
      </div>
    </div>
    <div
      class="image-group"
    >
      <div
        class="arco-image all-border-box pc"
      >
        <div
          class="image-content image-bottom-overlap"
        >
          <div
            class="image-placeholder"
          />
        </div>
        <div
          class="image-container loading animate"
          style="transition-duration: 200ms;"
        />
        <div
          class="image-content image-loading-container"
        >
          <svg
            fill="none"
            height="26"
            viewBox="0 0 26 26"
            width="26"
          >
            <path
              clip-rule="evenodd"
              d="M24.7 0C25.3853 0 25.9468 0.530321 25.9964 1.20298L26 1.3V24.7C26 25.3853 25.4697 25.9468 24.797 25.9964L24.7 26H1.3C0.614665 26 0.0531927 25.4697 0.00356573 24.797L0 24.7V1.3C0 0.614665 0.530321 0.0531927 1.20298 0.00356573L1.3 0H24.7ZM24 2H2V24H24V2ZM20.7716 10.7024C20.8663 10.7971 20.9194 10.9255 20.9194 11.0594V20.1892C20.9194 20.3983 20.7499 20.5678 20.5408 20.5678H11.1063C11.071 20.5678 11.0368 20.563 11.0044 20.5539L5.60569 20.5401C5.39659 20.5396 5.22749 20.3697 5.228 20.1606C5.22824 20.0605 5.26811 19.9645 5.33889 19.8938L10.5546 14.6781C10.7517 14.481 11.0713 14.481 11.2685 14.6781L13.6748 17.0846L20.0577 10.7024C20.2548 10.5053 20.5745 10.5053 20.7716 10.7024ZM9.5664 5.42578V9.21184H5.78034V5.42578H9.5664Z"
              fill="#C9CDD4"
              fill-rule="evenodd"
            />
          </svg>
        </div>
      </div>
      <div
        class="group-text"
      >
        Load placeholder
      </div>
    </div>
  </div>
</DocumentFragment>
`;
