// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`notice-bar demo test notice-bar demo: custom.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
    style="color: rgb(255, 87, 34); background-color: rgb(255, 241, 232);"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a notification message. The background color is customized here, and the gradient theme color will also be automatically set as the background color changes.
      </div>
    </div>
    <div
      class="arco-notice-bar-right-part"
    >
      <div
        style="background: rgb(255, 87, 34); line-height: 24px; color: white; font-size: 13px; border-radius: 24px; padding: 0px 8px; margin-top: -2px;"
      >
        Turn on
      </div>
    </div>
  </div>
  <div
    class="arco-notice-bar no-wrap"
    style="color: rgb(0, 180, 42); background-color: rgb(232, 255, 234); margin-top: 12px;"
  >
    <div
      class="arco-notice-bar-left-part"
    >
      <svg
        class="arco-icon arco-icon-success-circle "
        fill="currentColor"
        height="1em"
        viewBox="0 0 24 24"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 24C5.4 24 0 18.6 0 12S5.4 0 12 0s12 5.4 12 12-5.4 12-12 12zm0-22.5C6.2 1.5 1.5 6.2 1.5 12S6.2 22.5 12 22.5 22.5 17.8 22.5 12 17.8 1.5 12 1.5z"
        />
        <path
          d="M17.5 8c-.3-.3-.7-.3-1 0l-6.3 6.3-3-3c-.3-.3-.7-.3-1 0-.3.3-.3.7 0 1l3.5 3.5c.1.1.3.2.4.2s.4-.1.5-.2L17.5 9c.2-.3.2-.7 0-1z"
        />
      </svg>
    </div>
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        File is uploaded successfully!
      </div>
    </div>
  </div>
  <div
    class="arco-notice-bar no-wrap"
    style="color: rgb(245, 63, 63); background: rgb(255, 236, 232); margin-top: 12px;"
  >
    <div
      class="arco-notice-bar-left-part"
    >
      <svg
        class="arco-icon arco-icon-warn-circle "
        fill="currentColor"
        height="1em"
        viewBox="0 0 24 24"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 24C5.4 24 0 18.6 0 12S5.4 0 12 0s12 5.4 12 12-5.4 12-12 12zm0-22.5C6.2 1.5 1.5 6.2 1.5 12S6.2 22.5 12 22.5 22.5 17.8 22.5 12 17.8 1.5 12 1.5z"
        />
        <path
          d="M11.8 15.2h.3c.4 0 .7-.3.7-.7V7.2c0-.4-.3-.7-.7-.7h-.3c-.4 0-.7.3-.7.7v7.3c0 .4.3.7.7.7zM12.2 16.5h-.3c-.4 0-.7.3-.7.7v.3c0 .4.3.7.7.7h.3c.4 0 .7-.3.7-.7v-.3c0-.4-.3-.7-.7-.7z"
        />
      </svg>
    </div>
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        The network connection is not available, please check the network settings.
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: ellipsis.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap ellipsis"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a reminder message. This message is very long and will be omitted
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: icon.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
  >
    <div
      class="arco-notice-bar-left-part"
    >
      <svg
        class="arco-icon arco-icon-notice "
        fill="currentColor"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs />
        <path
          d="M618.667 853.333A21.333 21.333 0 01640 874.667v42.666a21.333 21.333 0 01-21.333 21.334H405.333A21.333 21.333 0 01384 917.333v-42.666a21.333 21.333 0 0121.333-21.334h213.334zM576 64a21.333 21.333 0 0121.333 21.333v32.427c147.222 39.125 256 177.067 256 341.248V704h64a21.333 21.333 0 0121.334 21.333V768a21.333 21.333 0 01-21.334 21.333H106.667A21.333 21.333 0 0185.333 768v-42.667A21.333 21.333 0 01106.667 704h64V459.008c0-164.181 108.8-302.123 256-341.248V85.333A21.333 21.333 0 01448 64h128zm-64 128c-141.376 0-256 119.595-256 267.136V704h512V459.136C768 311.595 653.376 192 512 192z"
        />
      </svg>
    </div>
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a reminder message.
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a reminder message.
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: marquee.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a very long reminder message displayed on a single line, longer than the container width, so it will scroll.
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
  <div
    class="arco-notice-bar no-wrap"
    style="margin-top: 12px;"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        It's a short message, but also scrollable.
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
  <div
    class="arco-notice-bar no-wrap"
    style="margin-top: 12px;"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Click me and I'll get longer and start scrolling!
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: more.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a reminder message.
      </div>
    </div>
    <div
      class="arco-notice-bar-right-part"
    >
      <svg
        class="arco-icon arco-icon-arrow-in "
        fill="currentColor"
        height="1em"
        viewBox="0 0 16 16"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.9 8L8.4 2.6c-.1-.1-.1-.3 0-.5l.5-.5c.1-.1.3-.1.5 0l5.9 5.9c.3.3.3.7 0 .9l-5.9 5.9c-.1.1-.3.1-.5 0l-.5-.5c-.1-.1-.1-.3 0-.5L13.9 8z"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: none.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a reminder message.
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: noshow.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        This is a reminder message.
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      Do not remind again
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: vertical.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar no-wrap"
  >
    <div
      class="arco-notice-bar-left-part"
    >
      <svg
        class="arco-icon arco-icon-notice "
        fill="currentColor"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs />
        <path
          d="M618.667 853.333A21.333 21.333 0 01640 874.667v42.666a21.333 21.333 0 01-21.333 21.334H405.333A21.333 21.333 0 01384 917.333v-42.666a21.333 21.333 0 0121.333-21.334h213.334zM576 64a21.333 21.333 0 0121.333 21.333v32.427c147.222 39.125 256 177.067 256 341.248V704h64a21.333 21.333 0 0121.334 21.333V768a21.333 21.333 0 01-21.334 21.333H106.667A21.333 21.333 0 0185.333 768v-42.667A21.333 21.333 0 01106.667 704h64V459.008c0-164.181 108.8-302.123 256-341.248V85.333A21.333 21.333 0 01448 64h128zm-64 128c-141.376 0-256 119.595-256 267.136V704h512V459.136C768 311.595 653.376 192 512 192z"
        />
      </svg>
    </div>
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        <div
          class="arco-carousel-wrap"
        >
          <div
            class="arco-carousel"
          >
            <div
              class="arco-carousel-inner auto vertical"
              style="transition-property: none; transition-duration: 0ms; transform: translateY(0) translateZ(0); width: 100%;"
            >
              <div
                class="arco-carousel-item carousel-item normal-item active vertical"
                style="transform: translateY(0px); padding-top: 0px; padding-bottom: 0px; box-sizing: border-box;"
              >
                <div>
                  Note that this is the first reminder message.
                </div>
              </div>
              <div
                class="arco-carousel-item carousel-item normal-item vertical ssr-float"
                style="transform: translateY(0px); padding-top: 0px; padding-bottom: 0px; box-sizing: border-box;"
              >
                <div>
                  Note that this is the second reminder message.
                </div>
              </div>
              <div
                class="arco-carousel-item carousel-item normal-item vertical ssr-float"
                style="transform: translateY(0px); padding-top: 0px; padding-bottom: 0px; box-sizing: border-box;"
              >
                <div>
                  Note that this is the third reminder message.
                </div>
              </div>
            </div>
          </div>
          <div
            class="arco-carousel-indicator pos-center arco-carousel-indicator-vertical vertical ver-pos-left"
          >
            <i
              class="indicator type-square active"
            />
            <i
              class="indicator type-square"
            />
            <i
              class="indicator type-square"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;

exports[`notice-bar demo test notice-bar demo: wrap.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-notice-bar wrapable"
  >
    <div
      class="arco-notice-bar-content"
    >
      <div
        class="arco-notice-bar-content-inner"
      >
        Note that this is a reminder message. The message is long, but I can choose to have it scroll or wrap, as it does now.
      </div>
    </div>
    <div
      class="arco-notice-bar-close"
    >
      <svg
        class="arco-icon arco-icon-close "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</DocumentFragment>
`;
