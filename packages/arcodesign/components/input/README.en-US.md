### Data Entry

# Input 

The input box, supports adding prefixes and suffixes.

======

> Props

|Property|Description|Type|DefaultValue|
|----------|-------------|------|------|
|type|Input box type|string|"text"|
|pattern|Regular expression to check the value of input box|string|-|
|inputClass|Custom classname for input DOM|string|-|
|inputStyle|Custom style for input DOM|CSSProperties|-|
|nativeProps|Other unlisted native properties have lower priority than listed component properties|InputHTMLAttributes\<HTMLInputElement\>|-|
|ariaLabel|accessible label|string|-|
|id|Input id|string|-|
|name|Input name|string|-|
|className|Custom classname|string|-|
|style|Custom stylesheet|CSSProperties|-|
|value|Binding value, if input, the component is controlled|string|-|
|defaultValue|Default value|string|-|
|maxLength|Maximum input length|number|-|
|border|Border display type|"all" \| "half" \| "bottom" \| "none"|"half"|
|placeholder|Placeholder text|string|-|
|disabled|Whether the input box is disabled|boolean|-|
|readOnly|Read\-only|boolean|-|
|autoFocus|Whether to automatically get the focus, it will trigger an onClick event once after being enabled|boolean|-|
|blockChangeWhenCompositing|When inputting Chinese on ios, onChange is not triggered during pinyin input, but only after confirming the selection|boolean|false|
|label|text to the left of the input box|ReactNode|-|
|required|Whether it is required|boolean|-|
|validator|Regular validation, input is not allowed if it does not meet the validation|RegExp \| ((value: string) =\> boolean)|-|
|prepend|The content of the header of the input box, outside the input box|ReactNode \| ((focusing: boolean, inputValue: string) =\> ReactNode)|-|
|append|The content at the end of the input box, outside the input box|ReactNode \| ((focusing: boolean, inputValue: string) =\> ReactNode)|-|
|blurBeforeFocus|Blur before focusing, that is, the keyboard will be re\-bounced when switching between different inputs\. It is often used to reload the keyboard when the input type is switched\. It is valid on Android\.|boolean|-|
|clearable|whether there is a clear button|boolean|-|
|clearShowType|Clear button display timing: focus \- display when focused, value \- display when there is value, always \- always display|"focus" \| "value" \| "always"|"focus"|
|preventEventWhenClearing|Whether to block the onBlur and onFocus events generated when the clear button is clicked in focus mode|boolean|true|
|clearIcon|Clear button type, also customizable|ReactNode|\<IconClear className="clear-icon" /\>|
|onClear|Callback when clear button is pressed|(e: TouchEvent\<HTMLElement\>) =\> void|-|
|prefix|The prefix of the input box, inside the input box, can also be customized|ReactNode|-|
|suffix|The suffix content of the input box, inside the input box, can also be customized|ReactNode|-|
|onChange|Fired when the data changes (when bluring the focus)|(e: ChangeEvent\<HTMLInputElement\>, value: string) =\> void|-|
|onInput|Callback when data changes|(e: ChangeEvent\<HTMLInputElement\>, value: string) =\> void|-|
|onFocus|Callback when the input box is focused|(e: FocusEvent\<HTMLInputElement, Element\>) =\> void|-|
|onBlur|Callback when the input box is blured|(e: FocusEvent\<HTMLInputElement, Element\>) =\> void|-|
|onClick|Callback when clicking the input box|(e: MouseEvent\<HTMLInputElement, MouseEvent\>) =\> void|-|
|onKeyUp|Native keyup event|(e: KeyboardEvent\<HTMLInputElement\>) =\> void|-|
|onKeyDown|Native keydown event|(e: KeyboardEvent\<HTMLInputElement\>) =\> void|-|
|onKeyPress|Native keypress event|(e: KeyboardEvent\<HTMLInputElement\>) =\> void|-|
|onPressEnter|Callback when the enter key is pressed|(e: KeyboardEvent\<HTMLInputElement\>) =\> void|-|

> Refs

|Property|Description|Type|
|----------|-------------|------|
|dom|The outermost element DOM|HTMLDivElement|
|input|Native input DOM|HTMLInputElement|
