@import '../../../mixin/pxtorem.less';

@prefix: arco;
@base-font-size: 50;
@arco-dark-mode-selector: .arco-theme-dark;
@background-color: #FFFFFF;
@dark-background-color: #17171A;
@container-background-color: #FFFFFF;
@dark-container-background-color: #232324;
@card-background-color: #F7F8FA;
@dark-card-background-color: hsla(0, 0%, 100%, 0.08);
@font-color: #1d2129;
@dark-font-color: #f6f6f6;
@sub-font-color: #4e5969;
@dark-sub-font-color: #c5c5c5;
@sub-info-font-color: #86909c;
@dark-sub-info-font-color: #929293;
@line-color: #e5e6eb;
@dark-line-color: #484849;
@lighter-line-color: #f2f3f5;
@dark-lighter-line-color: #2e2e30;
@primary-color: #165DFF;
@dark-primary-color: #3C7EFF;
@primary-disabled-color: #94BFFF;
@dark-primary-disabled-color: #0E32A6;
@lighter-primary-color: #E8F3FF;
@dark-lighter-primary-color: #000D4D;
@danger-color: #F53F3F;
@dark-danger-color: #F76965;
@warning-color: #FF7D00;
@dark-warning-color: #FF9626;
@success-color: #00B42A;
@dark-success-color: #27C346;
@disabled-color: #c9cdd4;
@dark-disabled-color: #5f5f60;
@mask-background: rgba(0, 0, 0, 0.6);
@mask-content-color: #FFFFFF;
@dark-mask-content-color: rgba(255, 255, 255, 0.9);
@mask-content-background: #FFFFFF;
@dark-mask-content-background: #2A2A2B;
@scroller-buffer: 10PX;
@full-screen-z-index: 1000;
@fixed-z-index: 100;
@popup-mask-background: @mask-background;
@dark-popup-content-background: @dark-mask-content-background;
@popup-content-background: @mask-content-background;
@popup-enter-transition: all 450ms cubic-bezier(0.34, 0.69, 0.1, 1);
@popup-exit-transition: all 240ms cubic-bezier(0.34, 0.69, 0.1, 1);
@dialog-mask-background: @mask-background;
@dialog-content-width: ~`pxtorem(270)`;
@dialog-content-android-width: ~`pxtorem(280)`;
@dark-dialog-content-background: @dark-mask-content-background;
@dialog-content-background: @mask-content-background;
@dialog-content-border-radius: ~`pxtorem(8)`;
@dialog-content-android-border-radius: ~`pxtorem(4)`;
@dialog-ios-horizontal-padding: ~`pxtorem(16)`;
@dialog-ios-vertical-padding: ~`pxtorem(20)`;
@dialog-ios-header-body-gutter: ~`pxtorem(4)`;
@dialog-android-horizontal-padding: ~`pxtorem(24)`;
@dialog-android-vertical-padding: ~`pxtorem(20)`;
@dialog-android-header-body-gutter: ~`pxtorem(12)`;
@dialog-android-body-footer-gutter: ~`pxtorem(24)`;
@dark-dialog-body-ios-color: @dark-sub-font-color;
@dialog-body-ios-color: @sub-font-color;
@dialog-body-ios-font-size: ~`pxtorem(15)`;
@dialog-body-ios-line-height: ~`pxtorem(22)`;
@dark-dialog-body-android-color: @dark-sub-font-color;
@dialog-body-android-color: @sub-font-color;
@dialog-body-android-font-size: ~`pxtorem(15)`;
@dialog-body-android-line-height: ~`pxtorem(24)`;
@dark-dialog-header-ios-color: @dark-font-color;
@dialog-header-ios-color: @font-color;
@dark-dialog-header-android-color: @dark-font-color;
@dialog-header-android-color: @font-color;
@dialog-header-ios-font-size: ~`pxtorem(17)`;
@dialog-header-ios-line-height: ~`pxtorem(26)`;
@dialog-header-android-font-size: ~`pxtorem(17)`;
@dialog-header-android-line-height: ~`pxtorem(28)`;
@dark-dialog-footer-ios-color: @dark-primary-color;
@dialog-footer-ios-color: @primary-color;
@dialog-footer-ios-font-size: ~`pxtorem(16)`;
@dialog-footer-ios-height: ~`pxtorem(44)`;
@dialog-footer-android-color: #1a74ff;
@dialog-footer-android-font-size: ~`pxtorem(15)`;
@dialog-footer-android-line-height: ~`pxtorem(20)`;
@dialog-footer-android-button-gutter: ~`pxtorem(28)`;
@dark-dialog-button-footer-primary-background: @dark-primary-color;
@dialog-button-footer-primary-background: @primary-color;
@dark-dialog-button-footer-primary-color: @dark-mask-content-color;
@dialog-button-footer-primary-color: @mask-content-color;
@dark-dialog-button-footer-color: @dark-sub-info-font-color;
@dialog-button-footer-color: @sub-info-font-color;
@dialog-button-footer-height: ~`pxtorem(36)`;
@dialog-button-footer-border-radius: ~`pxtorem(30)`;
@dialog-button-footer-gutter: ~`pxtorem(8)`;
@carousel-auto-transition: cubic-bezier(0.66, 0, 0.34, 1);
@carousel-slide-transition: cubic-bezier(0.32, 0.94, 0.6, 1);
@carousel-indicator-background: rgba(255, 255, 255, 0.5);
@dark-carousel-indicator-active-background: @dark-mask-content-color;
@carousel-indicator-active-background: @mask-content-color;
@dark-carousel-indicator-inverse-background: @dark-line-color;
@carousel-indicator-inverse-background: @line-color;
@dark-carousel-indicator-active-inverse-background: @dark-primary-color;
@carousel-indicator-active-inverse-background: @primary-color;
@carousel-indicator-outside-padding: ~`pxtorem(8)` 0 ~`pxtorem(5)`;
@carousel-indicator-position: ~`pxtorem(12)`;
@carousel-indicator-safe-padding: ~`pxtorem(16)`;
@carousel-circle-indicator-gutter: ~`pxtorem(8)`;
@carousel-square-indicator-gutter: ~`pxtorem(8)`;
@carousel-circle-indicator-size: 6PX;
@carousel-square-indicator-width: ~`pxtorem(12)`;
@carousel-square-indicator-height: 3PX;
@dark-carousel-item-text-color: @dark-mask-content-color;
@carousel-item-text-color: @mask-content-color;
@carousel-item-text-background: linear-gradient(180deg, rgba(0, 0, 0, 0) 5.18%, rgba(0, 0, 0, 0.15) 100%);
@carousel-item-text-height: ~`pxtorem(32)`;
@carousel-item-text-padding: 0 ~`pxtorem(12)`;
@carousel-item-text-font-size: ~`pxtorem(16)`;
@input-height: ~`pxtorem(54)`;
@dark-input-disabled-color: @dark-disabled-color;
@input-disabled-color: @disabled-color;
@dark-input-placeholder-color: @dark-disabled-color;
@input-placeholder-color: @disabled-color;
@dark-input-clear-icon-color: @dark-disabled-color;
@input-clear-icon-color: @disabled-color;
@input-clear-icon-font-size: 16PX;
@input-label-gutter: ~`pxtorem(24)`;
@input-horizontal-padding: ~`pxtorem(16)`;
@input-vertical-padding: ~`pxtorem(12)`;
@dark-input-caret-color: @dark-primary-color;
@input-caret-color: @primary-color;
@input-label-min-width: ~`pxtorem(64)`;
@input-text-font-size: ~`pxtorem(16)`;
@input-text-line-height: ~`pxtorem(22)`;
@textarea-font-size: ~`pxtorem(16)`;
@textarea-line-height: ~`pxtorem(22)`;
@textarea-padding: ~`pxtorem(16)`;
@textarea-has-stat-padding: ~`pxtorem(16)` ~`pxtorem(16)` ~`pxtorem(44)`;
@dark-textarea-statistic-color: @dark-sub-info-font-color;
@textarea-statistic-color: @sub-info-font-color;
@textarea-statistic-font-size: ~`pxtorem(14)`;
@avatar-size-map: large, medium, small, smaller, ultra-small;
@avatar-large-size: ~`pxtorem(56)`;
@avatar-medium-size: ~`pxtorem(48)`;
@avatar-small-size: ~`pxtorem(40)`;
@avatar-smaller-size: ~`pxtorem(32)`;
@avatar-ultra-small-size: ~`pxtorem(24)`;
@avatar-default-overlap-large-size: ~`pxtorem(28)`;
@avatar-default-overlap-medium-size: ~`pxtorem(24)`;
@avatar-default-overlap-small-size: ~`pxtorem(20)`;
@avatar-default-overlap-smaller-size: ~`pxtorem(16)`;
@avatar-default-overlap-ultra-small-size: ~`pxtorem(12)`;
@avatar-background: #4080FF;
@dark-avatar-background: #306FFF;
@dark-avatar-default-overlap-background: @dark-disabled-color;
@avatar-default-overlap-background: @disabled-color;
@dark-avatar-text-font-color: @dark-mask-content-color;
@avatar-text-font-color: @mask-content-color;
@avatar-large-text-font-size: ~`pxtorem(16)`;
@avatar-medium-text-font-size: ~`pxtorem(16)`;
@avatar-small-text-font-size: ~`pxtorem(14)`;
@avatar-smaller-text-font-size: ~`pxtorem(12)`;
@avatar-ultra-small-text-font-size: ~`pxtorem(10)`;
@avatar-group-large-size-offset: ~`pxtorem(-12)`;
@avatar-group-large-size-border: ~`pxtorem(1.5)`;
@avatar-group-medium-size-offset: ~`pxtorem(-12)`;
@avatar-group-medium-size-border: ~`pxtorem(1.5)`;
@avatar-group-small-size-offset: ~`pxtorem(-12)`;
@avatar-group-small-size-border: ~`pxtorem(1.5)`;
@avatar-group-smaller-size-offset: ~`pxtorem(-8)`;
@avatar-group-smaller-size-border: ~`pxtorem(1)`;
@avatar-group-ultra-small-size-offset: ~`pxtorem(-8)`;
@avatar-group-ultra-small-size-border: ~`pxtorem(1)`;
@dark-avatar-group-border-color: @dark-background-color;
@avatar-group-border-color: @background-color;
@avatar-info-box-large-size: ~`pxtorem(88)`;
@avatar-info-box-medium-size: ~`pxtorem(80)`;
@avatar-info-box-small-size: ~`pxtorem(80)`;
@avatar-info-box-smaller-size: ~`pxtorem(64)`;
@avatar-info-box-ultra-small-size: ~`pxtorem(56)`;
@avatar-name-large-font-size: ~`pxtorem(18)`;
@avatar-name-large-line-height: ~`pxtorem(26)`;
@avatar-desc-large-font-size: ~`pxtorem(14)`;
@avatar-desc-large-line-height: ~`pxtorem(20)`;
@avatar-desc-large-margin-top: ~`pxtorem(2)`;
@avatar-name-medium-font-size: ~`pxtorem(18)`;
@avatar-name-medium-line-height: ~`pxtorem(26)`;
@avatar-desc-medium-font-size: ~`pxtorem(14)`;
@avatar-desc-medium-line-height: ~`pxtorem(20)`;
@avatar-desc-medium-margin-top: ~`pxtorem(2)`;
@avatar-name-small-font-size: ~`pxtorem(16)`;
@avatar-name-small-line-height: ~`pxtorem(24)`;
@avatar-desc-small-font-size: ~`pxtorem(12)`;
@avatar-desc-small-line-height: ~`pxtorem(16)`;
@avatar-desc-small-margin-top: ~`pxtorem(0)`;
@avatar-name-smaller-font-size: ~`pxtorem(14)`;
@avatar-name-smaller-line-height: ~`pxtorem(20)`;
@avatar-desc-smaller-font-size: ~`pxtorem(12)`;
@avatar-desc-smaller-line-height: ~`pxtorem(16)`;
@avatar-desc-smaller-margin-top: ~`pxtorem(0)`;
@avatar-name-ultra-small-font-size: ~`pxtorem(13)`;
@avatar-name-ultra-small-line-height: ~`pxtorem(18)`;
@avatar-desc-ultra-small-font-size: ~`pxtorem(10)`;
@avatar-desc-ultra-small-line-height: ~`pxtorem(14)`;
@avatar-desc-ultra-small-margin-top: ~`pxtorem(2)`;
@dark-avatar-name-color: @dark-font-color;
@avatar-name-color: @font-color;
@dark-avatar-desc-color: @dark-sub-info-font-color;
@avatar-desc-color: @sub-info-font-color;
@button-line-height: 1.2;
@button-radius: 2PX;
@button-icon-text-gutter: ~`pxtorem(4)`;
@dark-button-primary-background: @dark-primary-color;
@button-primary-background: @primary-color;
@button-primary-clicked-background: #0E42D2;
@dark-button-primary-clicked-background: #689FFF;
@dark-button-primary-disabled-background: @dark-primary-disabled-color;
@button-primary-disabled-background: @primary-disabled-color;
@dark-button-primary-text-color: @dark-mask-content-color;
@button-primary-text-color: @mask-content-color;
@dark-button-primary-disabled-text-color: @dark-lighter-primary-color;
@button-primary-disabled-text-color: @lighter-primary-color;
@dark-button-default-background: @dark-lighter-primary-color;
@button-default-background: @lighter-primary-color;
@dark-button-default-clicked-background: @dark-primary-disabled-color;
@button-default-clicked-background: @primary-disabled-color;
@dark-button-default-disabled-background: @dark-lighter-primary-color;
@button-default-disabled-background: @lighter-primary-color;
@dark-button-default-text-color: @dark-primary-color;
@button-default-text-color: @primary-color;
@dark-button-default-disabled-text-color: @dark-primary-disabled-color;
@button-default-disabled-text-color: @primary-disabled-color;
@button-ghost-background: transparent;
@dark-button-ghost-clicked-background: @dark-lighter-primary-color;
@button-ghost-clicked-background: @lighter-primary-color;
@button-ghost-disabled-background: transparent;
@dark-button-ghost-text-color: @dark-primary-color;
@button-ghost-text-color: @primary-color;
@dark-button-ghost-disabled-text-color: @dark-primary-disabled-color;
@button-ghost-disabled-text-color: @primary-disabled-color;
@button-huge-padding: 0 ~`pxtorem(16)`;
@button-huge-height: ~`pxtorem(44)`;
@button-huge-text-size: ~`pxtorem(16)`;
@button-large-padding: 0 ~`pxtorem(16)`;
@button-large-height: ~`pxtorem(36)`;
@button-large-text-size: ~`pxtorem(15)`;
@button-medium-padding: 0 ~`pxtorem(16)`;
@button-medium-height: ~`pxtorem(32)`;
@button-medium-text-size: ~`pxtorem(14)`;
@button-small-padding: 0 ~`pxtorem(8)`;
@button-small-height: ~`pxtorem(28)`;
@button-small-text-size: ~`pxtorem(14)`;
@button-mini-padding: 0 ~`pxtorem(8)`;
@button-mini-height: ~`pxtorem(24)`;
@button-mini-text-size: ~`pxtorem(12)`;
@ellipsis-default-text-size: ~`pxtorem(16)`;
@ellipsis-float-ellipsis-node-background: linear-gradient(90deg, rgba(255, 255, 255, 0), #ffffff 20PX, #ffffff);
@dark-ellipsis-float-ellipsis-node-background: linear-gradient(90deg, rgba(35, 35, 36, 0), #232324 20PX, #232324);
@ellipsis-float-ellipsis-node-padding-left: 20PX;
@dark-checkbox-icon-color: @dark-line-color;
@checkbox-icon-color: @line-color;
@checkbox-icon-font-size: 20PX;
@checkbox-icon-margin-right: ~`pxtorem(8)`;
@dark-checkbox-icon-checked-color: @dark-primary-color;
@checkbox-icon-checked-color: @primary-color;
@dark-checkbox-icon-disabled-color: @dark-card-background-color;
@checkbox-icon-disabled-color: @card-background-color;
@dark-checkbox-icon-checked-disabled-color: @dark-lighter-primary-color;
@checkbox-icon-checked-disabled-color: @lighter-primary-color;
@checkbox-text-font-size: ~`pxtorem(16)`;
@checkbox-text-disabled-opacity: 0.5;
@checkbox-group-gutter: ~`pxtorem(24)`;
@tabs-tab-bar-font-size: ~`pxtorem(15)`;
@dark-tabs-tab-bar-background: @dark-background-color;
@tabs-tab-bar-background: @background-color;
@tabs-tab-bar-height: ~`pxtorem(42)`;
@tabs-tab-bar-width: ~`pxtorem(78)`;
@tabs-tab-bar-horizontal-height: ~`pxtorem(54)`;
@tabs-tab-bar-card-height: ~`pxtorem(40)`;
@dark-tabs-tab-bar-card-color: @dark-primary-color;
@tabs-tab-bar-card-color: @primary-color;
@dark-tabs-tab-bar-card-text-color: @dark-mask-content-color;
@tabs-tab-bar-card-text-color: @mask-content-color;
@tabs-tab-bar-card-border-radius: 2PX;
@dark-tabs-tab-bar-line-active-color: @dark-primary-color;
@tabs-tab-bar-line-active-color: @primary-color;
@tabs-tab-bar-line-gutter: ~`pxtorem(40)`;
@tabs-tab-bar-tag-gutter: ~`pxtorem(16)`;
@tabs-tab-bar-tag-height: ~`pxtorem(60)`;
@tabs-tab-bar-tag-vertical-padding: ~`pxtorem(12)`;
@dark-tabs-tab-bar-tag-background: @dark-card-background-color;
@tabs-tab-bar-tag-background: @card-background-color;
@dark-tabs-tab-bar-tag-text-color: @dark-font-color;
@tabs-tab-bar-tag-text-color: @font-color;
@dark-tabs-tab-bar-tag-active-background: @dark-primary-color;
@tabs-tab-bar-tag-active-background: @primary-color;
@dark-tabs-tab-bar-tag-active-text-color: @dark-mask-content-color;
@tabs-tab-bar-tag-active-text-color: @mask-content-color;
@tabs-tab-bar-tag-padding: 0 ~`pxtorem(16)`;
@dark-tabs-underline-color: @dark-primary-color;
@tabs-underline-color: @primary-color;
@tabs-underline-thick: 2PX;
@tabs-underline-size: ~`pxtorem(24)`;
@tabs-underline-border-radius: 2PX;
@tab-bar-height: ~`pxtorem(50)`;
@tab-bar-font-size: ~`pxtorem(10)`;
@tab-bar-icon-size: ~`pxtorem(20)`;
@tab-bar-only-title-font-size: ~`pxtorem(16)`;
@dark-tab-bar-color: @dark-sub-info-font-color;
@tab-bar-color: @sub-info-font-color;
@dark-tab-bar-active-color: @dark-primary-color;
@tab-bar-active-color: @primary-color;
@tab-bar-title-line-height: ~`pxtorem(14)`;
@tab-bar-only-title-line-height: ~`pxtorem(22)`;
@tab-bar-title-margin: 0 0 ~`pxtorem(5)` 0;
@tab-bar-item-icon-margin: ~`pxtorem(7)` 0 ~`pxtorem(4)`;
@nav-bar-height: ~`pxtorem(44)`;
@dark-nav-bar-bottom-border-color: @dark-line-color;
@nav-bar-bottom-border-color: @line-color;
@dark-nav-bar-background: @dark-background-color;
@nav-bar-background: @background-color;
@dark-nav-bar-font-color: @dark-font-color;
@nav-bar-font-color: @font-color;
@nav-bar-two-sides-font-size: ~`pxtorem(16)`;
@nav-bar-two-sides-padding: 0 ~`pxtorem(16)`;
@nav-bar-title-font-size: ~`pxtorem(17)`;
@nav-bar-title-text-font-size: ~`pxtorem(17)`;
@nav-bar-title-padding: 0 ~`pxtorem(46)`;
@nav-bar-back-icon-height: ~`pxtorem(16)`;
@dark-image-placeholder-background: @dark-card-background-color;
@image-placeholder-background: @card-background-color;
@dark-image-loading-icon-color: @dark-mask-content-color;
@image-loading-icon-color: @mask-content-color;
@dark-image-retry-icon-color: @dark-mask-content-color;
@image-retry-icon-color: @mask-content-color;
@image-mask-background: @mask-background;
@image-transition-function: cubic-bezier(0.39, 0.575, 0.565, 1);
@image-inner-font-size: ~`pxtorem(16)`;
@image-retry-font-size: ~`pxtorem(16)`;
@dark-switch-text-color: @dark-sub-font-color;
@switch-text-color: @sub-font-color;
@dark-switch-text-checked-color: @dark-mask-content-color;
@switch-text-checked-color: @mask-content-color;
@switch-inner-background: #FFFFFF;
@switch-inner-transition: all .2s;
@switch-inner-fully-border-radius: 50%;
@switch-inner-semi-border-radius: 1PX;
@switch-android-width: 40PX;
@switch-android-height: 24PX;
@switch-android-padding: 2PX;
@switch-android-inner-diameter-size: 20PX;
@switch-android-inner-box-shadow: 0 2PX 4PX 0 rgba(0, 0, 0, 0.08);
@switch-android-fully-border-radius: 20PX;
@switch-android-semi-border-radius: 2PX;
@switch-android-checked-inner-transform: translateX(16PX);
@switch-android-text-font-size: 12PX;
@switch-android-text-gap-size: 5PX;
@dark-switch-android-background: @dark-line-color;
@switch-android-background: @line-color;
@dark-switch-android-checked-background: @dark-primary-color;
@switch-android-checked-background: @primary-color;
@dark-switch-android-disabled-checked-background: @dark-primary-disabled-color;
@switch-android-disabled-checked-background: @primary-disabled-color;
@dark-switch-android-disabled-background: @dark-lighter-line-color;
@switch-android-disabled-background: @lighter-line-color;
@switch-ios-width: ~`pxtorem(51)`;
@switch-ios-height: ~`pxtorem(31)`;
@switch-ios-padding: ~`pxtorem(2)`;
@switch-ios-inner-diameter-size: ~`pxtorem(27)`;
@switch-ios-inner-border-color: rgba(0, 0, 0, .04);
@switch-ios-inner-box-shadow: 0 3PX 2PX 0 rgba(0, 0, 0, .12);
@switch-ios-fully-border-radius: ~`pxtorem(16)`;
@switch-ios-semi-border-radius: ~`pxtorem(2)`;
@switch-ios-checked-inner-transform: translateX(~`pxtorem(20)`);
@switch-ios-text-font-size: ~`pxtorem(14)`;
@switch-ios-text-gap-size: ~`pxtorem(6)`;
@switch-ios-background: rgba(17, 17, 17, .15);
@switch-ios-checked-background: #34C759;
@switch-ios-disabled-checked-background: #4DD865;
@switch-ios-disabled-checked-opacity: 0.3;
@switch-ios-disabled-background: rgba(120, 120, 128, .16);
@toast-background: rgba(0, 0, 0, 0.8);
@dark-toast-text-color: @dark-mask-content-color;
@toast-text-color: @mask-content-color;
@toast-font-size: ~`pxtorem(16)`;
@toast-line-height: ~`pxtorem(24)`;
@toast-border-radius: ~`pxtorem(4)`;
@toast-loading-arc-background-color: #666666;
@toast-loading-inner-font-size: ~`pxtorem(12)`;
@toast-safe-padding: 0 ~`pxtorem(16)`;
@toast-from-top-position: 30%;
@toast-from-bottom-position: 30%;
@toast-horizontal-padding: ~`pxtorem(8)` ~`pxtorem(16)`;
@toast-horizontal-icon-size: ~`pxtorem(16)`;
@toast-horizontal-icon-content-gutter: ~`pxtorem(8)`;
@toast-vertical-padding: ~`pxtorem(16)`;
@toast-vertical-icon-size: ~`pxtorem(24)`;
@toast-vertical-icon-content-gutter: ~`pxtorem(8)`;
@dark-loading-color: @dark-primary-color;
@loading-color: @primary-color;
@dark-loading-arc-background-color: @dark-line-color;
@loading-arc-background-color: @line-color;
@loading-dot-size: 6PX;
@loading-dot-gutter: ~`pxtorem(6)`;
@picker-view-font-size: ~`pxtorem(16)`;
@picker-view-cell-height: ~`pxtorem(44)`;
@picker-view-wrapper-height: ~`pxtorem(220)`;
@picker-view-mask-top-background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%);
@dark-picker-view-mask-top-background: linear-gradient(to bottom, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%);
@picker-view-mask-bottom-background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%);
@dark-picker-view-mask-bottom-background: linear-gradient(to top, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%);
@dark-picker-view-selection-border-color: @dark-line-color;
@picker-view-selection-border-color: @line-color;
@picker-wrapper-shadow: 0 2PX 8PX rgba(0, 0, 0, .15);
@picker-wrapper-border-radius: ~`pxtorem(4)`;
@picker-header-height: ~`pxtorem(54)`;
@dark-picker-header-background: @dark-mask-content-background;
@picker-header-background: @mask-content-background;
@picker-title-font-size: ~`pxtorem(16)`;
@picker-title-padding: 0 ~`pxtorem(60)`;
@picker-button-font-size: ~`pxtorem(15)`;
@picker-button-padding: ~`pxtorem(16)`;
@dark-picker-left-btn-color: @dark-primary-color;
@picker-left-btn-color: @primary-color;
@dark-picker-right-btn-color: @dark-primary-color;
@picker-right-btn-color: @primary-color;
@popover-arrow-size: 9PX;
@popover-arrow-border-radius: 1PX;
@popover-inner-border-radius: 4PX;
@popover-inner-opacity: 0.8;
@popover-inner-transition: opacity .3s ease-in-out;
@popover-inner-white-theme-opacity: 1;
@popover-inner-background-shadow: 0 2PX 8PX 0 rgba(0, 0, 0, .1);
@popover-inner-top-arrow-shadow: 6PX 6PX 8PX 0 rgba(0, 0, 0, .04);
@popover-inner-bottom-arrow-shadow: -6PX -6PX 8PX 0 rgba(0, 0, 0, .04);
@popover-background-color: #000000;
@dark-popover-white-theme-background-color: @dark-mask-content-background;
@popover-white-theme-background-color: @mask-content-background;
@dark-popover-content-color: @dark-mask-content-color;
@popover-content-color: @mask-content-color;
@popover-content-padding: ~`pxtorem(8)` ~`pxtorem(12)`;
@popover-content-android-padding: ~`pxtorem(10)` ~`pxtorem(12)` ~`pxtorem(6)`;
@popover-content-font-size: ~`pxtorem(14)`;
@popover-content-line-height: ~`pxtorem(20)`;
@popover-content-disabled-color: rgba(255, 255, 255, 0.3);
@dark-popover-content-white-theme-color: @dark-font-color;
@popover-content-white-theme-color: @font-color;
@dark-popover-content-white-theme-disabled-color: @dark-disabled-color;
@popover-content-white-theme-disabled-color: @disabled-color;
@popover-content-border-color: rgba(247, 248, 250, 0.1);
@dark-popover-content-white-theme-border-color: @dark-line-color;
@popover-content-white-theme-border-color: @line-color;
@popover-shadow-color: rgba(0, 0, 0, 0.1);
@popover-menu-content-padding: 0 ~`pxtorem(12)`;
@dark-popover-menu-icon-white-theme-color: @dark-sub-font-color;
@popover-menu-icon-white-theme-color: @sub-font-color;
@popover-menu-active-background: #242425;
@dark-popover-menu-active-white-theme-background: @dark-card-background-color;
@popover-menu-active-white-theme-background: @card-background-color;
@popover-horizontal-menu-max-width: ~`pxtorem(288)`;
@popover-horizontal-menu-item-size: ~`pxtorem(72)`;
@popover-horizontal-menu-item-padding: ~`pxtorem(12)` ~`pxtorem(0)`;
@popover-horizontal-menu-icon-margin: ~`pxtorem(0)` ~`pxtorem(0)` ~`pxtorem(8)` ~`pxtorem(0)`;
@popover-icon-divider-color: rgba(255, 255, 255, 0.3);
@popover-icon-divider-height: ~`pxtorem(12)`;
@popover-icon-size: ~`pxtorem(16)`;
@popover-icon-padding: ~`pxtorem(0)` ~`pxtorem(10)` ~`pxtorem(0)` ~`pxtorem(11)`;
@popover-text-suffix-edge: ~`pxtorem(12)`;
@popover-mask-background: @mask-background;
@load-more-font-size: ~`pxtorem(14)`;
@dark-load-more-text-color: @dark-sub-info-font-color;
@load-more-text-color: @sub-info-font-color;
@dark-cell-text-color: @dark-sub-info-font-color;
@cell-text-color: @sub-info-font-color;
@dark-cell-label-color: @dark-font-color;
@cell-label-color: @font-color;
@dark-cell-label-icon-color: @dark-sub-font-color;
@cell-label-icon-color: @sub-font-color;
@dark-cell-desc-color: @dark-sub-info-font-color;
@cell-desc-color: @sub-info-font-color;
@cell-desc-font-size: ~`pxtorem(14)`;
@cell-desc-margin-top: ~`pxtorem(2)`;
@cell-content-font-size: ~`pxtorem(14)`;
@dark-cell-arrow-color: @dark-disabled-color;
@cell-arrow-color: @disabled-color;
@cell-arrow-gutter: ~`pxtorem(8)`;
@cell-arrow-font-size: ~`pxtorem(12)`;
@dark-cell-background-color: @dark-container-background-color;
@cell-background-color: @container-background-color;
@cell-font-size: ~`pxtorem(16)`;
@cell-horizontal-padding: ~`pxtorem(16)`;
@cell-item-height: ~`pxtorem(54)`;
@cell-item-has-desc-height: ~`pxtorem(74)`;
@cell-label-gutter: ~`pxtorem(24)`;
@cell-label-icon-gutter: ~`pxtorem(12)`;
@cell-label-icon-font-size: ~`pxtorem(20)`;
@cell-extra-font-size: ~`pxtorem(14)`;
@cell-extra-line-height: ~`pxtorem(20)`;
@cell-extra-padding: ~`pxtorem(12)` ~`pxtorem(16)`;
@tag-font-size: ~`pxtorem(12)`;
@tag-icon-font-size: ~`pxtorem(12)`;
@tag-icon-margin-right: ~`pxtorem(2)`;
@tag-icon-close-margin-left: ~`pxtorem(4)`;
@tag-small-size-height: ~`pxtorem(18)`;
@tag-small-size-padding: ~`pxtorem(4)`;
@tag-medium-size-height: ~`pxtorem(20)`;
@tag-medium-size-padding: ~`pxtorem(4)`;
@tag-large-size-height: ~`pxtorem(24)`;
@tag-large-size-padding: ~`pxtorem(6)`;
@tag-filleted-padding: ~`pxtorem(8)`;
@tag-border-radius: 2PX;
@dark-tag-primary-color: @dark-primary-color;
@tag-primary-color: @primary-color;
@dark-tag-primary-background-color: @dark-lighter-primary-color;
@tag-primary-background-color: @lighter-primary-color;
@dark-tag-primary-border-color: @dark-primary-color;
@tag-primary-border-color: @primary-color;
@dark-tag-hollow-color: @dark-primary-color;
@tag-hollow-color: @primary-color;
@dark-tag-hollow-border-color: @dark-primary-color;
@tag-hollow-border-color: @primary-color;
@dark-tag-solid-color: @dark-mask-content-color;
@tag-solid-color: @mask-content-color;
@dark-tag-solid-background-color: @dark-primary-color;
@tag-solid-background-color: @primary-color;
@dark-tag-solid-border-color: @dark-primary-color;
@tag-solid-border-color: @primary-color;
@tag-list-horizontal-gutter: ~`pxtorem(8)`;
@tag-list-vertical-gutter: 0;
@dark-tag-list-add-border-color: @dark-line-color;
@tag-list-add-border-color: @line-color;
@dark-tag-list-add-background: @dark-lighter-line-color;
@tag-list-add-background: @lighter-line-color;
@dark-tag-list-add-color: @dark-sub-font-color;
@tag-list-add-color: @sub-font-color;
@image-preview-mask-background: rgba(0, 0, 0, 0.9);
@image-preview-indicator-font-size: ~`pxtorem(14)`;
@image-preview-indicator-padding: ~`pxtorem(12)` ~`pxtorem(20)`;
@image-preview-indicator-background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3));
@image-preview-thumb-transition: all cubic-bezier(0.34, 0.69, 0.1, 1);
@dropdown-menu-padding: ~`pxtorem(15)`;
@dropdown-menu-font-size: ~`pxtorem(16)`;
@dropdown-menu-line-height: ~`pxtorem(22)`;
@dark-dropdown-menu-color: @dark-font-color;
@dropdown-menu-color: @font-color;
@dark-dropdown-menu-selected-color: @dark-primary-color;
@dropdown-menu-selected-color: @primary-color;
@dark-dropdown-menu-disabled-color: @dark-disabled-color;
@dropdown-menu-disabled-color: @disabled-color;
@dark-dropdown-menu-tip-color: @dark-sub-info-font-color;
@dropdown-menu-tip-color: @sub-info-font-color;
@dropdown-menu-tip-min-width: ~`pxtorem(18)`;
@dropdown-menu-tip-padding-right: ~`pxtorem(16)`;
@dropdown-menu-label-max-width: ~`pxtorem(96)`;
@dropdown-menu-icon-size: 12PX;
@dark-dropdown-menu-icon-color: @dark-disabled-color;
@dropdown-menu-icon-color: @disabled-color;
@dark-dropdown-menu-icon-selected-color: @dark-primary-color;
@dropdown-menu-icon-selected-color: @primary-color;
@dropdown-menu-icon-margin-left: 4PX;
@dark-dropdown-options-background-color: @dark-container-background-color;
@dropdown-options-background-color: @container-background-color;
@dropdown-options-item-padding: ~`pxtorem(16)`;
@dropdown-options-item-font-size: ~`pxtorem(16)`;
@dropdown-options-item-line-height: ~`pxtorem(22)`;
@dark-dropdown-options-item-color: @dark-font-color;
@dropdown-options-item-color: @font-color;
@dark-dropdown-options-item-selected-color: @dark-primary-color;
@dropdown-options-item-selected-color: @primary-color;
@dark-dropdown-options-item-disabled-color: @dark-disabled-color;
@dropdown-options-item-disabled-color: @disabled-color;
@dropdown-options-item-icon-right: ~`pxtorem(16)`;
@dropdown-mask-background-color: @mask-background;
@dropdown-multi-rows-options-gutter: ~`pxtorem(12)`;
@dropdown-multi-rows-options-item-padding: ~`pxtorem(8)`;
@dropdown-multi-rows-options-item-font-size: ~`pxtorem(14)`;
@dropdown-multi-rows-options-item-line-height: ~`pxtorem(20)`;
@dark-dropdown-multi-rows-options-item-color: @dark-sub-font-color;
@dropdown-multi-rows-options-item-color: @sub-font-color;
@dropdown-multi-rows-options-item-border-radius: 2PX;
@dark-dropdown-multi-rows-options-item-background: @dark-card-background-color;
@dropdown-multi-rows-options-item-background: @card-background-color;
@dark-dropdown-multi-rows-options-item-selected-background: @dark-lighter-primary-color;
@dropdown-multi-rows-options-item-selected-background: @lighter-primary-color;
@dark-dropdown-multi-rows-options-item-selected-color: @dark-primary-color;
@dropdown-multi-rows-options-item-selected-color: @primary-color;
@dropdown-multi-rows-options-container-padding: ~`pxtorem(16)`;
@dropdown-multi-rows-options-container-margin: 0 ~`pxtorem(-12)` ~`pxtorem(-12)` 0;
@dark-collapse-disabled-header-color: @dark-disabled-color;
@collapse-disabled-header-color: @disabled-color;
@dark-collapse-header-background: @dark-container-background-color;
@collapse-header-background: @container-background-color;
@collapse-header-height: ~`pxtorem(54)`;
@collapse-header-font-size: ~`pxtorem(16)`;
@collapse-header-margin-left: ~`pxtorem(16)`;
@collapse-header-padding: ~`pxtorem(16)` ~`pxtorem(16)` ~`pxtorem(16)` 0;
@dark-collapse-header-color: @dark-font-color;
@collapse-header-color: @font-color;
@collapse-header-line-height: ~`pxtorem(22)`;
@dark-collapse-header-icon-color: @dark-disabled-color;
@collapse-header-icon-color: @disabled-color;
@collapse-content-padding: ~`pxtorem(12)` ~`pxtorem(16)`;
@collapse-content-font-size: ~`pxtorem(14)`;
@dark-collapse-content-color: @dark-sub-info-font-color;
@collapse-content-color: @sub-info-font-color;
@collapse-content-line-height: ~`pxtorem(22)`;
@dark-pull-refresh-label-background-color: @dark-card-background-color;
@pull-refresh-label-background-color: @card-background-color;
@pull-refresh-label-font-size: ~`pxtorem(12)`;
@dark-pull-refresh-content-background-color: @dark-background-color;
@pull-refresh-content-background-color: @background-color;
@dark-pull-refresh-label-text-color: @dark-sub-info-font-color;
@pull-refresh-label-text-color: @sub-info-font-color;
@dark-pull-refresh-label-loading-color: @dark-sub-info-font-color;
@pull-refresh-label-loading-color: @sub-info-font-color;
@slider-padding: ~`pxtorem(11)` ~`pxtorem(16)`;
@slider-mask-padding: ~`pxtorem(15)`;
@slider-has-mark-padding-bottom: ~`pxtorem(35)`;
@slider-label-font-size: ~`pxtorem(16)`;
@slider-label-gutter: ~`pxtorem(12)`;
@dark-slider-text-color: @dark-sub-info-font-color;
@slider-text-color: @sub-info-font-color;
@dark-slider-line-color: @dark-line-color;
@slider-line-color: @line-color;
@slider-line-border-radius: ~`pxtorem(4)`;
@dark-slider-line-activated-color: @dark-primary-color;
@slider-line-activated-color: @primary-color;
@dark-slider-line-disabled-color: @dark-primary-disabled-color;
@slider-line-disabled-color: @primary-disabled-color;
@slider-thumb-width: ~`pxtorem(24)`;
@slider-thumb-height: ~`pxtorem(24)`;
@slider-thumb-border-radius: 50%;
@slider-thumb-box-shadow: 0 2PX 8PX rgba(0, 0, 0, .1);
@dark-slider-thumb-background: @dark-mask-content-background;
@slider-thumb-background: @mask-content-background;
@slider-popover-arrow-size: 6PX;
@slider-popover-font-size: ~`pxtorem(12)`;
@slider-popover-line-height: ~`pxtorem(17)`;
@slider-popover-gutter: ~`pxtorem(14)`;
@slider-mark-width: 6PX;
@slider-mark-height: 6PX;
@slider-mark-border-radius: 50%;
@slider-mark-label-font-size: ~`pxtorem(14)`;
@slider-mark-label-line-height: ~`pxtorem(20)`;
@slider-horizontal-mark-label-top: ~`pxtorem(19)`;
@slider-vertical-mark-label-right: ~`pxtorem(13)`;
@dark-swipe-load-label-background: @dark-card-background-color;
@swipe-load-label-background: @card-background-color;
@swipe-load-label-border-radius: 50%;
@swipe-load-label-text-margin-left: ~`pxtorem(20)`;
@swipe-load-label-text-width: ~`pxtorem(20)`;
@dark-swipe-load-label-text-color: @dark-font-color;
@swipe-load-label-text-color: @font-color;
@swipe-load-label-text-font-size: ~`pxtorem(12)`;
@notice-bar-wrapper-padding: 0 ~`pxtorem(16)`;
@notice-bar-background: #FFF7E8;
@dark-notice-bar-background: #4D1B00;
@dark-notice-bar-color: @dark-warning-color;
@notice-bar-color: @warning-color;
@notice-bar-gradient-background: linear-gradient(to right, #fff7e8, rgba(255, 247, 232, 0));
@dark-notice-bar-gradient-background: linear-gradient(to right, #4D1B00, rgba(77, 27, 0, 0));
@notice-bar-line-height: ~`pxtorem(20)`;
@notice-bar-text-font-size: ~`pxtorem(14)`;
@notice-bar-icon-font-size: 16PX;
@notice-bar-single-line-height: ~`pxtorem(36)`;
@notice-bar-vertical-padding: ~`pxtorem(8)`;
@notice-bar-horizontal-padding: ~`pxtorem(8)`;
@notice-bar-gradient-width: ~`pxtorem(8)`;
@dark-notify-success-background: @dark-success-color;
@notify-success-background: @success-color;
@dark-notify-error-background: @dark-danger-color;
@notify-error-background: @danger-color;
@dark-notify-warn-background: @dark-warning-color;
@notify-warn-background: @warning-color;
@dark-notify-font-color: @dark-mask-content-color;
@notify-font-color: @mask-content-color;
@dark-notify-info-font-color: @dark-primary-color;
@notify-info-font-color: @primary-color;
@notify-font-size: ~`pxtorem(14)`;
@notify-min-height: ~`pxtorem(36)`;
@steps-padding: ~`pxtorem(16)` 0;
@steps-tail-border-radius: 2PX;
@steps-tail-horizontal-gutter: 18PX;
@steps-tail-vertical-gutter: 14PX;
@steps-tail-horizontal-padding: 0 @steps-tail-horizontal-gutter;
@steps-tail-vertical-padding: @steps-tail-vertical-gutter 0;
@steps-tail-horizontal-left: 9PX;
@steps-tail-vertical-top: 9PX;
@steps-tail-standard-size: 1PX;
@dark-steps-tail-standard-background: @dark-line-color;
@steps-tail-standard-background: @line-color;
@dark-steps-tail-finish-background: @dark-primary-color;
@steps-tail-finish-background: @primary-color;
@dark-steps-finish-icon-num-background: @dark-lighter-primary-color;
@steps-finish-icon-num-background: @lighter-primary-color;
@dark-steps-process-icon-num-background: @dark-primary-color;
@steps-process-icon-num-background: @primary-color;
@dark-steps-wait-icon-num-background: @dark-lighter-line-color;
@steps-wait-icon-num-background: @lighter-line-color;
@dark-steps-error-icon-num-background: @dark-danger-color;
@steps-error-icon-num-background: @danger-color;
@steps-icon-svg-standard-font-size: 10PX;
@dark-steps-finish-icon-svg-color: @dark-primary-color;
@steps-finish-icon-svg-color: @primary-color;
@dark-steps-error-icon-svg-color: @dark-mask-content-color;
@steps-error-icon-svg-color: @mask-content-color;
@steps-error-icon-svg-font-size: 8PX;
@steps-icon-num-font-size: 12PX;
@steps-icon-num-line-height: 18PX;
@dark-steps-icon-num-color: @dark-sub-info-font-color;
@steps-icon-num-color: @sub-info-font-color;
@dark-steps-process-icon-num-color: @dark-mask-content-color;
@steps-process-icon-num-color: @mask-content-color;
@dark-steps-finish-dot-border-color: @dark-primary-color;
@steps-finish-dot-border-color: @primary-color;
@dark-steps-process-dot-background: @dark-primary-color;
@steps-process-dot-background: @primary-color;
@dark-steps-wait-dot-border-color: @dark-sub-info-font-color;
@steps-wait-dot-border-color: @sub-info-font-color;
@dark-steps-finish-title-color: @dark-font-color;
@steps-finish-title-color: @font-color;
@dark-steps-error-title-color: @dark-danger-color;
@steps-error-title-color: @danger-color;
@dark-steps-process-title-color: @dark-primary-color;
@steps-process-title-color: @primary-color;
@dark-steps-wait-title-color: @dark-sub-info-font-color;
@steps-wait-title-color: @sub-info-font-color;
@dark-steps-description-color: @dark-sub-font-color;
@steps-description-color: @sub-font-color;
@dark-steps-wait-description-color: @dark-sub-info-font-color;
@steps-wait-description-color: @sub-info-font-color;
@steps-icon-width: 18PX;
@steps-icon-height: 18PX;
@steps-dot-width: 8PX;
@steps-dot-height: 8PX;
@steps-dot-border-width: 1.5PX;
@steps-horizontal-content-margin-top: ~`pxtorem(5)`;
@steps-vertical-content-margin-left: ~`pxtorem(13)`;
@steps-vertical-content-padding-bottom: ~`pxtorem(25)`;
@steps-title-font-size: ~`pxtorem(14)`;
@steps-title-line-height: ~`pxtorem(20)`;
@steps-description-font-size: ~`pxtorem(12)`;
@steps-description-line-height: ~`pxtorem(18)`;
@steps-description-margin-top: ~`pxtorem(2)`;
@steps-vertical-padding-bottom: 0;
@steps-vertical-padding-left: ~`pxtorem(20)`;
@dark-steps-process-with-config-item-icon-color: @dark-mask-content-color;
@steps-process-with-config-item-icon-color: @mask-content-color;
@swipe-action-open-transition: cubic-bezier(0.2, 0.8, 0.32, 1.28);
@swipe-action-close-transition: cubic-bezier(0.34, 0.69, 0.1, 1);
@swipe-action-info-padding: ~`pxtorem(16)`;
@swipe-action-info-bounce-buffer: ~`pxtorem(30)`;
@swipe-action-text-font-size: ~`pxtorem(16)`;
@swipe-action-text-line-height: ~`pxtorem(22)`;
@dark-swipe-action-text-color: @dark-mask-content-color;
@swipe-action-text-color: @mask-content-color;
@swipe-action-icon-width: ~`pxtorem(20)`;
@swipe-action-icon-height: ~`pxtorem(20)`;
@swipe-action-icon-margin-right: ~`pxtorem(4)`;
@dark-badge-background-color: @dark-danger-color;
@badge-background-color: @danger-color;
@dark-badge-text-color: @dark-mask-content-color;
@badge-text-color: @mask-content-color;
@badge-font-size: 12PX;
@badge-dot-width: 8PX;
@badge-text-width: 16PX;
@badge-text-padding: 4PX;
@badge-text-deviation: -8PX;
@badge-dot-deviation: -4PX;
@badge-border-radius: 100PX;
@dark-badge-border-color: @dark-background-color;
@badge-border-color: @background-color;
@circle-progress-font-size: ~`pxtorem(14)`;
@dark-circle-progress-primary-color: @dark-primary-color;
@circle-progress-primary-color: @primary-color;
@dark-circle-progress-track-color: @dark-lighter-line-color;
@circle-progress-track-color: @lighter-line-color;
@dark-circle-progress-disabled-color: @dark-disabled-color;
@circle-progress-disabled-color: @disabled-color;
@dark-circle-progress-mini-track-color: @dark-lighter-primary-color;
@circle-progress-mini-track-color: @lighter-primary-color;
@circle-progress-linear-gradient-start-color: #4776E6;
@circle-progress-linear-gradient-end-color: #14CAFF;
@circle-progress-linear-gradient-text-color: #3C89EC;
@dark-progress-primary-color: @dark-primary-color;
@progress-primary-color: @primary-color;
@dark-progress-track-color: @dark-lighter-line-color;
@progress-track-color: @lighter-line-color;
@dark-progress-disabled-color: @dark-disabled-color;
@progress-disabled-color: @disabled-color;
@dark-progress-disabled-text-color: @dark-sub-info-font-color;
@progress-disabled-text-color: @sub-info-font-color;
@progress-linear-gradient-start-color: #4776E6;
@progress-linear-gradient-end-color: #14CAFF;
@progress-linear-gradient-text-color: #3C89EC;
@progress-nav-track-color: transparent;
@progress-nav-track-height: 2PX;
@progress-track-height: 4PX;
@progress-inner-track-height: 18PX;
@pagination-padding: ~`pxtorem(11)` ~`pxtorem(16)`;
@pagination-center-field-gutter: ~`pxtorem(24)`;
@pagination-field-font-size: ~`pxtorem(15)`;
@pagination-field-line-height: ~`pxtorem(22)`;
@pagination-field-button-min-height: ~`pxtorem(32)`;
@pagination-field-button-border-radius: ~`pxtorem(2)`;
@pagination-field-button-padding: ~`pxtorem(6)` ~`pxtorem(16)`;
@pagination-field-btn-text-font-size: ~`pxtorem(14)`;
@pagination-field-btn-icon-text-gutter: ~`pxtorem(11)`;
@dark-pagination-field-primary-background: @dark-primary-color;
@pagination-field-primary-background: @primary-color;
@dark-pagination-field-primary-text-color: @dark-mask-content-color;
@pagination-field-primary-text-color: @mask-content-color;
@dark-pagination-field-default-background: @dark-card-background-color;
@pagination-field-default-background: @card-background-color;
@dark-pagination-field-default-text-color: @dark-font-color;
@pagination-field-default-text-color: @font-color;
@dark-pagination-field-disabled-background: @dark-card-background-color;
@pagination-field-disabled-background: @card-background-color;
@dark-pagination-field-disabled-text-color: @dark-disabled-color;
@pagination-field-disabled-text-color: @disabled-color;
@dark-pagination-field-text-color: @dark-font-color;
@pagination-field-text-color: @font-color;
@dark-pagination-field-text-primary-text-color: @dark-primary-color;
@pagination-field-text-primary-text-color: @primary-color;
@pagination-item-font-size: ~`pxtorem(18)`;
@pagination-item-line-height: ~`pxtorem(22)`;
@dark-pagination-item-primary-text-color: @dark-primary-color;
@pagination-item-primary-text-color: @primary-color;
@dark-pagination-item-default-text-color: @dark-font-color;
@pagination-item-default-text-color: @font-color;
@dark-progress-text-inner-color: @dark-mask-content-color;
@progress-text-inner-color: @mask-content-color;
@progress-text-gutter: ~`pxtorem(8)`;
@progress-text-font-size: ~`pxtorem(14)`;
@progress-text-follow-font-size: ~`pxtorem(13)`;
@progress-text-follow-border-radius: ~`pxtorem(20)`;
@progress-text-follow-width: ~`pxtorem(36)`;
@progress-text-follow-height: ~`pxtorem(20)`;
@transition-fade-duration: 300ms;
@rate-icon-size: 24PX;
@rate-icon-offset: 6PX;
@rate-icon-active-color: #FFB400;
@dark-rate-icon-normal-color: @dark-line-color;
@rate-icon-normal-color: @line-color;
@dark-rate-icon-disabled-active-color: @dark-disabled-color;
@rate-icon-disabled-active-color: @disabled-color;
@count-down-font-size: ~`pxtorem(16)`;
@count-down-line-height: ~`pxtorem(22)`;
@dark-count-down-color: @dark-font-color;
@count-down-color: @font-color;
@grid-icon-width: ~`pxtorem(32)`;
@grid-icon-height: ~`pxtorem(32)`;
@grid-vertical-text-margin-top: ~`pxtorem(8)`;
@grid-vertical-title-font-size: ~`pxtorem(16)`;
@grid-vertical-title-line-height: ~`pxtorem(20)`;
@grid-vertical-content-margin-top: ~`pxtorem(2)`;
@grid-vertical-content-font-size: ~`pxtorem(12)`;
@grid-vertical-content-line-height: ~`pxtorem(16)`;
@grid-horizontal-text-margin-left: ~`pxtorem(12)`;
@grid-horizontal-content-margin-top: ~`pxtorem(0)`;
@dark-grid-border-color: @dark-line-color;
@grid-border-color: @line-color;
@grid-border-size: 66.66%;
@action-sheet-item-height: ~`pxtorem(54)`;
@action-sheet-item-font-size: ~`pxtorem(16)`;
@action-sheet-border-radius: ~`pxtorem(8)`;
@dark-action-sheet-cancel-border-color: @dark-lighter-line-color;
@action-sheet-cancel-border-color: @lighter-line-color;
@action-sheet-cancel-border-width: ~`pxtorem(8)`;
@action-sheet-header-padding: ~`pxtorem(16)`;
@action-sheet-title-font-size: ~`pxtorem(16)`;
@action-sheet-sub-title-font-size: ~`pxtorem(14)`;
@search-bar-padding: ~`pxtorem(16)`;
@dark-search-bar-background-color: @dark-background-color;
@search-bar-background-color: @background-color;
@search-bar-square-shape-border-radius: ~`pxtorem(2)`;
@search-bar-round-shape-border-radius: ~`pxtorem(9999)`;
@search-bar-input-wrapper-height: ~`pxtorem(36)`;
@search-bar-input-wrapper-padding: ~`pxtorem(8)` ~`pxtorem(14)`;
@dark-search-bar-input-wrapper-background-color: @dark-lighter-line-color;
@search-bar-input-wrapper-background-color: @lighter-line-color;
@search-bar-input-wrapper-font-size: ~`pxtorem(14)`;
@search-bar-input-height: ~`pxtorem(20)`;
@dark-search-bar-input-caret-color: @dark-primary-color;
@search-bar-input-caret-color: @primary-color;
@dark-search-bar-input-placeholder-color: @dark-disabled-color;
@search-bar-input-placeholder-color: @disabled-color;
@search-bar-prefix-margin-right: ~`pxtorem(9)`;
@dark-search-bar-clear-icon-color: @dark-disabled-color;
@search-bar-clear-icon-color: @disabled-color;
@search-bar-clear-icon-font-size: 16PX;
@search-bar-clear-icon-padding-left: ~`pxtorem(16)`;
@dark-search-bar-search-icon-color: @dark-sub-info-font-color;
@search-bar-search-icon-color: @sub-info-font-color;
@search-bar-search-icon-font-size: ~`pxtorem(16)`;
@dark-search-bar-cancel-btn-color: @dark-primary-color;
@search-bar-cancel-btn-color: @primary-color;
@search-bar-cancel-btn-font-size: ~`pxtorem(15)`;
@search-bar-cancel-btn-margin-left: ~`pxtorem(16)`;
@dark-search-bar-association-background-color: @dark-container-background-color;
@search-bar-association-background-color: @container-background-color;
@search-bar-association-item-height: ~`pxtorem(52)`;
@search-bar-association-item-padding: ~`pxtorem(16)`;
@search-bar-association-item-font-size: ~`pxtorem(15)`;
@dark-search-bar-association-item-color: @dark-font-color;
@search-bar-association-item-color: @font-color;
@dark-search-bar-association-item-highlight-color: @dark-primary-color;
@search-bar-association-item-highlight-color: @primary-color;
@image-picker-font-size: ~`pxtorem(14)`;
@image-picker-disabled-opacity: 0.7;
@image-picker-border-radius: ~`pxtorem(2)`;
@dark-image-picker-add-background: @dark-card-background-color;
@image-picker-add-background: @card-background-color;
@image-picker-add-icon-font-size: ~`pxtorem(30)`;
@dark-image-picker-add-icon-color: @dark-disabled-color;
@image-picker-add-icon-color: @disabled-color;
@image-picker-add-text-font-size: ~`pxtorem(12)`;
@dark-image-picker-add-text-color: @dark-sub-info-font-color;
@image-picker-add-text-color: @sub-info-font-color;
@dark-image-picker-error-color: @dark-mask-content-color;
@image-picker-error-color: @mask-content-color;
@image-picker-error-background: rgba(0, 0, 0, 0.5);
@dark-image-picker-close-color: @dark-mask-content-color;
@image-picker-close-color: @mask-content-color;
@image-picker-close-font-size: ~`pxtorem(12)`;
@image-picker-close-width: ~`pxtorem(18)`;
@image-picker-close-height: ~`pxtorem(18)`;
@image-picker-close-background: rgba(0, 0, 0, 0.3);
@image-picker-close-border-radius: 0 ~`pxtorem(2)`;
@dark-index-bar-background: @dark-background-color;
@index-bar-background: @background-color;
@dark-index-bar-group-active-color: @dark-primary-color;
@index-bar-group-active-color: @primary-color;
@index-bar-group-left-spacing: ~`pxtorem(16)`;
@index-bar-group-title-height: ~`pxtorem(24)`;
@index-bar-group-title-background: #f7f8fa;
@dark-index-bar-group-title-background: #2e2e30;
@dark-index-bar-group-title-font-color: @dark-sub-info-font-color;
@index-bar-group-title-font-color: @sub-info-font-color;
@index-bar-group-title-font-size: ~`pxtorem(14)`;
@index-bar-group-item-height: ~`pxtorem(54)`;
@index-bar-group-item-font-size: ~`pxtorem(16)`;
@dark-index-bar-sidebar-active-color: @dark-primary-color;
@index-bar-sidebar-active-color: @primary-color;
@index-bar-sidebar-item-font-size: ~`pxtorem(10)`;
@index-bar-sidebar-item-line-height: ~`pxtorem(14)`;
@index-bar-sidebar-item-padding: ~`pxtorem(2)` ~`pxtorem(8)`;
@index-bar-sidebar-item-width: ~`pxtorem(10)`;
@index-bar-sidebar-sweat-padding: 0 ~`pxtorem(8)`;
@index-bar-sidebar-sweat-background: #333333;
@dark-index-bar-sidebar-sweat-color: @dark-mask-content-color;
@index-bar-sidebar-sweat-color: @mask-content-color;
@index-bar-sidebar-sweat-right: ~`pxtorem(36)`;
@index-bar-sidebar-sweat-font-size: ~`pxtorem(24)`;
@index-bar-sidebar-sweat-radius: ~`pxtorem(50)`;
@index-bar-sidebar-sweat-triangle-position: ~`pxtorem(-28)`;
@index-bar-sidebar-sweat-triangle-border: ~`pxtorem(18)` solid transparent;
@index-bar-sidebar-toast-background: rgba(0, 0, 0, 0.8);
@dark-index-bar-sidebar-toast-color: @dark-mask-content-color;
@index-bar-sidebar-toast-color: @mask-content-color;
@index-bar-sidebar-toast-height: ~`pxtorem(48)`;
@index-bar-sidebar-toast-radius: ~`pxtorem(4)`;
@index-bar-sidebar-toast-padding: 0 ~`pxtorem(8)`;
@index-bar-sidebar-toast-font-size: ~`pxtorem(24)`;
@stepper-width: ~`pxtorem(98)`;
@stepper-font-size: ~`pxtorem(14)`;
@dark-stepper-square-border-color: @dark-lighter-line-color;
@stepper-square-border-color: @lighter-line-color;
@stepper-square-border-radius: ~`pxtorem(2)`;
@stepper-square-background-color: transparent;
@stepper-round-button-border-radius: 50%;
@stepper-round-input-background-color: transparent;
@stepper-button-size: ~`pxtorem(28)`;
@stepper-button-icon-size: ~`pxtorem(10)`;
@dark-stepper-default-background-color: @dark-card-background-color;
@stepper-default-background-color: @card-background-color;
@dark-stepper-content-color: @dark-font-color;
@stepper-content-color: @font-color;
@dark-stepper-disable-color: @dark-disabled-color;
@stepper-disable-color: @disabled-color;
@stepper-input-width: ~`pxtorem(40)`;
@stepper-input-height: ~`pxtorem(28)`;
@stepper-input-margin: 0 1PX;
@form-item-label-item-font-size: ~`pxtorem(16)`;
@form-item-label-item-line-height: ~`pxtorem(54)`;
@dark-form-item-label-item-color: @dark-font-color;
@form-item-label-item-color: @font-color;
@form-item-label-item-gutter: ~`pxtorem(16)`;
@form-item-label-item-width: ~`pxtorem(96)`;
@dark-form-item-border-divider-color: @dark-line-color;
@form-item-border-divider-color: @line-color;
@dark-form-item-label-item-required-asterisk-color: @dark-danger-color;
@form-item-label-item-required-asterisk-color: @danger-color;
@dark-form-item-error-message-color: @dark-danger-color;
@form-item-error-message-color: @danger-color;
@dark-form-item-warning-message-color: @dark-warning-color;
@form-item-warning-message-color: @warning-color;
@time-line-dot-width: ~`pxtorem(9)`;
@dark-time-line-dot-border-color: @dark-primary-color;
@time-line-dot-border-color: @primary-color;
@dark-time-line-dot-background-color: @dark-background-color;
@time-line-dot-background-color: @background-color;
@time-line-axis-width: ~`pxtorem(1)`;
@dark-time-line-axis-color: @dark-line-color;
@time-line-axis-color: @line-color;
@time-line-label-font-size: ~`pxtorem(12)`;
@dark-time-line-label-color: @dark-sub-info-font-color;
@time-line-label-color: @sub-info-font-color;
@time-line-content-margin-top: ~`pxtorem(7)`;
@time-line-content-margin-bottom: ~`pxtorem(19)`;
@time-line-content-margin-left: ~`pxtorem(8)`;
@time-line-content-border-radius: ~`pxtorem(4)`;
@time-line-content-font-size: ~`pxtorem(16)`;
@dark-time-line-content-background-color: @dark-line-color;
@time-line-content-background-color: @line-color;
@dark-time-line-content-color: @dark-font-color;
@time-line-content-color: @font-color;
@keyboard-background: #f2f3f5;
@dark-keyboard-background: #232324;
@keyboard-content-padding: ~`pxtorem(8)`;
@keyboard-unified-margin: ~`pxtorem(8)`;
@dark-keyboard-confirm-key-background: @dark-primary-color;
@keyboard-confirm-key-background: @primary-color;
@dark-keyboard-confirm-key-color: @dark-mask-content-color;
@keyboard-confirm-key-color: @mask-content-color;
@keyboard-confirm-key-font-size: ~`pxtorem(18)`;
@keyboard-key-font-weight: 500;
@keyboard-key-font-size: ~`pxtorem(22)`;
@keyboard-key-icon-size: ~`pxtorem(26)`;
@keyboard-key-line-height: ~`pxtorem(30)`;
@keyboard-key-background: #ffffff;
@dark-keyboard-key-background: #2e2e30;
@dark-keyboard-key-active-background: @dark-line-color;
@keyboard-key-active-background: @line-color;
@keyboard-key-border-radius: ~`pxtorem(4)`;
@keyboard-key-height: ~`pxtorem(48)`;
@dark-keyboard-key-color: @dark-font-color;
@keyboard-key-color: @font-color;
@divider-line-thickness: 1PX;
@dark-divider-line-color: @dark-line-color;
@divider-line-color: @line-color;
@divider-content-font-size: ~`pxtorem(14)`;
@dark-divider-content-font-color: @dark-sub-font-color;
@divider-content-font-color: @sub-font-color;
@divider-left-width: ~`pxtorem(28)`;
@divider-right-width: ~`pxtorem(28)`;
@divider-content-padding: ~`pxtorem(12)`;
@divider-padding: ~`pxtorem(16)`;
@skeleton-border-radius: ~`pxtorem(0)`;
@dark-skeleton-background-color: @dark-lighter-line-color;
@skeleton-background-color: @lighter-line-color;
@skeleton-gradient-animation-color: rgba(255, 255, 255, 0.6);
@dark-skeleton-gradient-animation-color: hsla(0, 0%, 100%, 0.08);
@skeleton-breath-opacity: 0.4;
@skeleton-gradient-animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
@skeleton-gradient-animation-duration: 1.5s;
@skeleton-breath-animation-duration: 1.5s;
@skeleton-title-height: ~`pxtorem(16)`;
@skeleton-paragraph-height: ~`pxtorem(16)`;
@skeleton-paragraph-margin-top: ~`pxtorem(12)`;
@skeleton-avatar-size: ~`pxtorem(32)`;
@skeleton-grid-icon-size: ~`pxtorem(32)`;
@skeleton-grid-text-width: ~`pxtorem(64)`;
@skeleton-grid-text-height: ~`pxtorem(16)`;
@skeleton-medium-gutter: ~`pxtorem(8)`;
@skeleton-large-gutter: ~`pxtorem(20)`;
@uploader-item-height: ~`pxtorem(36)`;
@uploader-item-margin-top: ~`pxtorem(16)`;
@uploader-item-padding: 0 ~`pxtorem(12)`;
@dark-uploader-item-background-color: @dark-card-background-color;
@uploader-item-background-color: @card-background-color;
@uploader-file-icon-font-size: ~`pxtorem(16)`;
@uploader-file-icon-margin-right: ~`pxtorem(12)`;
@dark-uploader-file-icon-color: @dark-sub-font-color;
@uploader-file-icon-color: @sub-font-color;
@uploader-item-text-font-size: ~`pxtorem(14)`;
@uploader-item-border-radius: ~`pxtorem(2)`;
@dark-uploader-item-text-color: @dark-font-color;
@uploader-item-text-color: @font-color;
@uploader-loaded-icon-font-size: ~`pxtorem(16)`;
@dark-uploader-loaded-icon-color: @dark-success-color;
@uploader-loaded-icon-color: @success-color;
@uploader-error-text-font-size: ~`pxtorem(12)`;
@dark-uploader-error-text-color: @dark-primary-color;
@uploader-error-text-color: @primary-color;
@uploader-delete-icon-font-size: ~`pxtorem(12)`;
@uploader-delete-icon-padding-left: ~`pxtorem(12)`;
@dark-uploader-delete-icon-color: @dark-sub-font-color;
@uploader-delete-icon-color: @sub-font-color;
@dark-uploader-disabled-delete-icon-color: @dark-disabled-color;
@uploader-disabled-delete-icon-color: @disabled-color;
@dark-uploader-item-text-error-color: @dark-danger-color;
@uploader-item-text-error-color: @danger-color;

