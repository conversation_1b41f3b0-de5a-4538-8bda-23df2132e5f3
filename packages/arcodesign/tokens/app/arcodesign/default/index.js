"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
exports.getRem = getRem;

function getRem(px, baseFontSize) {
  var num = Math.round(px / Number(baseFontSize) * 1000000) / 1000000;
  return num ? "".concat(num, "rem") : num;
}

var tokens = {
  "prefix": "arco",
  "base-font-size": "50",
  "arco-dark-mode-selector": ".arco-theme-dark",
  "background-color": "#FFFFFF",
  "dark-background-color": "#17171A",
  "container-background-color": "#FFFFFF",
  "dark-container-background-color": "#232324",
  "card-background-color": "#F7F8FA",
  "dark-card-background-color": "hsla(0, 0%, 100%, 0.08)",
  "font-color": "#1d2129",
  "dark-font-color": "#f6f6f6",
  "sub-font-color": "#4e5969",
  "dark-sub-font-color": "#c5c5c5",
  "sub-info-font-color": "#86909c",
  "dark-sub-info-font-color": "#929293",
  "line-color": "#e5e6eb",
  "dark-line-color": "#484849",
  "lighter-line-color": "#f2f3f5",
  "dark-lighter-line-color": "#2e2e30",
  "primary-color": "#165DFF",
  "dark-primary-color": "#3C7EFF",
  "primary-disabled-color": "#94BFFF",
  "dark-primary-disabled-color": "#0E32A6",
  "lighter-primary-color": "#E8F3FF",
  "dark-lighter-primary-color": "#000D4D",
  "danger-color": "#F53F3F",
  "dark-danger-color": "#F76965",
  "warning-color": "#FF7D00",
  "dark-warning-color": "#FF9626",
  "success-color": "#00B42A",
  "dark-success-color": "#27C346",
  "disabled-color": "#c9cdd4",
  "dark-disabled-color": "#5f5f60",
  "mask-background": "rgba(0, 0, 0, 0.6)",
  "mask-content-color": "#FFFFFF",
  "dark-mask-content-color": "rgba(255, 255, 255, 0.9)",
  "mask-content-background": "#FFFFFF",
  "dark-mask-content-background": "#2A2A2B",
  "scroller-buffer": "10PX",
  "full-screen-z-index": "1000",
  "fixed-z-index": "100",
  "popup-mask-background": "var(--mask-background)",
  "dark-popup-content-background": "var(--dark-mask-content-background)",
  "popup-content-background": "var(--mask-content-background)",
  "popup-enter-transition": "all 450ms cubic-bezier(0.34, 0.69, 0.1, 1)",
  "popup-exit-transition": "all 240ms cubic-bezier(0.34, 0.69, 0.1, 1)",
  "dialog-mask-background": "var(--mask-background)",
  "dialog-content-width": "5.4rem",
  "dialog-content-android-width": "5.6rem",
  "dark-dialog-content-background": "var(--dark-mask-content-background)",
  "dialog-content-background": "var(--mask-content-background)",
  "dialog-content-border-radius": "0.16rem",
  "dialog-content-android-border-radius": "0.08rem",
  "dialog-ios-horizontal-padding": "0.32rem",
  "dialog-ios-vertical-padding": "0.4rem",
  "dialog-ios-header-body-gutter": "0.08rem",
  "dialog-android-horizontal-padding": "0.48rem",
  "dialog-android-vertical-padding": "0.4rem",
  "dialog-android-header-body-gutter": "0.24rem",
  "dialog-android-body-footer-gutter": "0.48rem",
  "dark-dialog-body-ios-color": "var(--dark-sub-font-color)",
  "dialog-body-ios-color": "var(--sub-font-color)",
  "dialog-body-ios-font-size": "0.3rem",
  "dialog-body-ios-line-height": "0.44rem",
  "dark-dialog-body-android-color": "var(--dark-sub-font-color)",
  "dialog-body-android-color": "var(--sub-font-color)",
  "dialog-body-android-font-size": "0.3rem",
  "dialog-body-android-line-height": "0.48rem",
  "dark-dialog-header-ios-color": "var(--dark-font-color)",
  "dialog-header-ios-color": "var(--font-color)",
  "dark-dialog-header-android-color": "var(--dark-font-color)",
  "dialog-header-android-color": "var(--font-color)",
  "dialog-header-ios-font-size": "0.34rem",
  "dialog-header-ios-line-height": "0.52rem",
  "dialog-header-android-font-size": "0.34rem",
  "dialog-header-android-line-height": "0.56rem",
  "dark-dialog-footer-ios-color": "var(--dark-primary-color)",
  "dialog-footer-ios-color": "var(--primary-color)",
  "dialog-footer-ios-font-size": "0.32rem",
  "dialog-footer-ios-height": "0.88rem",
  "dialog-footer-android-color": "#1a74ff",
  "dialog-footer-android-font-size": "0.3rem",
  "dialog-footer-android-line-height": "0.4rem",
  "dialog-footer-android-button-gutter": "0.56rem",
  "dark-dialog-button-footer-primary-background": "var(--dark-primary-color)",
  "dialog-button-footer-primary-background": "var(--primary-color)",
  "dark-dialog-button-footer-primary-color": "var(--dark-mask-content-color)",
  "dialog-button-footer-primary-color": "var(--mask-content-color)",
  "dark-dialog-button-footer-color": "var(--dark-sub-info-font-color)",
  "dialog-button-footer-color": "var(--sub-info-font-color)",
  "dialog-button-footer-height": "0.72rem",
  "dialog-button-footer-border-radius": "0.6rem",
  "dialog-button-footer-gutter": "0.16rem",
  "carousel-auto-transition": "cubic-bezier(0.66, 0, 0.34, 1)",
  "carousel-slide-transition": "cubic-bezier(0.32, 0.94, 0.6, 1)",
  "carousel-indicator-background": "rgba(255, 255, 255, 0.5)",
  "dark-carousel-indicator-active-background": "var(--dark-mask-content-color)",
  "carousel-indicator-active-background": "var(--mask-content-color)",
  "dark-carousel-indicator-inverse-background": "var(--dark-line-color)",
  "carousel-indicator-inverse-background": "var(--line-color)",
  "dark-carousel-indicator-active-inverse-background": "var(--dark-primary-color)",
  "carousel-indicator-active-inverse-background": "var(--primary-color)",
  "carousel-indicator-outside-padding": "0.16rem 0 0.1rem",
  "carousel-indicator-position": "0.24rem",
  "carousel-indicator-safe-padding": "0.32rem",
  "carousel-circle-indicator-gutter": "0.16rem",
  "carousel-square-indicator-gutter": "0.16rem",
  "carousel-circle-indicator-size": "6PX",
  "carousel-square-indicator-width": "0.24rem",
  "carousel-square-indicator-height": "3PX",
  "dark-carousel-item-text-color": "var(--dark-mask-content-color)",
  "carousel-item-text-color": "var(--mask-content-color)",
  "carousel-item-text-background": "linear-gradient(180deg, rgba(0, 0, 0, 0) 5.18%, rgba(0, 0, 0, 0.15) 100%)",
  "carousel-item-text-height": "0.64rem",
  "carousel-item-text-padding": "0 0.24rem",
  "carousel-item-text-font-size": "0.32rem",
  "input-height": "1.08rem",
  "dark-input-disabled-color": "var(--dark-disabled-color)",
  "input-disabled-color": "var(--disabled-color)",
  "dark-input-placeholder-color": "var(--dark-disabled-color)",
  "input-placeholder-color": "var(--disabled-color)",
  "dark-input-clear-icon-color": "var(--dark-disabled-color)",
  "input-clear-icon-color": "var(--disabled-color)",
  "input-clear-icon-font-size": "16PX",
  "input-label-gutter": "0.48rem",
  "input-horizontal-padding": "0.32rem",
  "input-vertical-padding": "0.24rem",
  "dark-input-caret-color": "var(--dark-primary-color)",
  "input-caret-color": "var(--primary-color)",
  "input-label-min-width": "1.28rem",
  "input-text-font-size": "0.32rem",
  "input-text-line-height": "0.44rem",
  "textarea-font-size": "0.32rem",
  "textarea-line-height": "0.44rem",
  "textarea-padding": "0.32rem",
  "textarea-has-stat-padding": "0.32rem 0.32rem 0.88rem",
  "dark-textarea-statistic-color": "var(--dark-sub-info-font-color)",
  "textarea-statistic-color": "var(--sub-info-font-color)",
  "textarea-statistic-font-size": "0.28rem",
  "avatar-size-map": "large, medium, small, smaller, ultra-small",
  "avatar-large-size": "1.12rem",
  "avatar-medium-size": "0.96rem",
  "avatar-small-size": "0.8rem",
  "avatar-smaller-size": "0.64rem",
  "avatar-ultra-small-size": "0.48rem",
  "avatar-default-overlap-large-size": "0.56rem",
  "avatar-default-overlap-medium-size": "0.48rem",
  "avatar-default-overlap-small-size": "0.4rem",
  "avatar-default-overlap-smaller-size": "0.32rem",
  "avatar-default-overlap-ultra-small-size": "0.24rem",
  "avatar-background": "#4080FF",
  "dark-avatar-background": "#306FFF",
  "dark-avatar-default-overlap-background": "var(--dark-disabled-color)",
  "avatar-default-overlap-background": "var(--disabled-color)",
  "dark-avatar-text-font-color": "var(--dark-mask-content-color)",
  "avatar-text-font-color": "var(--mask-content-color)",
  "avatar-large-text-font-size": "0.32rem",
  "avatar-medium-text-font-size": "0.32rem",
  "avatar-small-text-font-size": "0.28rem",
  "avatar-smaller-text-font-size": "0.24rem",
  "avatar-ultra-small-text-font-size": "0.2rem",
  "avatar-group-large-size-offset": "-0.24rem",
  "avatar-group-large-size-border": "0.03rem",
  "avatar-group-medium-size-offset": "-0.24rem",
  "avatar-group-medium-size-border": "0.03rem",
  "avatar-group-small-size-offset": "-0.24rem",
  "avatar-group-small-size-border": "0.03rem",
  "avatar-group-smaller-size-offset": "-0.16rem",
  "avatar-group-smaller-size-border": "0.02rem",
  "avatar-group-ultra-small-size-offset": "-0.16rem",
  "avatar-group-ultra-small-size-border": "0.02rem",
  "dark-avatar-group-border-color": "var(--dark-background-color)",
  "avatar-group-border-color": "var(--background-color)",
  "avatar-info-box-large-size": "1.76rem",
  "avatar-info-box-medium-size": "1.6rem",
  "avatar-info-box-small-size": "1.6rem",
  "avatar-info-box-smaller-size": "1.28rem",
  "avatar-info-box-ultra-small-size": "1.12rem",
  "avatar-name-large-font-size": "0.36rem",
  "avatar-name-large-line-height": "0.52rem",
  "avatar-desc-large-font-size": "0.28rem",
  "avatar-desc-large-line-height": "0.4rem",
  "avatar-desc-large-margin-top": "0.04rem",
  "avatar-name-medium-font-size": "0.36rem",
  "avatar-name-medium-line-height": "0.52rem",
  "avatar-desc-medium-font-size": "0.28rem",
  "avatar-desc-medium-line-height": "0.4rem",
  "avatar-desc-medium-margin-top": "0.04rem",
  "avatar-name-small-font-size": "0.32rem",
  "avatar-name-small-line-height": "0.48rem",
  "avatar-desc-small-font-size": "0.24rem",
  "avatar-desc-small-line-height": "0.32rem",
  "avatar-desc-small-margin-top": "0",
  "avatar-name-smaller-font-size": "0.28rem",
  "avatar-name-smaller-line-height": "0.4rem",
  "avatar-desc-smaller-font-size": "0.24rem",
  "avatar-desc-smaller-line-height": "0.32rem",
  "avatar-desc-smaller-margin-top": "0",
  "avatar-name-ultra-small-font-size": "0.26rem",
  "avatar-name-ultra-small-line-height": "0.36rem",
  "avatar-desc-ultra-small-font-size": "0.2rem",
  "avatar-desc-ultra-small-line-height": "0.28rem",
  "avatar-desc-ultra-small-margin-top": "0.04rem",
  "dark-avatar-name-color": "var(--dark-font-color)",
  "avatar-name-color": "var(--font-color)",
  "dark-avatar-desc-color": "var(--dark-sub-info-font-color)",
  "avatar-desc-color": "var(--sub-info-font-color)",
  "button-line-height": "1.2",
  "button-radius": "2PX",
  "button-icon-text-gutter": "0.08rem",
  "dark-button-primary-background": "var(--dark-primary-color)",
  "button-primary-background": "var(--primary-color)",
  "button-primary-clicked-background": "#0E42D2",
  "dark-button-primary-clicked-background": "#689FFF",
  "dark-button-primary-disabled-background": "var(--dark-primary-disabled-color)",
  "button-primary-disabled-background": "var(--primary-disabled-color)",
  "dark-button-primary-text-color": "var(--dark-mask-content-color)",
  "button-primary-text-color": "var(--mask-content-color)",
  "dark-button-primary-disabled-text-color": "var(--dark-lighter-primary-color)",
  "button-primary-disabled-text-color": "var(--lighter-primary-color)",
  "dark-button-default-background": "var(--dark-lighter-primary-color)",
  "button-default-background": "var(--lighter-primary-color)",
  "dark-button-default-clicked-background": "var(--dark-primary-disabled-color)",
  "button-default-clicked-background": "var(--primary-disabled-color)",
  "dark-button-default-disabled-background": "var(--dark-lighter-primary-color)",
  "button-default-disabled-background": "var(--lighter-primary-color)",
  "dark-button-default-text-color": "var(--dark-primary-color)",
  "button-default-text-color": "var(--primary-color)",
  "dark-button-default-disabled-text-color": "var(--dark-primary-disabled-color)",
  "button-default-disabled-text-color": "var(--primary-disabled-color)",
  "button-ghost-background": "transparent",
  "dark-button-ghost-clicked-background": "var(--dark-lighter-primary-color)",
  "button-ghost-clicked-background": "var(--lighter-primary-color)",
  "button-ghost-disabled-background": "transparent",
  "dark-button-ghost-text-color": "var(--dark-primary-color)",
  "button-ghost-text-color": "var(--primary-color)",
  "dark-button-ghost-disabled-text-color": "var(--dark-primary-disabled-color)",
  "button-ghost-disabled-text-color": "var(--primary-disabled-color)",
  "button-huge-padding": "0 0.32rem",
  "button-huge-height": "0.88rem",
  "button-huge-text-size": "0.32rem",
  "button-large-padding": "0 0.32rem",
  "button-large-height": "0.72rem",
  "button-large-text-size": "0.3rem",
  "button-medium-padding": "0 0.32rem",
  "button-medium-height": "0.64rem",
  "button-medium-text-size": "0.28rem",
  "button-small-padding": "0 0.16rem",
  "button-small-height": "0.56rem",
  "button-small-text-size": "0.28rem",
  "button-mini-padding": "0 0.16rem",
  "button-mini-height": "0.48rem",
  "button-mini-text-size": "0.24rem",
  "ellipsis-default-text-size": "0.32rem",
  "ellipsis-float-ellipsis-node-background": "linear-gradient(90deg, rgba(255, 255, 255, 0), #ffffff 20PX, #ffffff)",
  "dark-ellipsis-float-ellipsis-node-background": "linear-gradient(90deg, rgba(35, 35, 36, 0), #232324 20PX, #232324)",
  "ellipsis-float-ellipsis-node-padding-left": "20PX",
  "dark-checkbox-icon-color": "var(--dark-line-color)",
  "checkbox-icon-color": "var(--line-color)",
  "checkbox-icon-font-size": "20PX",
  "checkbox-icon-margin-right": "0.16rem",
  "dark-checkbox-icon-checked-color": "var(--dark-primary-color)",
  "checkbox-icon-checked-color": "var(--primary-color)",
  "dark-checkbox-icon-disabled-color": "var(--dark-card-background-color)",
  "checkbox-icon-disabled-color": "var(--card-background-color)",
  "dark-checkbox-icon-checked-disabled-color": "var(--dark-lighter-primary-color)",
  "checkbox-icon-checked-disabled-color": "var(--lighter-primary-color)",
  "checkbox-text-font-size": "0.32rem",
  "checkbox-text-disabled-opacity": "0.5",
  "checkbox-group-gutter": "0.48rem",
  "tabs-tab-bar-font-size": "0.3rem",
  "dark-tabs-tab-bar-background": "var(--dark-background-color)",
  "tabs-tab-bar-background": "var(--background-color)",
  "tabs-tab-bar-height": "0.84rem",
  "tabs-tab-bar-width": "1.56rem",
  "tabs-tab-bar-horizontal-height": "1.08rem",
  "tabs-tab-bar-card-height": "0.8rem",
  "dark-tabs-tab-bar-card-color": "var(--dark-primary-color)",
  "tabs-tab-bar-card-color": "var(--primary-color)",
  "dark-tabs-tab-bar-card-text-color": "var(--dark-mask-content-color)",
  "tabs-tab-bar-card-text-color": "var(--mask-content-color)",
  "tabs-tab-bar-card-border-radius": "2PX",
  "dark-tabs-tab-bar-line-active-color": "var(--dark-primary-color)",
  "tabs-tab-bar-line-active-color": "var(--primary-color)",
  "tabs-tab-bar-line-gutter": "0.8rem",
  "tabs-tab-bar-tag-gutter": "0.32rem",
  "tabs-tab-bar-tag-height": "1.2rem",
  "tabs-tab-bar-tag-vertical-padding": "0.24rem",
  "dark-tabs-tab-bar-tag-background": "var(--dark-card-background-color)",
  "tabs-tab-bar-tag-background": "var(--card-background-color)",
  "dark-tabs-tab-bar-tag-text-color": "var(--dark-font-color)",
  "tabs-tab-bar-tag-text-color": "var(--font-color)",
  "dark-tabs-tab-bar-tag-active-background": "var(--dark-primary-color)",
  "tabs-tab-bar-tag-active-background": "var(--primary-color)",
  "dark-tabs-tab-bar-tag-active-text-color": "var(--dark-mask-content-color)",
  "tabs-tab-bar-tag-active-text-color": "var(--mask-content-color)",
  "tabs-tab-bar-tag-padding": "0 0.32rem",
  "dark-tabs-underline-color": "var(--dark-primary-color)",
  "tabs-underline-color": "var(--primary-color)",
  "tabs-underline-thick": "2PX",
  "tabs-underline-size": "0.48rem",
  "tabs-underline-border-radius": "2PX",
  "tab-bar-height": "1rem",
  "tab-bar-font-size": "0.2rem",
  "tab-bar-icon-size": "0.4rem",
  "tab-bar-only-title-font-size": "0.32rem",
  "dark-tab-bar-color": "var(--dark-sub-info-font-color)",
  "tab-bar-color": "var(--sub-info-font-color)",
  "dark-tab-bar-active-color": "var(--dark-primary-color)",
  "tab-bar-active-color": "var(--primary-color)",
  "tab-bar-title-line-height": "0.28rem",
  "tab-bar-only-title-line-height": "0.44rem",
  "tab-bar-title-margin": "0 0 0.1rem 0",
  "tab-bar-item-icon-margin": "0.14rem 0 0.08rem",
  "nav-bar-height": "0.88rem",
  "dark-nav-bar-bottom-border-color": "var(--dark-line-color)",
  "nav-bar-bottom-border-color": "var(--line-color)",
  "dark-nav-bar-background": "var(--dark-background-color)",
  "nav-bar-background": "var(--background-color)",
  "dark-nav-bar-font-color": "var(--dark-font-color)",
  "nav-bar-font-color": "var(--font-color)",
  "nav-bar-two-sides-font-size": "0.32rem",
  "nav-bar-two-sides-padding": "0 0.32rem",
  "nav-bar-title-font-size": "0.34rem",
  "nav-bar-title-text-font-size": "0.34rem",
  "nav-bar-title-padding": "0 0.92rem",
  "nav-bar-back-icon-height": "0.32rem",
  "dark-image-placeholder-background": "var(--dark-card-background-color)",
  "image-placeholder-background": "var(--card-background-color)",
  "dark-image-loading-icon-color": "var(--dark-mask-content-color)",
  "image-loading-icon-color": "var(--mask-content-color)",
  "dark-image-retry-icon-color": "var(--dark-mask-content-color)",
  "image-retry-icon-color": "var(--mask-content-color)",
  "image-mask-background": "var(--mask-background)",
  "image-transition-function": "cubic-bezier(0.39, 0.575, 0.565, 1)",
  "image-inner-font-size": "0.32rem",
  "image-retry-font-size": "0.32rem",
  "dark-switch-text-color": "var(--dark-sub-font-color)",
  "switch-text-color": "var(--sub-font-color)",
  "dark-switch-text-checked-color": "var(--dark-mask-content-color)",
  "switch-text-checked-color": "var(--mask-content-color)",
  "switch-inner-background": "#FFFFFF",
  "switch-inner-transition": "all .2s",
  "switch-inner-fully-border-radius": "50%",
  "switch-inner-semi-border-radius": "1PX",
  "switch-android-width": "40PX",
  "switch-android-height": "24PX",
  "switch-android-padding": "2PX",
  "switch-android-inner-diameter-size": "20PX",
  "switch-android-inner-box-shadow": "0 2PX 4PX 0 rgba(0, 0, 0, 0.08)",
  "switch-android-fully-border-radius": "20PX",
  "switch-android-semi-border-radius": "2PX",
  "switch-android-checked-inner-transform": "translateX(16PX)",
  "switch-android-text-font-size": "12PX",
  "switch-android-text-gap-size": "5PX",
  "dark-switch-android-background": "var(--dark-line-color)",
  "switch-android-background": "var(--line-color)",
  "dark-switch-android-checked-background": "var(--dark-primary-color)",
  "switch-android-checked-background": "var(--primary-color)",
  "dark-switch-android-disabled-checked-background": "var(--dark-primary-disabled-color)",
  "switch-android-disabled-checked-background": "var(--primary-disabled-color)",
  "dark-switch-android-disabled-background": "var(--dark-lighter-line-color)",
  "switch-android-disabled-background": "var(--lighter-line-color)",
  "switch-ios-width": "1.02rem",
  "switch-ios-height": "0.62rem",
  "switch-ios-padding": "0.04rem",
  "switch-ios-inner-diameter-size": "0.54rem",
  "switch-ios-inner-border-color": "rgba(0, 0, 0, .04)",
  "switch-ios-inner-box-shadow": "0 3PX 2PX 0 rgba(0, 0, 0, .12)",
  "switch-ios-fully-border-radius": "0.32rem",
  "switch-ios-semi-border-radius": "0.04rem",
  "switch-ios-checked-inner-transform": "translateX(0.4rem)",
  "switch-ios-text-font-size": "0.28rem",
  "switch-ios-text-gap-size": "0.12rem",
  "switch-ios-background": "rgba(17, 17, 17, .15)",
  "switch-ios-checked-background": "#34C759",
  "switch-ios-disabled-checked-background": "#4DD865",
  "switch-ios-disabled-checked-opacity": "0.3",
  "switch-ios-disabled-background": "rgba(120, 120, 128, .16)",
  "toast-background": "rgba(0, 0, 0, 0.8)",
  "dark-toast-text-color": "var(--dark-mask-content-color)",
  "toast-text-color": "var(--mask-content-color)",
  "toast-font-size": "0.32rem",
  "toast-line-height": "0.48rem",
  "toast-border-radius": "0.08rem",
  "toast-loading-arc-background-color": "#666666",
  "toast-loading-inner-font-size": "0.24rem",
  "toast-safe-padding": "0 0.32rem",
  "toast-from-top-position": "30%",
  "toast-from-bottom-position": "30%",
  "toast-horizontal-padding": "0.16rem 0.32rem",
  "toast-horizontal-icon-size": "0.32rem",
  "toast-horizontal-icon-content-gutter": "0.16rem",
  "toast-vertical-padding": "0.32rem",
  "toast-vertical-icon-size": "0.48rem",
  "toast-vertical-icon-content-gutter": "0.16rem",
  "dark-loading-color": "var(--dark-primary-color)",
  "loading-color": "var(--primary-color)",
  "dark-loading-arc-background-color": "var(--dark-line-color)",
  "loading-arc-background-color": "var(--line-color)",
  "loading-dot-size": "6PX",
  "loading-dot-gutter": "0.12rem",
  "picker-view-font-size": "0.32rem",
  "picker-view-cell-height": "0.88rem",
  "picker-view-wrapper-height": "4.4rem",
  "picker-view-mask-top-background": "linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)",
  "dark-picker-view-mask-top-background": "linear-gradient(to bottom, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)",
  "picker-view-mask-bottom-background": "linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)",
  "dark-picker-view-mask-bottom-background": "linear-gradient(to top, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)",
  "dark-picker-view-selection-border-color": "var(--dark-line-color)",
  "picker-view-selection-border-color": "var(--line-color)",
  "picker-wrapper-shadow": "0 2PX 8PX rgba(0, 0, 0, .15)",
  "picker-wrapper-border-radius": "0.08rem",
  "picker-header-height": "1.08rem",
  "dark-picker-header-background": "var(--dark-mask-content-background)",
  "picker-header-background": "var(--mask-content-background)",
  "picker-title-font-size": "0.32rem",
  "picker-title-padding": "0 1.2rem",
  "picker-button-font-size": "0.3rem",
  "picker-button-padding": "0.32rem",
  "dark-picker-left-btn-color": "var(--dark-primary-color)",
  "picker-left-btn-color": "var(--primary-color)",
  "dark-picker-right-btn-color": "var(--dark-primary-color)",
  "picker-right-btn-color": "var(--primary-color)",
  "popover-arrow-size": "9PX",
  "popover-arrow-border-radius": "1PX",
  "popover-inner-border-radius": "4PX",
  "popover-inner-opacity": "0.8",
  "popover-inner-transition": "opacity .3s ease-in-out",
  "popover-inner-white-theme-opacity": "1",
  "popover-inner-background-shadow": "0 2PX 8PX 0 rgba(0, 0, 0, .1)",
  "popover-inner-top-arrow-shadow": "6PX 6PX 8PX 0 rgba(0, 0, 0, .04)",
  "popover-inner-bottom-arrow-shadow": "-6PX -6PX 8PX 0 rgba(0, 0, 0, .04)",
  "popover-background-color": "#000000",
  "dark-popover-white-theme-background-color": "var(--dark-mask-content-background)",
  "popover-white-theme-background-color": "var(--mask-content-background)",
  "dark-popover-content-color": "var(--dark-mask-content-color)",
  "popover-content-color": "var(--mask-content-color)",
  "popover-content-padding": "0.16rem 0.24rem",
  "popover-content-android-padding": "0.2rem 0.24rem 0.12rem",
  "popover-content-font-size": "0.28rem",
  "popover-content-line-height": "0.4rem",
  "popover-content-disabled-color": "rgba(255, 255, 255, 0.3)",
  "dark-popover-content-white-theme-color": "var(--dark-font-color)",
  "popover-content-white-theme-color": "var(--font-color)",
  "dark-popover-content-white-theme-disabled-color": "var(--dark-disabled-color)",
  "popover-content-white-theme-disabled-color": "var(--disabled-color)",
  "popover-content-border-color": "rgba(247, 248, 250, 0.1)",
  "dark-popover-content-white-theme-border-color": "var(--dark-line-color)",
  "popover-content-white-theme-border-color": "var(--line-color)",
  "popover-shadow-color": "rgba(0, 0, 0, 0.1)",
  "popover-menu-content-padding": "0 0.24rem",
  "dark-popover-menu-icon-white-theme-color": "var(--dark-sub-font-color)",
  "popover-menu-icon-white-theme-color": "var(--sub-font-color)",
  "popover-menu-active-background": "#242425",
  "dark-popover-menu-active-white-theme-background": "var(--dark-card-background-color)",
  "popover-menu-active-white-theme-background": "var(--card-background-color)",
  "popover-horizontal-menu-max-width": "5.76rem",
  "popover-horizontal-menu-item-size": "1.44rem",
  "popover-horizontal-menu-item-padding": "0.24rem 0",
  "popover-horizontal-menu-icon-margin": "0 0 0.16rem 0",
  "popover-icon-divider-color": "rgba(255, 255, 255, 0.3)",
  "popover-icon-divider-height": "0.24rem",
  "popover-icon-size": "0.32rem",
  "popover-icon-padding": "0 0.2rem 0 0.22rem",
  "popover-text-suffix-edge": "0.24rem",
  "popover-mask-background": "var(--mask-background)",
  "load-more-font-size": "0.28rem",
  "dark-load-more-text-color": "var(--dark-sub-info-font-color)",
  "load-more-text-color": "var(--sub-info-font-color)",
  "dark-cell-text-color": "var(--dark-sub-info-font-color)",
  "cell-text-color": "var(--sub-info-font-color)",
  "dark-cell-label-color": "var(--dark-font-color)",
  "cell-label-color": "var(--font-color)",
  "dark-cell-label-icon-color": "var(--dark-sub-font-color)",
  "cell-label-icon-color": "var(--sub-font-color)",
  "dark-cell-desc-color": "var(--dark-sub-info-font-color)",
  "cell-desc-color": "var(--sub-info-font-color)",
  "cell-desc-font-size": "0.28rem",
  "cell-desc-margin-top": "0.04rem",
  "cell-content-font-size": "0.28rem",
  "dark-cell-arrow-color": "var(--dark-disabled-color)",
  "cell-arrow-color": "var(--disabled-color)",
  "cell-arrow-gutter": "0.16rem",
  "cell-arrow-font-size": "0.24rem",
  "dark-cell-background-color": "var(--dark-container-background-color)",
  "cell-background-color": "var(--container-background-color)",
  "cell-font-size": "0.32rem",
  "cell-horizontal-padding": "0.32rem",
  "cell-item-height": "1.08rem",
  "cell-item-has-desc-height": "1.48rem",
  "cell-label-gutter": "0.48rem",
  "cell-label-icon-gutter": "0.24rem",
  "cell-label-icon-font-size": "0.4rem",
  "cell-extra-font-size": "0.28rem",
  "cell-extra-line-height": "0.4rem",
  "cell-extra-padding": "0.24rem 0.32rem",
  "tag-font-size": "0.24rem",
  "tag-icon-font-size": "0.24rem",
  "tag-icon-margin-right": "0.04rem",
  "tag-icon-close-margin-left": "0.08rem",
  "tag-small-size-height": "0.36rem",
  "tag-small-size-padding": "0.08rem",
  "tag-medium-size-height": "0.4rem",
  "tag-medium-size-padding": "0.08rem",
  "tag-large-size-height": "0.48rem",
  "tag-large-size-padding": "0.12rem",
  "tag-filleted-padding": "0.16rem",
  "tag-border-radius": "2PX",
  "dark-tag-primary-color": "var(--dark-primary-color)",
  "tag-primary-color": "var(--primary-color)",
  "dark-tag-primary-background-color": "var(--dark-lighter-primary-color)",
  "tag-primary-background-color": "var(--lighter-primary-color)",
  "dark-tag-primary-border-color": "var(--dark-primary-color)",
  "tag-primary-border-color": "var(--primary-color)",
  "dark-tag-hollow-color": "var(--dark-primary-color)",
  "tag-hollow-color": "var(--primary-color)",
  "dark-tag-hollow-border-color": "var(--dark-primary-color)",
  "tag-hollow-border-color": "var(--primary-color)",
  "dark-tag-solid-color": "var(--dark-mask-content-color)",
  "tag-solid-color": "var(--mask-content-color)",
  "dark-tag-solid-background-color": "var(--dark-primary-color)",
  "tag-solid-background-color": "var(--primary-color)",
  "dark-tag-solid-border-color": "var(--dark-primary-color)",
  "tag-solid-border-color": "var(--primary-color)",
  "tag-list-horizontal-gutter": "0.16rem",
  "tag-list-vertical-gutter": "0",
  "dark-tag-list-add-border-color": "var(--dark-line-color)",
  "tag-list-add-border-color": "var(--line-color)",
  "dark-tag-list-add-background": "var(--dark-lighter-line-color)",
  "tag-list-add-background": "var(--lighter-line-color)",
  "dark-tag-list-add-color": "var(--dark-sub-font-color)",
  "tag-list-add-color": "var(--sub-font-color)",
  "image-preview-mask-background": "rgba(0, 0, 0, 0.9)",
  "image-preview-indicator-font-size": "0.28rem",
  "image-preview-indicator-padding": "0.24rem 0.4rem",
  "image-preview-indicator-background": "linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3))",
  "image-preview-thumb-transition": "all cubic-bezier(0.34, 0.69, 0.1, 1)",
  "dropdown-menu-padding": "0.3rem",
  "dropdown-menu-font-size": "0.32rem",
  "dropdown-menu-line-height": "0.44rem",
  "dark-dropdown-menu-color": "var(--dark-font-color)",
  "dropdown-menu-color": "var(--font-color)",
  "dark-dropdown-menu-selected-color": "var(--dark-primary-color)",
  "dropdown-menu-selected-color": "var(--primary-color)",
  "dark-dropdown-menu-disabled-color": "var(--dark-disabled-color)",
  "dropdown-menu-disabled-color": "var(--disabled-color)",
  "dark-dropdown-menu-tip-color": "var(--dark-sub-info-font-color)",
  "dropdown-menu-tip-color": "var(--sub-info-font-color)",
  "dropdown-menu-tip-min-width": "0.36rem",
  "dropdown-menu-tip-padding-right": "0.32rem",
  "dropdown-menu-label-max-width": "1.92rem",
  "dropdown-menu-icon-size": "12PX",
  "dark-dropdown-menu-icon-color": "var(--dark-disabled-color)",
  "dropdown-menu-icon-color": "var(--disabled-color)",
  "dark-dropdown-menu-icon-selected-color": "var(--dark-primary-color)",
  "dropdown-menu-icon-selected-color": "var(--primary-color)",
  "dropdown-menu-icon-margin-left": "4PX",
  "dark-dropdown-options-background-color": "var(--dark-container-background-color)",
  "dropdown-options-background-color": "var(--container-background-color)",
  "dropdown-options-item-padding": "0.32rem",
  "dropdown-options-item-font-size": "0.32rem",
  "dropdown-options-item-line-height": "0.44rem",
  "dark-dropdown-options-item-color": "var(--dark-font-color)",
  "dropdown-options-item-color": "var(--font-color)",
  "dark-dropdown-options-item-selected-color": "var(--dark-primary-color)",
  "dropdown-options-item-selected-color": "var(--primary-color)",
  "dark-dropdown-options-item-disabled-color": "var(--dark-disabled-color)",
  "dropdown-options-item-disabled-color": "var(--disabled-color)",
  "dropdown-options-item-icon-right": "0.32rem",
  "dropdown-mask-background-color": "var(--mask-background)",
  "dropdown-multi-rows-options-gutter": "0.24rem",
  "dropdown-multi-rows-options-item-padding": "0.16rem",
  "dropdown-multi-rows-options-item-font-size": "0.28rem",
  "dropdown-multi-rows-options-item-line-height": "0.4rem",
  "dark-dropdown-multi-rows-options-item-color": "var(--dark-sub-font-color)",
  "dropdown-multi-rows-options-item-color": "var(--sub-font-color)",
  "dropdown-multi-rows-options-item-border-radius": "2PX",
  "dark-dropdown-multi-rows-options-item-background": "var(--dark-card-background-color)",
  "dropdown-multi-rows-options-item-background": "var(--card-background-color)",
  "dark-dropdown-multi-rows-options-item-selected-background": "var(--dark-lighter-primary-color)",
  "dropdown-multi-rows-options-item-selected-background": "var(--lighter-primary-color)",
  "dark-dropdown-multi-rows-options-item-selected-color": "var(--dark-primary-color)",
  "dropdown-multi-rows-options-item-selected-color": "var(--primary-color)",
  "dropdown-multi-rows-options-container-padding": "0.32rem",
  "dropdown-multi-rows-options-container-margin": "0 -0.24rem -0.24rem 0",
  "dark-collapse-disabled-header-color": "var(--dark-disabled-color)",
  "collapse-disabled-header-color": "var(--disabled-color)",
  "dark-collapse-header-background": "var(--dark-container-background-color)",
  "collapse-header-background": "var(--container-background-color)",
  "collapse-header-height": "1.08rem",
  "collapse-header-font-size": "0.32rem",
  "collapse-header-margin-left": "0.32rem",
  "collapse-header-padding": "0.32rem 0.32rem 0.32rem 0",
  "dark-collapse-header-color": "var(--dark-font-color)",
  "collapse-header-color": "var(--font-color)",
  "collapse-header-line-height": "0.44rem",
  "dark-collapse-header-icon-color": "var(--dark-disabled-color)",
  "collapse-header-icon-color": "var(--disabled-color)",
  "collapse-content-padding": "0.24rem 0.32rem",
  "collapse-content-font-size": "0.28rem",
  "dark-collapse-content-color": "var(--dark-sub-info-font-color)",
  "collapse-content-color": "var(--sub-info-font-color)",
  "collapse-content-line-height": "0.44rem",
  "dark-pull-refresh-label-background-color": "var(--dark-card-background-color)",
  "pull-refresh-label-background-color": "var(--card-background-color)",
  "pull-refresh-label-font-size": "0.24rem",
  "dark-pull-refresh-content-background-color": "var(--dark-background-color)",
  "pull-refresh-content-background-color": "var(--background-color)",
  "dark-pull-refresh-label-text-color": "var(--dark-sub-info-font-color)",
  "pull-refresh-label-text-color": "var(--sub-info-font-color)",
  "dark-pull-refresh-label-loading-color": "var(--dark-sub-info-font-color)",
  "pull-refresh-label-loading-color": "var(--sub-info-font-color)",
  "slider-padding": "0.22rem 0.32rem",
  "slider-mask-padding": "0.3rem",
  "slider-has-mark-padding-bottom": "0.7rem",
  "slider-label-font-size": "0.32rem",
  "slider-label-gutter": "0.24rem",
  "dark-slider-text-color": "var(--dark-sub-info-font-color)",
  "slider-text-color": "var(--sub-info-font-color)",
  "dark-slider-line-color": "var(--dark-line-color)",
  "slider-line-color": "var(--line-color)",
  "slider-line-border-radius": "0.08rem",
  "dark-slider-line-activated-color": "var(--dark-primary-color)",
  "slider-line-activated-color": "var(--primary-color)",
  "dark-slider-line-disabled-color": "var(--dark-primary-disabled-color)",
  "slider-line-disabled-color": "var(--primary-disabled-color)",
  "slider-thumb-width": "0.48rem",
  "slider-thumb-height": "0.48rem",
  "slider-thumb-border-radius": "50%",
  "slider-thumb-box-shadow": "0 2PX 8PX rgba(0, 0, 0, .1)",
  "dark-slider-thumb-background": "var(--dark-mask-content-background)",
  "slider-thumb-background": "var(--mask-content-background)",
  "slider-popover-arrow-size": "6PX",
  "slider-popover-font-size": "0.24rem",
  "slider-popover-line-height": "0.34rem",
  "slider-popover-gutter": "0.28rem",
  "slider-mark-width": "6PX",
  "slider-mark-height": "6PX",
  "slider-mark-border-radius": "50%",
  "slider-mark-label-font-size": "0.28rem",
  "slider-mark-label-line-height": "0.4rem",
  "slider-horizontal-mark-label-top": "0.38rem",
  "slider-vertical-mark-label-right": "0.26rem",
  "dark-swipe-load-label-background": "var(--dark-card-background-color)",
  "swipe-load-label-background": "var(--card-background-color)",
  "swipe-load-label-border-radius": "50%",
  "swipe-load-label-text-margin-left": "0.4rem",
  "swipe-load-label-text-width": "0.4rem",
  "dark-swipe-load-label-text-color": "var(--dark-font-color)",
  "swipe-load-label-text-color": "var(--font-color)",
  "swipe-load-label-text-font-size": "0.24rem",
  "notice-bar-wrapper-padding": "0 0.32rem",
  "notice-bar-background": "#FFF7E8",
  "dark-notice-bar-background": "#4D1B00",
  "dark-notice-bar-color": "var(--dark-warning-color)",
  "notice-bar-color": "var(--warning-color)",
  "notice-bar-gradient-background": "linear-gradient(to right, #fff7e8, rgba(255, 247, 232, 0))",
  "dark-notice-bar-gradient-background": "linear-gradient(to right, #4D1B00, rgba(77, 27, 0, 0))",
  "notice-bar-line-height": "0.4rem",
  "notice-bar-text-font-size": "0.28rem",
  "notice-bar-icon-font-size": "16PX",
  "notice-bar-single-line-height": "0.72rem",
  "notice-bar-vertical-padding": "0.16rem",
  "notice-bar-horizontal-padding": "0.16rem",
  "notice-bar-gradient-width": "0.16rem",
  "dark-notify-success-background": "var(--dark-success-color)",
  "notify-success-background": "var(--success-color)",
  "dark-notify-error-background": "var(--dark-danger-color)",
  "notify-error-background": "var(--danger-color)",
  "dark-notify-warn-background": "var(--dark-warning-color)",
  "notify-warn-background": "var(--warning-color)",
  "dark-notify-font-color": "var(--dark-mask-content-color)",
  "notify-font-color": "var(--mask-content-color)",
  "dark-notify-info-font-color": "var(--dark-primary-color)",
  "notify-info-font-color": "var(--primary-color)",
  "notify-font-size": "0.28rem",
  "notify-min-height": "0.72rem",
  "steps-padding": "0.32rem 0",
  "steps-tail-border-radius": "2PX",
  "steps-tail-horizontal-gutter": "18PX",
  "steps-tail-vertical-gutter": "14PX",
  "steps-tail-horizontal-padding": "0 var(--steps-tail-horizontal-gutter)",
  "steps-tail-vertical-padding": "var(--steps-tail-vertical-gutter) 0",
  "steps-tail-horizontal-left": "9PX",
  "steps-tail-vertical-top": "9PX",
  "steps-tail-standard-size": "1PX",
  "dark-steps-tail-standard-background": "var(--dark-line-color)",
  "steps-tail-standard-background": "var(--line-color)",
  "dark-steps-tail-finish-background": "var(--dark-primary-color)",
  "steps-tail-finish-background": "var(--primary-color)",
  "dark-steps-finish-icon-num-background": "var(--dark-lighter-primary-color)",
  "steps-finish-icon-num-background": "var(--lighter-primary-color)",
  "dark-steps-process-icon-num-background": "var(--dark-primary-color)",
  "steps-process-icon-num-background": "var(--primary-color)",
  "dark-steps-wait-icon-num-background": "var(--dark-lighter-line-color)",
  "steps-wait-icon-num-background": "var(--lighter-line-color)",
  "dark-steps-error-icon-num-background": "var(--dark-danger-color)",
  "steps-error-icon-num-background": "var(--danger-color)",
  "steps-icon-svg-standard-font-size": "10PX",
  "dark-steps-finish-icon-svg-color": "var(--dark-primary-color)",
  "steps-finish-icon-svg-color": "var(--primary-color)",
  "dark-steps-error-icon-svg-color": "var(--dark-mask-content-color)",
  "steps-error-icon-svg-color": "var(--mask-content-color)",
  "steps-error-icon-svg-font-size": "8PX",
  "steps-icon-num-font-size": "12PX",
  "steps-icon-num-line-height": "18PX",
  "dark-steps-icon-num-color": "var(--dark-sub-info-font-color)",
  "steps-icon-num-color": "var(--sub-info-font-color)",
  "dark-steps-process-icon-num-color": "var(--dark-mask-content-color)",
  "steps-process-icon-num-color": "var(--mask-content-color)",
  "dark-steps-finish-dot-border-color": "var(--dark-primary-color)",
  "steps-finish-dot-border-color": "var(--primary-color)",
  "dark-steps-process-dot-background": "var(--dark-primary-color)",
  "steps-process-dot-background": "var(--primary-color)",
  "dark-steps-wait-dot-border-color": "var(--dark-sub-info-font-color)",
  "steps-wait-dot-border-color": "var(--sub-info-font-color)",
  "dark-steps-finish-title-color": "var(--dark-font-color)",
  "steps-finish-title-color": "var(--font-color)",
  "dark-steps-error-title-color": "var(--dark-danger-color)",
  "steps-error-title-color": "var(--danger-color)",
  "dark-steps-process-title-color": "var(--dark-primary-color)",
  "steps-process-title-color": "var(--primary-color)",
  "dark-steps-wait-title-color": "var(--dark-sub-info-font-color)",
  "steps-wait-title-color": "var(--sub-info-font-color)",
  "dark-steps-description-color": "var(--dark-sub-font-color)",
  "steps-description-color": "var(--sub-font-color)",
  "dark-steps-wait-description-color": "var(--dark-sub-info-font-color)",
  "steps-wait-description-color": "var(--sub-info-font-color)",
  "steps-icon-width": "18PX",
  "steps-icon-height": "18PX",
  "steps-dot-width": "8PX",
  "steps-dot-height": "8PX",
  "steps-dot-border-width": "1.5PX",
  "steps-horizontal-content-margin-top": "0.1rem",
  "steps-vertical-content-margin-left": "0.26rem",
  "steps-vertical-content-padding-bottom": "0.5rem",
  "steps-title-font-size": "0.28rem",
  "steps-title-line-height": "0.4rem",
  "steps-description-font-size": "0.24rem",
  "steps-description-line-height": "0.36rem",
  "steps-description-margin-top": "0.04rem",
  "steps-vertical-padding-bottom": "0",
  "steps-vertical-padding-left": "0.4rem",
  "dark-steps-process-with-config-item-icon-color": "var(--dark-mask-content-color)",
  "steps-process-with-config-item-icon-color": "var(--mask-content-color)",
  "swipe-action-open-transition": "cubic-bezier(0.2, 0.8, 0.32, 1.28)",
  "swipe-action-close-transition": "cubic-bezier(0.34, 0.69, 0.1, 1)",
  "swipe-action-info-padding": "0.32rem",
  "swipe-action-info-bounce-buffer": "0.6rem",
  "swipe-action-text-font-size": "0.32rem",
  "swipe-action-text-line-height": "0.44rem",
  "dark-swipe-action-text-color": "var(--dark-mask-content-color)",
  "swipe-action-text-color": "var(--mask-content-color)",
  "swipe-action-icon-width": "0.4rem",
  "swipe-action-icon-height": "0.4rem",
  "swipe-action-icon-margin-right": "0.08rem",
  "dark-badge-background-color": "var(--dark-danger-color)",
  "badge-background-color": "var(--danger-color)",
  "dark-badge-text-color": "var(--dark-mask-content-color)",
  "badge-text-color": "var(--mask-content-color)",
  "badge-font-size": "12PX",
  "badge-dot-width": "8PX",
  "badge-text-width": "16PX",
  "badge-text-padding": "4PX",
  "badge-text-deviation": "-8PX",
  "badge-dot-deviation": "-4PX",
  "badge-border-radius": "100PX",
  "dark-badge-border-color": "var(--dark-background-color)",
  "badge-border-color": "var(--background-color)",
  "circle-progress-font-size": "0.28rem",
  "dark-circle-progress-primary-color": "var(--dark-primary-color)",
  "circle-progress-primary-color": "var(--primary-color)",
  "dark-circle-progress-track-color": "var(--dark-lighter-line-color)",
  "circle-progress-track-color": "var(--lighter-line-color)",
  "dark-circle-progress-disabled-color": "var(--dark-disabled-color)",
  "circle-progress-disabled-color": "var(--disabled-color)",
  "dark-circle-progress-mini-track-color": "var(--dark-lighter-primary-color)",
  "circle-progress-mini-track-color": "var(--lighter-primary-color)",
  "circle-progress-linear-gradient-start-color": "#4776E6",
  "circle-progress-linear-gradient-end-color": "#14CAFF",
  "circle-progress-linear-gradient-text-color": "#3C89EC",
  "dark-progress-primary-color": "var(--dark-primary-color)",
  "progress-primary-color": "var(--primary-color)",
  "dark-progress-track-color": "var(--dark-lighter-line-color)",
  "progress-track-color": "var(--lighter-line-color)",
  "dark-progress-disabled-color": "var(--dark-disabled-color)",
  "progress-disabled-color": "var(--disabled-color)",
  "dark-progress-disabled-text-color": "var(--dark-sub-info-font-color)",
  "progress-disabled-text-color": "var(--sub-info-font-color)",
  "progress-linear-gradient-start-color": "#4776E6",
  "progress-linear-gradient-end-color": "#14CAFF",
  "progress-linear-gradient-text-color": "#3C89EC",
  "progress-nav-track-color": "transparent",
  "progress-nav-track-height": "2PX",
  "progress-track-height": "4PX",
  "progress-inner-track-height": "18PX",
  "pagination-padding": "0.22rem 0.32rem",
  "pagination-center-field-gutter": "0.48rem",
  "pagination-field-font-size": "0.3rem",
  "pagination-field-line-height": "0.44rem",
  "pagination-field-button-min-height": "0.64rem",
  "pagination-field-button-border-radius": "0.04rem",
  "pagination-field-button-padding": "0.12rem 0.32rem",
  "pagination-field-btn-text-font-size": "0.28rem",
  "pagination-field-btn-icon-text-gutter": "0.22rem",
  "dark-pagination-field-primary-background": "var(--dark-primary-color)",
  "pagination-field-primary-background": "var(--primary-color)",
  "dark-pagination-field-primary-text-color": "var(--dark-mask-content-color)",
  "pagination-field-primary-text-color": "var(--mask-content-color)",
  "dark-pagination-field-default-background": "var(--dark-card-background-color)",
  "pagination-field-default-background": "var(--card-background-color)",
  "dark-pagination-field-default-text-color": "var(--dark-font-color)",
  "pagination-field-default-text-color": "var(--font-color)",
  "dark-pagination-field-disabled-background": "var(--dark-card-background-color)",
  "pagination-field-disabled-background": "var(--card-background-color)",
  "dark-pagination-field-disabled-text-color": "var(--dark-disabled-color)",
  "pagination-field-disabled-text-color": "var(--disabled-color)",
  "dark-pagination-field-text-color": "var(--dark-font-color)",
  "pagination-field-text-color": "var(--font-color)",
  "dark-pagination-field-text-primary-text-color": "var(--dark-primary-color)",
  "pagination-field-text-primary-text-color": "var(--primary-color)",
  "pagination-item-font-size": "0.36rem",
  "pagination-item-line-height": "0.44rem",
  "dark-pagination-item-primary-text-color": "var(--dark-primary-color)",
  "pagination-item-primary-text-color": "var(--primary-color)",
  "dark-pagination-item-default-text-color": "var(--dark-font-color)",
  "pagination-item-default-text-color": "var(--font-color)",
  "dark-progress-text-inner-color": "var(--dark-mask-content-color)",
  "progress-text-inner-color": "var(--mask-content-color)",
  "progress-text-gutter": "0.16rem",
  "progress-text-font-size": "0.28rem",
  "progress-text-follow-font-size": "0.26rem",
  "progress-text-follow-border-radius": "0.4rem",
  "progress-text-follow-width": "0.72rem",
  "progress-text-follow-height": "0.4rem",
  "transition-fade-duration": "300ms",
  "rate-icon-size": "24PX",
  "rate-icon-offset": "6PX",
  "rate-icon-active-color": "#FFB400",
  "dark-rate-icon-normal-color": "var(--dark-line-color)",
  "rate-icon-normal-color": "var(--line-color)",
  "dark-rate-icon-disabled-active-color": "var(--dark-disabled-color)",
  "rate-icon-disabled-active-color": "var(--disabled-color)",
  "count-down-font-size": "0.32rem",
  "count-down-line-height": "0.44rem",
  "dark-count-down-color": "var(--dark-font-color)",
  "count-down-color": "var(--font-color)",
  "grid-icon-width": "0.64rem",
  "grid-icon-height": "0.64rem",
  "grid-vertical-text-margin-top": "0.16rem",
  "grid-vertical-title-font-size": "0.32rem",
  "grid-vertical-title-line-height": "0.4rem",
  "grid-vertical-content-margin-top": "0.04rem",
  "grid-vertical-content-font-size": "0.24rem",
  "grid-vertical-content-line-height": "0.32rem",
  "grid-horizontal-text-margin-left": "0.24rem",
  "grid-horizontal-content-margin-top": "0",
  "dark-grid-border-color": "var(--dark-line-color)",
  "grid-border-color": "var(--line-color)",
  "grid-border-size": "66.66%",
  "action-sheet-item-height": "1.08rem",
  "action-sheet-item-font-size": "0.32rem",
  "action-sheet-border-radius": "0.16rem",
  "dark-action-sheet-cancel-border-color": "var(--dark-lighter-line-color)",
  "action-sheet-cancel-border-color": "var(--lighter-line-color)",
  "action-sheet-cancel-border-width": "0.16rem",
  "action-sheet-header-padding": "0.32rem",
  "action-sheet-title-font-size": "0.32rem",
  "action-sheet-sub-title-font-size": "0.28rem",
  "search-bar-padding": "0.32rem",
  "dark-search-bar-background-color": "var(--dark-background-color)",
  "search-bar-background-color": "var(--background-color)",
  "search-bar-square-shape-border-radius": "0.04rem",
  "search-bar-round-shape-border-radius": "199.98rem",
  "search-bar-input-wrapper-height": "0.72rem",
  "search-bar-input-wrapper-padding": "0.16rem 0.28rem",
  "dark-search-bar-input-wrapper-background-color": "var(--dark-lighter-line-color)",
  "search-bar-input-wrapper-background-color": "var(--lighter-line-color)",
  "search-bar-input-wrapper-font-size": "0.28rem",
  "search-bar-input-height": "0.4rem",
  "dark-search-bar-input-caret-color": "var(--dark-primary-color)",
  "search-bar-input-caret-color": "var(--primary-color)",
  "dark-search-bar-input-placeholder-color": "var(--dark-disabled-color)",
  "search-bar-input-placeholder-color": "var(--disabled-color)",
  "search-bar-prefix-margin-right": "0.18rem",
  "dark-search-bar-clear-icon-color": "var(--dark-disabled-color)",
  "search-bar-clear-icon-color": "var(--disabled-color)",
  "search-bar-clear-icon-font-size": "16PX",
  "search-bar-clear-icon-padding-left": "0.32rem",
  "dark-search-bar-search-icon-color": "var(--dark-sub-info-font-color)",
  "search-bar-search-icon-color": "var(--sub-info-font-color)",
  "search-bar-search-icon-font-size": "0.32rem",
  "dark-search-bar-cancel-btn-color": "var(--dark-primary-color)",
  "search-bar-cancel-btn-color": "var(--primary-color)",
  "search-bar-cancel-btn-font-size": "0.3rem",
  "search-bar-cancel-btn-margin-left": "0.32rem",
  "dark-search-bar-association-background-color": "var(--dark-container-background-color)",
  "search-bar-association-background-color": "var(--container-background-color)",
  "search-bar-association-item-height": "1.04rem",
  "search-bar-association-item-padding": "0.32rem",
  "search-bar-association-item-font-size": "0.3rem",
  "dark-search-bar-association-item-color": "var(--dark-font-color)",
  "search-bar-association-item-color": "var(--font-color)",
  "dark-search-bar-association-item-highlight-color": "var(--dark-primary-color)",
  "search-bar-association-item-highlight-color": "var(--primary-color)",
  "image-picker-font-size": "0.28rem",
  "image-picker-disabled-opacity": "0.7",
  "image-picker-border-radius": "0.04rem",
  "dark-image-picker-add-background": "var(--dark-card-background-color)",
  "image-picker-add-background": "var(--card-background-color)",
  "image-picker-add-icon-font-size": "0.6rem",
  "dark-image-picker-add-icon-color": "var(--dark-disabled-color)",
  "image-picker-add-icon-color": "var(--disabled-color)",
  "image-picker-add-text-font-size": "0.24rem",
  "dark-image-picker-add-text-color": "var(--dark-sub-info-font-color)",
  "image-picker-add-text-color": "var(--sub-info-font-color)",
  "dark-image-picker-error-color": "var(--dark-mask-content-color)",
  "image-picker-error-color": "var(--mask-content-color)",
  "image-picker-error-background": "rgba(0, 0, 0, 0.5)",
  "dark-image-picker-close-color": "var(--dark-mask-content-color)",
  "image-picker-close-color": "var(--mask-content-color)",
  "image-picker-close-font-size": "0.24rem",
  "image-picker-close-width": "0.36rem",
  "image-picker-close-height": "0.36rem",
  "image-picker-close-background": "rgba(0, 0, 0, 0.3)",
  "image-picker-close-border-radius": "0 0.04rem",
  "dark-index-bar-background": "var(--dark-background-color)",
  "index-bar-background": "var(--background-color)",
  "dark-index-bar-group-active-color": "var(--dark-primary-color)",
  "index-bar-group-active-color": "var(--primary-color)",
  "index-bar-group-left-spacing": "0.32rem",
  "index-bar-group-title-height": "0.48rem",
  "index-bar-group-title-background": "#f7f8fa",
  "dark-index-bar-group-title-background": "#2e2e30",
  "dark-index-bar-group-title-font-color": "var(--dark-sub-info-font-color)",
  "index-bar-group-title-font-color": "var(--sub-info-font-color)",
  "index-bar-group-title-font-size": "0.28rem",
  "index-bar-group-item-height": "1.08rem",
  "index-bar-group-item-font-size": "0.32rem",
  "dark-index-bar-sidebar-active-color": "var(--dark-primary-color)",
  "index-bar-sidebar-active-color": "var(--primary-color)",
  "index-bar-sidebar-item-font-size": "0.2rem",
  "index-bar-sidebar-item-line-height": "0.28rem",
  "index-bar-sidebar-item-padding": "0.04rem 0.16rem",
  "index-bar-sidebar-item-width": "0.2rem",
  "index-bar-sidebar-sweat-padding": "0 0.16rem",
  "index-bar-sidebar-sweat-background": "#333333",
  "dark-index-bar-sidebar-sweat-color": "var(--dark-mask-content-color)",
  "index-bar-sidebar-sweat-color": "var(--mask-content-color)",
  "index-bar-sidebar-sweat-right": "0.72rem",
  "index-bar-sidebar-sweat-font-size": "0.48rem",
  "index-bar-sidebar-sweat-radius": "1rem",
  "index-bar-sidebar-sweat-triangle-position": "-0.56rem",
  "index-bar-sidebar-sweat-triangle-border": "0.36rem solid transparent",
  "index-bar-sidebar-toast-background": "rgba(0, 0, 0, 0.8)",
  "dark-index-bar-sidebar-toast-color": "var(--dark-mask-content-color)",
  "index-bar-sidebar-toast-color": "var(--mask-content-color)",
  "index-bar-sidebar-toast-height": "0.96rem",
  "index-bar-sidebar-toast-radius": "0.08rem",
  "index-bar-sidebar-toast-padding": "0 0.16rem",
  "index-bar-sidebar-toast-font-size": "0.48rem",
  "stepper-width": "1.96rem",
  "stepper-font-size": "0.28rem",
  "dark-stepper-square-border-color": "var(--dark-lighter-line-color)",
  "stepper-square-border-color": "var(--lighter-line-color)",
  "stepper-square-border-radius": "0.04rem",
  "stepper-square-background-color": "transparent",
  "stepper-round-button-border-radius": "50%",
  "stepper-round-input-background-color": "transparent",
  "stepper-button-size": "0.56rem",
  "stepper-button-icon-size": "0.2rem",
  "dark-stepper-default-background-color": "var(--dark-card-background-color)",
  "stepper-default-background-color": "var(--card-background-color)",
  "dark-stepper-content-color": "var(--dark-font-color)",
  "stepper-content-color": "var(--font-color)",
  "dark-stepper-disable-color": "var(--dark-disabled-color)",
  "stepper-disable-color": "var(--disabled-color)",
  "stepper-input-width": "0.8rem",
  "stepper-input-height": "0.56rem",
  "stepper-input-margin": "0 1PX",
  "form-item-label-item-font-size": "0.32rem",
  "form-item-label-item-line-height": "1.08rem",
  "dark-form-item-label-item-color": "var(--dark-font-color)",
  "form-item-label-item-color": "var(--font-color)",
  "form-item-label-item-gutter": "0.32rem",
  "form-item-label-item-width": "1.92rem",
  "dark-form-item-border-divider-color": "var(--dark-line-color)",
  "form-item-border-divider-color": "var(--line-color)",
  "dark-form-item-label-item-required-asterisk-color": "var(--dark-danger-color)",
  "form-item-label-item-required-asterisk-color": "var(--danger-color)",
  "dark-form-item-error-message-color": "var(--dark-danger-color)",
  "form-item-error-message-color": "var(--danger-color)",
  "dark-form-item-warning-message-color": "var(--dark-warning-color)",
  "form-item-warning-message-color": "var(--warning-color)",
  "time-line-dot-width": "0.18rem",
  "dark-time-line-dot-border-color": "var(--dark-primary-color)",
  "time-line-dot-border-color": "var(--primary-color)",
  "dark-time-line-dot-background-color": "var(--dark-background-color)",
  "time-line-dot-background-color": "var(--background-color)",
  "time-line-axis-width": "0.02rem",
  "dark-time-line-axis-color": "var(--dark-line-color)",
  "time-line-axis-color": "var(--line-color)",
  "time-line-label-font-size": "0.24rem",
  "dark-time-line-label-color": "var(--dark-sub-info-font-color)",
  "time-line-label-color": "var(--sub-info-font-color)",
  "time-line-content-margin-top": "0.14rem",
  "time-line-content-margin-bottom": "0.38rem",
  "time-line-content-margin-left": "0.16rem",
  "time-line-content-border-radius": "0.08rem",
  "time-line-content-font-size": "0.32rem",
  "dark-time-line-content-background-color": "var(--dark-line-color)",
  "time-line-content-background-color": "var(--line-color)",
  "dark-time-line-content-color": "var(--dark-font-color)",
  "time-line-content-color": "var(--font-color)",
  "keyboard-background": "#f2f3f5",
  "dark-keyboard-background": "#232324",
  "keyboard-content-padding": "0.16rem",
  "keyboard-unified-margin": "0.16rem",
  "dark-keyboard-confirm-key-background": "var(--dark-primary-color)",
  "keyboard-confirm-key-background": "var(--primary-color)",
  "dark-keyboard-confirm-key-color": "var(--dark-mask-content-color)",
  "keyboard-confirm-key-color": "var(--mask-content-color)",
  "keyboard-confirm-key-font-size": "0.36rem",
  "keyboard-key-font-weight": "500",
  "keyboard-key-font-size": "0.44rem",
  "keyboard-key-icon-size": "0.52rem",
  "keyboard-key-line-height": "0.6rem",
  "keyboard-key-background": "#ffffff",
  "dark-keyboard-key-background": "#2e2e30",
  "dark-keyboard-key-active-background": "var(--dark-line-color)",
  "keyboard-key-active-background": "var(--line-color)",
  "keyboard-key-border-radius": "0.08rem",
  "keyboard-key-height": "0.96rem",
  "dark-keyboard-key-color": "var(--dark-font-color)",
  "keyboard-key-color": "var(--font-color)",
  "divider-line-thickness": "1PX",
  "dark-divider-line-color": "var(--dark-line-color)",
  "divider-line-color": "var(--line-color)",
  "divider-content-font-size": "0.28rem",
  "dark-divider-content-font-color": "var(--dark-sub-font-color)",
  "divider-content-font-color": "var(--sub-font-color)",
  "divider-left-width": "0.56rem",
  "divider-right-width": "0.56rem",
  "divider-content-padding": "0.24rem",
  "divider-padding": "0.32rem",
  "skeleton-border-radius": "0",
  "dark-skeleton-background-color": "var(--dark-lighter-line-color)",
  "skeleton-background-color": "var(--lighter-line-color)",
  "skeleton-gradient-animation-color": "rgba(255, 255, 255, 0.6)",
  "dark-skeleton-gradient-animation-color": "hsla(0, 0%, 100%, 0.08)",
  "skeleton-breath-opacity": "0.4",
  "skeleton-gradient-animation-timing-function": "cubic-bezier(0.42, 0, 0.58, 1)",
  "skeleton-gradient-animation-duration": "1.5s",
  "skeleton-breath-animation-duration": "1.5s",
  "skeleton-title-height": "0.32rem",
  "skeleton-paragraph-height": "0.32rem",
  "skeleton-paragraph-margin-top": "0.24rem",
  "skeleton-avatar-size": "0.64rem",
  "skeleton-grid-icon-size": "0.64rem",
  "skeleton-grid-text-width": "1.28rem",
  "skeleton-grid-text-height": "0.32rem",
  "skeleton-medium-gutter": "0.16rem",
  "skeleton-large-gutter": "0.4rem",
  "uploader-item-height": "0.72rem",
  "uploader-item-margin-top": "0.32rem",
  "uploader-item-padding": "0 0.24rem",
  "dark-uploader-item-background-color": "var(--dark-card-background-color)",
  "uploader-item-background-color": "var(--card-background-color)",
  "uploader-file-icon-font-size": "0.32rem",
  "uploader-file-icon-margin-right": "0.24rem",
  "dark-uploader-file-icon-color": "var(--dark-sub-font-color)",
  "uploader-file-icon-color": "var(--sub-font-color)",
  "uploader-item-text-font-size": "0.28rem",
  "uploader-item-border-radius": "0.04rem",
  "dark-uploader-item-text-color": "var(--dark-font-color)",
  "uploader-item-text-color": "var(--font-color)",
  "uploader-loaded-icon-font-size": "0.32rem",
  "dark-uploader-loaded-icon-color": "var(--dark-success-color)",
  "uploader-loaded-icon-color": "var(--success-color)",
  "uploader-error-text-font-size": "0.24rem",
  "dark-uploader-error-text-color": "var(--dark-primary-color)",
  "uploader-error-text-color": "var(--primary-color)",
  "uploader-delete-icon-font-size": "0.24rem",
  "uploader-delete-icon-padding-left": "0.24rem",
  "dark-uploader-delete-icon-color": "var(--dark-sub-font-color)",
  "uploader-delete-icon-color": "var(--sub-font-color)",
  "dark-uploader-disabled-delete-icon-color": "var(--dark-disabled-color)",
  "uploader-disabled-delete-icon-color": "var(--disabled-color)",
  "dark-uploader-item-text-error-color": "var(--dark-danger-color)",
  "uploader-item-text-error-color": "var(--danger-color)"
};
var _default = tokens;
exports["default"] = _default;