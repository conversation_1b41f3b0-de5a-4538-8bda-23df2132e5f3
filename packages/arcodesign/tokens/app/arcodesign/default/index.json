{"actionSheetBorderRadius": {"cssKey": "action-sheet-border-radius", "desc": "动作面板顶部圆角值", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "动作面板顶部圆角值", "en": "ActionSheet top border radius"}}, "actionSheetCancelBorderColor": {"cssKey": "action-sheet-cancel-border-color", "desc": "动作面板取消按钮顶线颜色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "动作面板取消按钮顶线颜色", "en": "Top border color of ActionSheet cancel button"}}, "actionSheetCancelBorderWidth": {"cssKey": "action-sheet-cancel-border-width", "desc": "动作面板取消按钮顶线宽度", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "动作面板取消按钮顶线宽度", "en": "Top border Heigt of ActionSheet cancel button"}}, "actionSheetHeaderPadding": {"cssKey": "action-sheet-header-padding", "desc": "动作面板头部内容内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "动作面板头部内容内边距", "en": "ActionSheet header padding"}}, "actionSheetItemFontSize": {"cssKey": "action-sheet-item-font-size", "desc": "动作面板选项字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "动作面板选项字体大小", "en": "ActionSheet item font size"}}, "actionSheetItemHeight": {"cssKey": "action-sheet-item-height", "desc": "动作面板选项高度", "override": "", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "动作面板选项高度", "en": "AactionSheet item height"}}, "actionSheetSubTitleFontSize": {"cssKey": "action-sheet-sub-title-font-size", "desc": "动作面板头部描述字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "动作面板头部描述字体大小", "en": "ActionSheet subtitle font size"}}, "actionSheetTitleFontSize": {"cssKey": "action-sheet-title-font-size", "desc": "动作面板头部标题字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "动作面板头部标题字体大小", "en": "ActionSheet title font size"}}, "arcoDarkModeSelector": {"cssKey": "arco-dark-mode-selector", "desc": "判断处于暗黑模式的选择器", "override": "", "value": ".arco-theme-dark", "jsValue": ".arco-theme-dark", "staticValue": ".arco-theme-dark", "localeDesc": {"ch": "判断处于暗黑模式的选择器", "en": "Selector to determine whether it is in dark mode"}}, "avatarBackground": {"cssKey": "avatar-background", "desc": "头像组件背景色", "override": "", "value": "#4080FF", "jsValue": "#4080FF", "staticValue": "#4080FF", "localeDesc": {"ch": "头像组件背景色", "en": "Avatar background color"}}, "avatarDefaultOverlapBackground": {"cssKey": "avatar-default-overlap-background", "desc": "头像组件默认头像背景色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "头像组件默认头像背景色", "en": "Default avatar background color of the avatar"}}, "avatarDefaultOverlapLargeSize": {"cssKey": "avatar-default-overlap-large-size", "desc": "large 头像组件默认头像图标大小", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "large 头像组件默认头像图标大小", "en": "Default avatar icon size of the large avatar"}}, "avatarDefaultOverlapMediumSize": {"cssKey": "avatar-default-overlap-medium-size", "desc": "medium 头像组件默认头像图标大小", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "medium 头像组件默认头像图标大小", "en": "Default avatar icon size of the medium avatar"}}, "avatarDefaultOverlapSmallSize": {"cssKey": "avatar-default-overlap-small-size", "desc": "small 头像组件默认头像图标大小", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "small 头像组件默认头像图标大小", "en": "Default avatar icon size of the small avatar"}}, "avatarDefaultOverlapSmallerSize": {"cssKey": "avatar-default-overlap-smaller-size", "desc": "smaller 头像组件默认头像图标大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "smaller 头像组件默认头像图标大小", "en": "Default avatar icon size of the smaller avatar"}}, "avatarDefaultOverlapUltraSmallSize": {"cssKey": "avatar-default-overlap-ultra-small-size", "desc": "ultra-small 头像组件默认头像图标大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "ultra-small 头像组件默认头像图标大小", "en": "Default avatar icon size of the ultra-small avatar"}}, "avatarDescColor": {"cssKey": "avatar-desc-color", "desc": "头像组件辅助信息文字颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "头像组件辅助信息文字颜色", "en": "Auxiliary information font color of avatar"}}, "avatarDescLargeFontSize": {"cssKey": "avatar-desc-large-font-size", "desc": "large 头像组件辅助信息文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "large 头像组件辅助信息文字大小", "en": "Auxiliary information font size of large avatar"}}, "avatarDescLargeLineHeight": {"cssKey": "avatar-desc-large-line-height", "desc": "large 头像组件辅助信息文字行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "large 头像组件辅助信息文字行高", "en": "Auxiliary information font line height of large avatar"}}, "avatarDescLargeMarginTop": {"cssKey": "avatar-desc-large-margin-top", "desc": "large 头像组件用户名和辅助信息间距的大小", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "large 头像组件用户名和辅助信息间距的大小", "en": "The distance of the username and auxiliary information of large avatar"}}, "avatarDescMediumFontSize": {"cssKey": "avatar-desc-medium-font-size", "desc": "medium 头像组件辅助信息文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "medium 头像组件辅助信息文字大小", "en": "Auxiliary information font size of medium avatar"}}, "avatarDescMediumLineHeight": {"cssKey": "avatar-desc-medium-line-height", "desc": "medium 头像组件辅助信息文字行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "medium 头像组件辅助信息文字行高", "en": "Auxiliary information font line height of medium avatar"}}, "avatarDescMediumMarginTop": {"cssKey": "avatar-desc-medium-margin-top", "desc": "medium 头像组件用户名和辅助信息间距的大小", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "medium 头像组件用户名和辅助信息间距的大小", "en": "The distance of the username and auxiliary information of medium avatar"}}, "avatarDescSmallFontSize": {"cssKey": "avatar-desc-small-font-size", "desc": "small 头像组件辅助信息文字大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "small 头像组件辅助信息文字大小", "en": "Auxiliary information font size of small avatar"}}, "avatarDescSmallLineHeight": {"cssKey": "avatar-desc-small-line-height", "desc": "small 头像组件辅助信息文字行高", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "small 头像组件辅助信息文字行高", "en": "Auxiliary information font line height of small avatar"}}, "avatarDescSmallMarginTop": {"cssKey": "avatar-desc-small-margin-top", "desc": "small 头像组件用户名和辅助信息间距的大小", "override": "", "value": "~`pxtorem(0)`", "jsValue": "@getRem@0", "staticValue": "0", "localeDesc": {"ch": "small 头像组件用户名和辅助信息间距的大小", "en": "The distance of the username and auxiliary information of small avatar"}}, "avatarDescSmallerFontSize": {"cssKey": "avatar-desc-smaller-font-size", "desc": "smaller 头像组件辅助信息文字大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "smaller 头像组件辅助信息文字大小", "en": "Auxiliary information font size of smaller avatar"}}, "avatarDescSmallerLineHeight": {"cssKey": "avatar-desc-smaller-line-height", "desc": "smaller 头像组件辅助信息文字行高", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "smaller 头像组件辅助信息文字行高", "en": "Auxiliary information font line height of smaller avatar"}}, "avatarDescSmallerMarginTop": {"cssKey": "avatar-desc-smaller-margin-top", "desc": "smaller 头像组件用户名和辅助信息间距的大小", "override": "", "value": "~`pxtorem(0)`", "jsValue": "@getRem@0", "staticValue": "0", "localeDesc": {"ch": "smaller 头像组件用户名和辅助信息间距的大小", "en": "The distance of the username and auxiliary information of smaller avatar"}}, "avatarDescUltraSmallFontSize": {"cssKey": "avatar-desc-ultra-small-font-size", "desc": "ultra-small 头像组件辅助信息文字大小", "override": "", "value": "~`pxtorem(10)`", "jsValue": "@getRem@10", "staticValue": "0.2rem", "localeDesc": {"ch": "ultra-small 头像组件辅助信息文字大小", "en": "Auxiliary information font size of ultra-small avatar"}}, "avatarDescUltraSmallLineHeight": {"cssKey": "avatar-desc-ultra-small-line-height", "desc": "ultra-small 头像组件辅助信息文字行高", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "ultra-small 头像组件辅助信息文字行高", "en": "Auxiliary information font line height of ultra-small avatar"}}, "avatarDescUltraSmallMarginTop": {"cssKey": "avatar-desc-ultra-small-margin-top", "desc": "ultra-small 头像组件用户名和辅助信息间距的大小", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "ultra-small 头像组件用户名和辅助信息间距的大小", "en": "The distance of the username and auxiliary information of ultra-small avatar"}}, "avatarGroupBorderColor": {"cssKey": "avatar-group-border-color", "desc": "头像叠层border颜色", "override": "", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "头像叠层border颜色", "en": "Border color of avatar group"}}, "avatarGroupLargeSizeBorder": {"cssKey": "avatar-group-large-size-border", "desc": "large 头像叠层组件头像border宽度", "override": "", "value": "~`pxtorem(1.5)`", "jsValue": "@getRem@1.5", "staticValue": "0.03rem", "localeDesc": {"ch": "large 头像叠层组件头像border宽度", "en": "Avatar border width of the large avatar group"}}, "avatarGroupLargeSizeOffset": {"cssKey": "avatar-group-large-size-offset", "desc": "large 头像叠层组件头像重叠偏移量", "override": "", "value": "~`pxtorem(-12)`", "jsValue": "@getRem@-12", "staticValue": "-0.24rem", "localeDesc": {"ch": "large 头像叠层组件头像重叠偏移量", "en": "Avatar overlap offset of the large avatar group"}}, "avatarGroupMediumSizeBorder": {"cssKey": "avatar-group-medium-size-border", "desc": "medium 头像叠层组件头像border宽度", "override": "", "value": "~`pxtorem(1.5)`", "jsValue": "@getRem@1.5", "staticValue": "0.03rem", "localeDesc": {"ch": "medium 头像叠层组件头像border宽度", "en": "Avatar border width of the medium avatar group"}}, "avatarGroupMediumSizeOffset": {"cssKey": "avatar-group-medium-size-offset", "desc": "medium 头像叠层组件头像重叠偏移量", "override": "", "value": "~`pxtorem(-12)`", "jsValue": "@getRem@-12", "staticValue": "-0.24rem", "localeDesc": {"ch": "medium 头像叠层组件头像重叠偏移量", "en": "Avatar overlap offset of the medium avatar group"}}, "avatarGroupSmallSizeBorder": {"cssKey": "avatar-group-small-size-border", "desc": "small 头像叠层组件头像border宽度", "override": "", "value": "~`pxtorem(1.5)`", "jsValue": "@getRem@1.5", "staticValue": "0.03rem", "localeDesc": {"ch": "small 头像叠层组件头像border宽度", "en": "Avatar border width of the small avatar group"}}, "avatarGroupSmallSizeOffset": {"cssKey": "avatar-group-small-size-offset", "desc": "small 头像叠层组件头像重叠偏移量", "override": "", "value": "~`pxtorem(-12)`", "jsValue": "@getRem@-12", "staticValue": "-0.24rem", "localeDesc": {"ch": "small 头像叠层组件头像重叠偏移量", "en": "Avatar overlap offset of the small avatar group"}}, "avatarGroupSmallerSizeBorder": {"cssKey": "avatar-group-smaller-size-border", "desc": "smaller 头像叠层组件头像border宽度", "override": "", "value": "~`pxtorem(1)`", "jsValue": "@getRem@1", "staticValue": "0.02rem", "localeDesc": {"ch": "smaller 头像叠层组件头像border宽度", "en": "Avatar border width of the smaller avatar group"}}, "avatarGroupSmallerSizeOffset": {"cssKey": "avatar-group-smaller-size-offset", "desc": "smaller 头像叠层组件头像重叠偏移量", "override": "", "value": "~`pxtorem(-8)`", "jsValue": "@getRem@-8", "staticValue": "-0.16rem", "localeDesc": {"ch": "smaller 头像叠层组件头像重叠偏移量", "en": "Avatar overlap offset of the smaller avatar group"}}, "avatarGroupUltraSmallSizeBorder": {"cssKey": "avatar-group-ultra-small-size-border", "desc": "ultra-small 头像叠层组件头像border宽度", "override": "", "value": "~`pxtorem(1)`", "jsValue": "@getRem@1", "staticValue": "0.02rem", "localeDesc": {"ch": "ultra-small 头像叠层组件头像border宽度", "en": "Avatar border width of the ultra-small avatar group"}}, "avatarGroupUltraSmallSizeOffset": {"cssKey": "avatar-group-ultra-small-size-offset", "desc": "ultra-small 头像叠层组件头像重叠偏移量", "override": "", "value": "~`pxtorem(-8)`", "jsValue": "@getRem@-8", "staticValue": "-0.16rem", "localeDesc": {"ch": "ultra-small 头像叠层组件头像重叠偏移量", "en": "Avatar overlap offset of the ultra-small avatar group"}}, "avatarInfoBoxLargeSize": {"cssKey": "avatar-info-box-large-size", "desc": "large大小带有辅助信息的头像组件容器高度", "override": "", "value": "~`px<PERSON><PERSON>(88)`", "jsValue": "@getRem@88", "staticValue": "1.76rem", "localeDesc": {"ch": "large大小带有辅助信息的头像组件容器高度", "en": "Container height of large avatar with auxiliary information"}}, "avatarInfoBoxMediumSize": {"cssKey": "avatar-info-box-medium-size", "desc": "medium大小带有辅助信息的头像组件容器高度", "override": "", "value": "~`px<PERSON>m(80)`", "jsValue": "@getRem@80", "staticValue": "1.6rem", "localeDesc": {"ch": "medium大小带有辅助信息的头像组件容器高度", "en": "Container height of medium avatar with auxiliary information"}}, "avatarInfoBoxSmallSize": {"cssKey": "avatar-info-box-small-size", "desc": "small大小带有辅助信息的头像组件容器高度", "override": "", "value": "~`px<PERSON>m(80)`", "jsValue": "@getRem@80", "staticValue": "1.6rem", "localeDesc": {"ch": "small大小带有辅助信息的头像组件容器高度", "en": "Container height of small avatar with auxiliary information"}}, "avatarInfoBoxSmallerSize": {"cssKey": "avatar-info-box-smaller-size", "desc": "smaller大小带有辅助信息的头像组件容器高度", "override": "", "value": "~`px<PERSON>m(64)`", "jsValue": "@getRem@64", "staticValue": "1.28rem", "localeDesc": {"ch": "smaller大小带有辅助信息的头像组件容器高度", "en": "Container height of smaller avatar with auxiliary information"}}, "avatarInfoBoxUltraSmallSize": {"cssKey": "avatar-info-box-ultra-small-size", "desc": "ultra-small大小带有辅助信息的头像组件容器高度", "override": "", "value": "~`px<PERSON><PERSON>(56)`", "jsValue": "@getRem@56", "staticValue": "1.12rem", "localeDesc": {"ch": "ultra-small大小带有辅助信息的头像组件容器高度", "en": "Container height of ultra-small avatar with auxiliary information"}}, "avatarLargeSize": {"cssKey": "avatar-large-size", "desc": "large 头像组件的大小", "override": "", "value": "~`px<PERSON><PERSON>(56)`", "jsValue": "@getRem@56", "staticValue": "1.12rem", "localeDesc": {"ch": "large 头像组件的大小", "en": "Size of the large avatar"}}, "avatarLargeTextFontSize": {"cssKey": "avatar-large-text-font-size", "desc": "large 字体头像字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "large 字体头像字体大小", "en": "Font size of the large text avatar"}}, "avatarMediumSize": {"cssKey": "avatar-medium-size", "desc": "medium 头像组件的大小", "override": "", "value": "~`pxtorem(48)`", "jsValue": "@getRem@48", "staticValue": "0.96rem", "localeDesc": {"ch": "medium 头像组件的大小", "en": "Size of the medium avatar"}}, "avatarMediumTextFontSize": {"cssKey": "avatar-medium-text-font-size", "desc": "medium 字体头像字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "medium 字体头像字体大小", "en": "Font size of the medium text avatar"}}, "avatarNameColor": {"cssKey": "avatar-name-color", "desc": "头像组件用户名字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "头像组件用户名字体颜色", "en": "Avatar username font color"}}, "avatarNameLargeFontSize": {"cssKey": "avatar-name-large-font-size", "desc": "large 头像组件用户名文字大小", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "large 头像组件用户名文字大小", "en": "Username font size of large avatar"}}, "avatarNameLargeLineHeight": {"cssKey": "avatar-name-large-line-height", "desc": "large 头像组件用户名文字行高", "override": "", "value": "~`px<PERSON>m(26)`", "jsValue": "@getRem@26", "staticValue": "0.52rem", "localeDesc": {"ch": "large 头像组件用户名文字行高", "en": "Username font line height of large avatar"}}, "avatarNameMediumFontSize": {"cssKey": "avatar-name-medium-font-size", "desc": "medium 头像组件用户名文字大小", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "medium 头像组件用户名文字大小", "en": "Username font size of medium avatar"}}, "avatarNameMediumLineHeight": {"cssKey": "avatar-name-medium-line-height", "desc": "medium 头像组件用户名文字行高", "override": "", "value": "~`px<PERSON>m(26)`", "jsValue": "@getRem@26", "staticValue": "0.52rem", "localeDesc": {"ch": "medium 头像组件用户名文字行高", "en": "Username font line height of medium avatar"}}, "avatarNameSmallFontSize": {"cssKey": "avatar-name-small-font-size", "desc": "small 头像组件用户名文字大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "small 头像组件用户名文字大小", "en": "Username font size of small avatar"}}, "avatarNameSmallLineHeight": {"cssKey": "avatar-name-small-line-height", "desc": "small 头像组件用户名文字行高", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "small 头像组件用户名文字行高", "en": "Username font line height of small avatar"}}, "avatarNameSmallerFontSize": {"cssKey": "avatar-name-smaller-font-size", "desc": "smaller 头像组件用户名文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "smaller 头像组件用户名文字大小", "en": "Username font size of smaller avatar"}}, "avatarNameSmallerLineHeight": {"cssKey": "avatar-name-smaller-line-height", "desc": "smaller 头像组件用户名文字行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "smaller 头像组件用户名文字行高", "en": "Username font line height of smaller avatar"}}, "avatarNameUltraSmallFontSize": {"cssKey": "avatar-name-ultra-small-font-size", "desc": "ultra-small 头像组件用户名文字大小", "override": "", "value": "~`px<PERSON>m(13)`", "jsValue": "@getRem@13", "staticValue": "0.26rem", "localeDesc": {"ch": "ultra-small 头像组件用户名文字大小", "en": "Username font size of ultra-small avatar"}}, "avatarNameUltraSmallLineHeight": {"cssKey": "avatar-name-ultra-small-line-height", "desc": "ultra-small 头像组件用户名文字行高", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "ultra-small 头像组件用户名文字行高", "en": "Username font line height of ultra-small avatar"}}, "avatarSmallSize": {"cssKey": "avatar-small-size", "desc": "small 头像组件的大小", "override": "", "value": "~`pxtorem(40)`", "jsValue": "@getRem@40", "staticValue": "0.8rem", "localeDesc": {"ch": "small 头像组件的大小", "en": "Size of the small avatar"}}, "avatarSmallTextFontSize": {"cssKey": "avatar-small-text-font-size", "desc": "small 字体头像字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "small 字体头像字体大小", "en": "Font size of the small text avatar"}}, "avatarSmallerSize": {"cssKey": "avatar-smaller-size", "desc": "smaller 头像组件的大小", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "smaller 头像组件的大小", "en": "Size of the smaller avatar"}}, "avatarSmallerTextFontSize": {"cssKey": "avatar-smaller-text-font-size", "desc": "smaller 字体头像字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "smaller 字体头像字体大小", "en": "Font size of the smaller text avatar"}}, "avatarTextFontColor": {"cssKey": "avatar-text-font-color", "desc": "字体头像文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "字体头像文字颜色", "en": "Text avatar font color"}}, "avatarUltraSmallSize": {"cssKey": "avatar-ultra-small-size", "desc": "ultra-small 头像组件的大小", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "ultra-small 头像组件的大小", "en": "Size of the ultra-small avatar"}}, "avatarUltraSmallTextFontSize": {"cssKey": "avatar-ultra-small-text-font-size", "desc": "ultra-small 字体头像字体大小", "override": "", "value": "~`pxtorem(10)`", "jsValue": "@getRem@10", "staticValue": "0.2rem", "localeDesc": {"ch": "ultra-small 字体头像字体大小", "en": "Font size of the ultra-small text avatar"}}, "backgroundColor": {"cssKey": "background-color", "desc": "基础背景色", "override": "", "value": "#FFFFFF", "jsValue": "#FFFFFF", "staticValue": "#FFFFFF", "localeDesc": {"ch": "基础背景色", "en": "Base background color"}}, "badgeBackgroundColor": {"cssKey": "badge-background-color", "desc": "徽标背景色", "override": "", "value": "@danger-color", "jsValue": "@global@dangerColor", "staticValue": "#F53F3F", "localeDesc": {"ch": "徽标背景色", "en": "Badge background color"}}, "badgeBorderColor": {"cssKey": "badge-border-color", "desc": "徽标带边框时的边框颜色", "override": "", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "徽标带边框时的边框颜色", "en": "Border color of Badge with border"}}, "badgeBorderRadius": {"cssKey": "badge-border-radius", "desc": "徽标带文字样式的圆角值", "override": "", "value": "100PX", "jsValue": "100PX", "staticValue": "100PX", "localeDesc": {"ch": "徽标带文字样式的圆角值", "en": "Border radius of Badge with text"}}, "badgeDotDeviation": {"cssKey": "badge-dot-deviation", "desc": "徽标无文字小圆点样式相对于右上角的缩进", "override": "", "value": "-4PX", "jsValue": "-4PX", "staticValue": "-4PX", "localeDesc": {"ch": "徽标无文字小圆点样式相对于右上角的缩进", "en": "indent relative to right of Badge with text"}}, "badgeDotWidth": {"cssKey": "badge-dot-width", "desc": "徽标无文字小圆点样式大小", "override": "", "value": "8PX", "jsValue": "8PX", "staticValue": "8PX", "localeDesc": {"ch": "徽标无文字小圆点样式大小", "en": "Badge dot Width"}}, "badgeFontSize": {"cssKey": "badge-font-size", "desc": "徽标字体大小", "override": "", "value": "12PX", "jsValue": "12PX", "staticValue": "12PX", "localeDesc": {"ch": "徽标字体大小", "en": "Badge font size"}}, "badgeTextColor": {"cssKey": "badge-text-color", "desc": "徽标文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "徽标文字颜色", "en": "Badge font color"}}, "badgeTextDeviation": {"cssKey": "badge-text-deviation", "desc": "徽标带文字样式相对于右上角的缩进", "override": "", "value": "-8PX", "jsValue": "-8PX", "staticValue": "-8PX", "localeDesc": {"ch": "徽标带文字样式相对于右上角的缩进", "en": "indent relative to top right of Badge with text"}}, "badgeTextPadding": {"cssKey": "badge-text-padding", "desc": "徽标带文字样式横向内边距", "override": "", "value": "4PX", "jsValue": "4PX", "staticValue": "4PX", "localeDesc": {"ch": "徽标带文字样式横向内边距", "en": "Horizontal padding of Badge with text"}}, "badgeTextWidth": {"cssKey": "badge-text-width", "desc": "徽标带文字样式的高度及最小宽度", "override": "", "value": "16PX", "jsValue": "16PX", "staticValue": "16PX", "localeDesc": {"ch": "徽标带文字样式的高度及最小宽度", "en": "Height and minimum width of Badge with text"}}, "baseFontSize": {"cssKey": "base-font-size", "desc": "rem 转换使用的基础字号", "override": "", "value": "50", "jsValue": "50", "staticValue": "50", "localeDesc": {"ch": "rem 转换使用的基础字号", "en": "Base font size used for rem conversion"}}, "buttonDefaultBackground": {"cssKey": "button-default-background", "desc": "default 类型按钮背景色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "default 类型按钮背景色", "en": "Default button background color"}}, "buttonDefaultClickedBackground": {"cssKey": "button-default-clicked-background", "desc": "default 类型按钮点击态背景色", "override": "", "value": "@primary-disabled-color", "jsValue": "@global@primaryDisabledColor", "staticValue": "#94BFFF", "localeDesc": {"ch": "default 类型按钮点击态背景色", "en": "Background color of default button in click state"}}, "buttonDefaultDisabledBackground": {"cssKey": "button-default-disabled-background", "desc": "default 类型按钮禁用态背景色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "default 类型按钮禁用态背景色", "en": "Background color of disabled default button"}}, "buttonDefaultDisabledTextColor": {"cssKey": "button-default-disabled-text-color", "desc": "default 类型按钮禁用态文字颜色", "override": "button-default-disabled-text", "value": "@primary-disabled-color", "jsValue": "@global@primaryDisabledColor", "staticValue": "#94BFFF", "localeDesc": {"ch": "default 类型按钮禁用态文字颜色", "en": "Font color of disabled default button"}}, "buttonDefaultTextColor": {"cssKey": "button-default-text-color", "desc": "default 类型按钮文字颜色", "override": "button-default-text", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "default 类型按钮文字颜色", "en": "Font color of default button"}}, "buttonGhostBackground": {"cssKey": "button-ghost-background", "desc": "ghost 类型按钮背景色", "override": "", "value": "transparent", "jsValue": "transparent", "staticValue": "transparent", "localeDesc": {"ch": "ghost 类型按钮背景色", "en": "Ghost button background color"}}, "buttonGhostClickedBackground": {"cssKey": "button-ghost-clicked-background", "desc": "ghost 类型按钮点击态背景色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "ghost 类型按钮点击态背景色", "en": "Background color of ghost button in click state"}}, "buttonGhostDisabledBackground": {"cssKey": "button-ghost-disabled-background", "desc": "ghost 类型按钮禁用态背景色", "override": "", "value": "transparent", "jsValue": "transparent", "staticValue": "transparent", "localeDesc": {"ch": "ghost 类型按钮禁用态背景色", "en": "Background color of disabled ghost button"}}, "buttonGhostDisabledTextColor": {"cssKey": "button-ghost-disabled-text-color", "desc": "ghost 类型按钮禁用态文字颜色", "override": "button-ghost-disabled-text", "value": "@primary-disabled-color", "jsValue": "@global@primaryDisabledColor", "staticValue": "#94BFFF", "localeDesc": {"ch": "ghost 类型按钮禁用态文字颜色", "en": "Font color of disabled ghost button"}}, "buttonGhostTextColor": {"cssKey": "button-ghost-text-color", "desc": "ghost 类型按钮文字颜色", "override": "button-ghost-text", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "ghost 类型按钮文字颜色", "en": "Font color of ghost button"}}, "buttonHugeHeight": {"cssKey": "button-huge-height", "desc": "huge 按钮高度", "override": "", "value": "~`px<PERSON><PERSON>(44)`", "jsValue": "@getRem@44", "staticValue": "0.88rem", "localeDesc": {"ch": "huge 按钮高度", "en": "Huge button height"}}, "buttonHugePadding": {"cssKey": "button-huge-padding", "desc": "huge 按钮内部padding", "override": "", "value": "0 ~`px<PERSON><PERSON>(16)`", "jsValue": "0 @getRem@16", "staticValue": "0 0.32rem", "localeDesc": {"ch": "huge 按钮内部padding", "en": "Huge button padding"}}, "buttonHugeTextSize": {"cssKey": "button-huge-text-size", "desc": "huge 按钮文字大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "huge 按钮文字大小", "en": "Huge button font size"}}, "buttonIconTextGutter": {"cssKey": "button-icon-text-gutter", "desc": "按钮图标与文字的间距", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "按钮图标与文字的间距", "en": "Gutter between icon and text"}}, "buttonLargeHeight": {"cssKey": "button-large-height", "desc": "large 按钮高度", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "large 按钮高度", "en": "Large button height"}}, "buttonLargePadding": {"cssKey": "button-large-padding", "desc": "large 按钮内部padding", "override": "", "value": "0 ~`px<PERSON><PERSON>(16)`", "jsValue": "0 @getRem@16", "staticValue": "0 0.32rem", "localeDesc": {"ch": "large 按钮内部padding", "en": "Large button padding"}}, "buttonLargeTextSize": {"cssKey": "button-large-text-size", "desc": "large 按钮文字大小", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "large 按钮文字大小", "en": "Large button font size"}}, "buttonLineHeight": {"cssKey": "button-line-height", "desc": "按钮的行高", "override": "button-border", "value": "1.2", "jsValue": "1.2", "staticValue": "1.2", "localeDesc": {"ch": "按钮的行高", "en": "Button line height"}}, "buttonMediumHeight": {"cssKey": "button-medium-height", "desc": "medium 按钮高度", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "medium 按钮高度", "en": "Medium button height"}}, "buttonMediumPadding": {"cssKey": "button-medium-padding", "desc": "medium 按钮内部padding", "override": "", "value": "0 ~`px<PERSON><PERSON>(16)`", "jsValue": "0 @getRem@16", "staticValue": "0 0.32rem", "localeDesc": {"ch": "medium 按钮内部padding", "en": "Medium button padding"}}, "buttonMediumTextSize": {"cssKey": "button-medium-text-size", "desc": "medium 按钮文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "medium 按钮文字大小", "en": "Medium button font size"}}, "buttonMiniHeight": {"cssKey": "button-mini-height", "desc": "mini 按钮高度", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "mini 按钮高度", "en": "Mini button height"}}, "buttonMiniPadding": {"cssKey": "button-mini-padding", "desc": "mini 按钮内部padding", "override": "", "value": "0 ~`pxtorem(8)`", "jsValue": "0 @getRem@8", "staticValue": "0 0.16rem", "localeDesc": {"ch": "mini 按钮内部padding", "en": "Mini button padding"}}, "buttonMiniTextSize": {"cssKey": "button-mini-text-size", "desc": "mini 按钮文字大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "mini 按钮文字大小", "en": "Mini button font size"}}, "buttonPrimaryBackground": {"cssKey": "button-primary-background", "desc": "primary 类型按钮背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "primary 类型按钮背景色", "en": "Primary button background color"}}, "buttonPrimaryClickedBackground": {"cssKey": "button-primary-clicked-background", "desc": "primary 类型按钮点击态背景色", "override": "", "value": "#0E42D2", "jsValue": "#0E42D2", "staticValue": "#0E42D2", "localeDesc": {"ch": "primary 类型按钮点击态背景色", "en": "Background color in click state of primary button"}}, "buttonPrimaryDisabledBackground": {"cssKey": "button-primary-disabled-background", "desc": "primary 类型按钮禁用态背景色", "override": "", "value": "@primary-disabled-color", "jsValue": "@global@primaryDisabledColor", "staticValue": "#94BFFF", "localeDesc": {"ch": "primary 类型按钮禁用态背景色", "en": "Background color of disabled primary button"}}, "buttonPrimaryDisabledTextColor": {"cssKey": "button-primary-disabled-text-color", "desc": "primary 类型按钮禁用态文字颜色", "override": "button-primary-disabled-text", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "primary 类型按钮禁用态文字颜色", "en": "Font color of disabled primary button"}}, "buttonPrimaryTextColor": {"cssKey": "button-primary-text-color", "desc": "primary 类型按钮文字颜色", "override": "button-primary-text", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "primary 类型按钮文字颜色", "en": "Font color of primary button"}}, "buttonRadius": {"cssKey": "button-radius", "desc": "shape=semi时圆角大小", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "shape=semi时圆角大小", "en": "Button border radius when shape=semi"}}, "buttonSmallHeight": {"cssKey": "button-small-height", "desc": "small 按钮高度", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "small 按钮高度", "en": "Small button height"}}, "buttonSmallPadding": {"cssKey": "button-small-padding", "desc": "small 按钮内部padding", "override": "", "value": "0 ~`pxtorem(8)`", "jsValue": "0 @getRem@8", "staticValue": "0 0.16rem", "localeDesc": {"ch": "small 按钮内部padding", "en": "Small button padding"}}, "buttonSmallTextSize": {"cssKey": "button-small-text-size", "desc": "small 按钮文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "small 按钮文字大小", "en": "Small button font size"}}, "cardBackgroundColor": {"cssKey": "card-background-color", "desc": "卡片类元素背景色", "override": "", "value": "#F7F8FA", "jsValue": "#F7F8FA", "staticValue": "#F7F8FA", "localeDesc": {"ch": "卡片类元素背景色", "en": "Background color of card elements"}}, "carouselAutoTransition": {"cssKey": "carousel-auto-transition", "desc": "轮播图自动轮播时的动画曲线", "override": "", "value": "cubic-bezier(0.66, 0, 0.34, 1)", "jsValue": "cubic-bezier(0.66, 0, 0.34, 1)", "staticValue": "cubic-bezier(0.66, 0, 0.34, 1)", "localeDesc": {"ch": "轮播图自动轮播时的动画曲线", "en": "The animation curve when the carousel image rotates automatically"}}, "carouselCircleIndicatorGutter": {"cssKey": "carousel-circle-indicator-gutter", "desc": "轮播图圆点形指示器之间的间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "轮播图圆点形指示器之间的间距", "en": "Gutter between items of the carousel dot indicator"}}, "carouselCircleIndicatorSize": {"cssKey": "carousel-circle-indicator-size", "desc": "轮播图圆点形指示器大小", "override": "", "value": "6PX", "jsValue": "6PX", "staticValue": "6PX", "localeDesc": {"ch": "轮播图圆点形指示器大小", "en": "Size of item of the carousel dot indicator"}}, "carouselIndicatorActiveBackground": {"cssKey": "carousel-indicator-active-background", "desc": "轮播图指示器高亮背景色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "轮播图指示器高亮背景色", "en": "Carousel indicator active background color"}}, "carouselIndicatorActiveInverseBackground": {"cssKey": "carousel-indicator-active-inverse-background", "desc": "轮播图指示器放轮播图外部时高亮背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "轮播图指示器放轮播图外部时高亮背景色", "en": "The active background color of the carousel indicator  which is outside the carousel"}}, "carouselIndicatorBackground": {"cssKey": "carousel-indicator-background", "desc": "轮播图指示器背景色", "override": "", "value": "rgba(255, 255, 255, 0.5)", "jsValue": "rgba(255, 255, 255, 0.5)", "staticValue": "rgba(255, 255, 255, 0.5)", "localeDesc": {"ch": "轮播图指示器背景色", "en": "Carousel indicator background color"}}, "carouselIndicatorInverseBackground": {"cssKey": "carousel-indicator-inverse-background", "desc": "轮播图指示器放轮播图外部时背景色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "轮播图指示器放轮播图外部时背景色", "en": "The background color of the carousel indicator which is outside the carousel"}}, "carouselIndicatorOutsidePadding": {"cssKey": "carousel-indicator-outside-padding", "desc": "轮播图指示器放轮播图外部时，指示器容器内边距", "override": "", "value": "~`pxtorem(8)` 0 ~`pxtorem(5)`", "jsValue": "@getRem@8 0 @getRem@5", "staticValue": "0.16rem 0 0.1rem", "localeDesc": {"ch": "轮播图指示器放轮播图外部时，指示器容器内边距", "en": "the padding of the container of the indicator which is in outside the carousel"}}, "carouselIndicatorPosition": {"cssKey": "carousel-indicator-position", "desc": "轮播图指示器距离边缘的距离", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "轮播图指示器距离边缘的距离", "en": "The distance of the carousel indicator from the edge"}}, "carouselIndicatorSafePadding": {"cssKey": "carousel-indicator-safe-padding", "desc": "轮播图指示器不居中时，距离两侧的安全距离", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "轮播图指示器不居中时，距离两侧的安全距离", "en": "Safe distance from both sides when the carousel indicator is not centered"}}, "carouselItemTextBackground": {"cssKey": "carousel-item-text-background", "desc": "轮播图滑块文字背景色", "override": "", "value": "linear-gradient(180deg, rgba(0, 0, 0, 0) 5.18%, rgba(0, 0, 0, 0.15) 100%)", "jsValue": "linear-gradient(180deg, rgba(0, 0, 0, 0) 5.18%, rgba(0, 0, 0, 0.15) 100%)", "staticValue": "linear-gradient(180deg, rgba(0, 0, 0, 0) 5.18%, rgba(0, 0, 0, 0.15) 100%)", "localeDesc": {"ch": "轮播图滑块文字背景色", "en": "Background color of the carousel sliders"}}, "carouselItemTextColor": {"cssKey": "carousel-item-text-color", "desc": "轮播图滑块文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "轮播图滑块文字颜色", "en": "Font color of the carousel sliders"}}, "carouselItemTextFontSize": {"cssKey": "carousel-item-text-font-size", "desc": "轮播图滑块文字大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "轮播图滑块文字大小", "en": "Font size of the carousel sliders"}}, "carouselItemTextHeight": {"cssKey": "carousel-item-text-height", "desc": "轮播图滑块文字高度", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "轮播图滑块文字高度", "en": "Font height of the carousel sliders"}}, "carouselItemTextPadding": {"cssKey": "carousel-item-text-padding", "desc": "轮播图滑块文字内边距", "override": "", "value": "0 ~`px<PERSON><PERSON>(12)`", "jsValue": "0 @getRem@12", "staticValue": "0 0.24rem", "localeDesc": {"ch": "轮播图滑块文字内边距", "en": "<PERSON><PERSON> Padding of the carousel sliders"}}, "carouselSlideTransition": {"cssKey": "carousel-slide-transition", "desc": "轮播图手势滑动时手指抬起后的动画曲线", "override": "", "value": "cubic-bezier(0.32, 0.94, 0.6, 1)", "jsValue": "cubic-bezier(0.32, 0.94, 0.6, 1)", "staticValue": "cubic-bezier(0.32, 0.94, 0.6, 1)", "localeDesc": {"ch": "轮播图手势滑动时手指抬起后的动画曲线", "en": "The animation curve after the finger is lifted when the carousel gesture slides"}}, "carouselSquareIndicatorGutter": {"cssKey": "carousel-square-indicator-gutter", "desc": "轮播图方形指示器之间的间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "轮播图方形指示器之间的间距", "en": "Gutter between items of carousel square indicator"}}, "carouselSquareIndicatorHeight": {"cssKey": "carousel-square-indicator-height", "desc": "轮播图方形指示器高度", "override": "", "value": "3PX", "jsValue": "3PX", "staticValue": "3PX", "localeDesc": {"ch": "轮播图方形指示器高度", "en": "Height of item of the carousel square indicator"}}, "carouselSquareIndicatorWidth": {"cssKey": "carousel-square-indicator-width", "desc": "轮播图方形指示器宽度", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "轮播图方形指示器宽度", "en": "Width of item of the carousel square indicator"}}, "cellArrowColor": {"cssKey": "cell-arrow-color", "desc": "单元格右箭头图标颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "单元格右箭头图标颜色", "en": "Cell right arrow icon color"}}, "cellArrowFontSize": {"cssKey": "cell-arrow-font-size", "desc": "单元格右箭头自定义图标大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "单元格右箭头自定义图标大小", "en": "Cell right arrow custom icon size"}}, "cellArrowGutter": {"cssKey": "cell-arrow-gutter", "desc": "单元格右箭头图标左侧间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "单元格右箭头图标左侧间距", "en": "Left space of cell right arrow icon"}}, "cellBackgroundColor": {"cssKey": "cell-background-color", "desc": "单元格背景色", "override": "", "value": "@container-background-color", "jsValue": "@global@containerBackgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "单元格背景色", "en": "Cell background color"}}, "cellContentFontSize": {"cssKey": "cell-content-font-size", "desc": "单元格内容文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "单元格内容文字大小", "en": "Cell content font size"}}, "cellDescColor": {"cssKey": "cell-desc-color", "desc": "单元格标签描述文字颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "单元格标签描述文字颜色", "en": "Cell description font color"}}, "cellDescFontSize": {"cssKey": "cell-desc-font-size", "desc": "单元格标签描述文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "单元格标签描述文字大小", "en": "Cell description font size"}}, "cellDescMarginTop": {"cssKey": "cell-desc-margin-top", "desc": "单元格标签描述文字顶部间距", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "单元格标签描述文字顶部间距", "en": "Cell description margin top"}}, "cellExtraFontSize": {"cssKey": "cell-extra-font-size", "desc": "单元格头部和尾部说明文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "单元格头部和尾部说明文字大小", "en": "Head and tail description font size of Cell"}}, "cellExtraLineHeight": {"cssKey": "cell-extra-line-height", "desc": "单元格头部和尾部说明文字行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "单元格头部和尾部说明文字行高", "en": "Head and tail description line height of Cell"}}, "cellExtraPadding": {"cssKey": "cell-extra-padding", "desc": "单元格头部和尾部说明内边距", "override": "", "value": "~`pxtorem(12)` ~`pxtorem(16)`", "jsValue": "@getRem@12 @getRem@16", "staticValue": "0.24rem 0.32rem", "localeDesc": {"ch": "单元格头部和尾部说明内边距", "en": "Head and tail description padding of Cell"}}, "cellFontSize": {"cssKey": "cell-font-size", "desc": "单元格标签文字大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "单元格标签文字大小", "en": "Cell font size"}}, "cellHorizontalPadding": {"cssKey": "cell-horizontal-padding", "desc": "单元格两侧横向间距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "单元格两侧横向间距", "en": "Horizontal padding on both sides of cell"}}, "cellItemHasDescHeight": {"cssKey": "cell-item-has-desc-height", "desc": "单元格有描述文字时的高度", "override": "", "value": "~`px<PERSON><PERSON>(74)`", "jsValue": "@getRem@74", "staticValue": "1.48rem", "localeDesc": {"ch": "单元格有描述文字时的高度", "en": "Cell height with description"}}, "cellItemHeight": {"cssKey": "cell-item-height", "desc": "单元格基础样式高度", "override": "", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "单元格基础样式高度", "en": "Cell height"}}, "cellLabelColor": {"cssKey": "cell-label-color", "desc": "单元格标签文字颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "单元格标签文字颜色", "en": "Cell label font color"}}, "cellLabelGutter": {"cssKey": "cell-label-gutter", "desc": "单元格标签文字右侧间距", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "单元格标签文字右侧间距", "en": "Gutter to the right of cell label"}}, "cellLabelIconColor": {"cssKey": "cell-label-icon-color", "desc": "单元格标签图标颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "单元格标签图标颜色", "en": "Cell label icon color"}}, "cellLabelIconFontSize": {"cssKey": "cell-label-icon-font-size", "desc": "单元格标签图标大小", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "单元格标签图标大小", "en": "Cell label icon size"}}, "cellLabelIconGutter": {"cssKey": "cell-label-icon-gutter", "desc": "单元格标签图标右侧间距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "单元格标签图标右侧间距", "en": "Gutter to the right of cell label icon"}}, "cellTextColor": {"cssKey": "cell-text-color", "desc": "单元格主体文字颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "单元格主体文字颜色", "en": "Cell body font color"}}, "checkboxGroupGutter": {"cssKey": "checkbox-group-gutter", "desc": "checkbox选项组中选项之间的间距", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "checkbox选项组中选项之间的间距", "en": "Gutter between options in checkbox group"}}, "checkboxIconCheckedColor": {"cssKey": "checkbox-icon-checked-color", "desc": "checkbox图标被选中时的颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "checkbox图标被选中时的颜色", "en": "Color of selected checkbox icon"}}, "checkboxIconCheckedDisabledColor": {"cssKey": "checkbox-icon-checked-disabled-color", "desc": "checkbox图标被禁用且选中时的颜色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "checkbox图标被禁用且选中时的颜色", "en": "Color of selected but Disabled checkbox icon"}}, "checkboxIconColor": {"cssKey": "checkbox-icon-color", "desc": "checkbox图标颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "checkbox图标颜色", "en": "Checkbox icon color"}}, "checkboxIconDisabledColor": {"cssKey": "checkbox-icon-disabled-color", "desc": "checkbox图标被禁用时的颜色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "checkbox图标被禁用时的颜色", "en": "Color of disabled checkbox icon"}}, "checkboxIconFontSize": {"cssKey": "checkbox-icon-font-size", "desc": "checkbox图标大小（宽高）", "override": "", "value": "20PX", "jsValue": "20PX", "staticValue": "20PX", "localeDesc": {"ch": "checkbox图标大小（宽高）", "en": "Checkbox icon size (width and height)"}}, "checkboxIconMarginRight": {"cssKey": "checkbox-icon-margin-right", "desc": "checkbox图标右侧margin", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "checkbox图标右侧margin", "en": "Right margin of checkbox icon"}}, "checkboxTextDisabledOpacity": {"cssKey": "checkbox-text-disabled-opacity", "desc": "checkbox文字禁用态透明度", "override": "", "value": "0.5", "jsValue": "0.5", "staticValue": "0.5", "localeDesc": {"ch": "checkbox文字禁用态透明度", "en": "Text transparency of disable checkbox"}}, "checkboxTextFontSize": {"cssKey": "checkbox-text-font-size", "desc": "checkbox文字字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "checkbox文字字体大小", "en": "Font size of checkbox"}}, "circleProgressDisabledColor": {"cssKey": "circle-progress-disabled-color", "desc": "环形进度条 不可用状态进度条颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "环形进度条 不可用状态进度条颜色", "en": "CircleProgress disabled track color"}}, "circleProgressFontSize": {"cssKey": "circle-progress-font-size", "desc": "环形进度条 文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "环形进度条 文字大小", "en": "CircleProgress font size"}}, "circleProgressLinearGradientEndColor": {"cssKey": "circle-progress-linear-gradient-end-color", "desc": "环形进度条 自定义进度条颜色渐变结束的颜色", "override": "", "value": "#14CAFF", "jsValue": "#14CAFF", "staticValue": "#14CAFF", "localeDesc": {"ch": "环形进度条 自定义进度条颜色渐变结束的颜色", "en": "End gradient color of CircleProgress track color"}}, "circleProgressLinearGradientStartColor": {"cssKey": "circle-progress-linear-gradient-start-color", "desc": "环形进度条 自定义进度条颜色渐变开始的颜色", "override": "", "value": "#4776E6", "jsValue": "#4776E6", "staticValue": "#4776E6", "localeDesc": {"ch": "环形进度条 自定义进度条颜色渐变开始的颜色", "en": "Start gradient color of CircleProgress track color"}}, "circleProgressLinearGradientTextColor": {"cssKey": "circle-progress-linear-gradient-text-color", "desc": "环形进度条 自定义进度条文字颜色", "override": "", "value": "#3C89EC", "jsValue": "#3C89EC", "staticValue": "#3C89EC", "localeDesc": {"ch": "环形进度条 自定义进度条文字颜色", "en": "CircleProgress font color"}}, "circleProgressMiniTrackColor": {"cssKey": "circle-progress-mini-track-color", "desc": "环形进度条 微型进度条轨道颜色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "环形进度条 微型进度条轨道颜色", "en": "Mini CircleProgress track color"}}, "circleProgressPrimaryColor": {"cssKey": "circle-progress-primary-color", "desc": "环形进度条 进度条颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "环形进度条 进度条颜色", "en": "CircleProgress progress bar color"}}, "circleProgressTrackColor": {"cssKey": "circle-progress-track-color", "desc": "环形进度条 轨道颜色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "环形进度条 轨道颜色", "en": "CircleProgress track color"}}, "collapseContentColor": {"cssKey": "collapse-content-color", "desc": "collapse content（面板）的字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "collapse content（面板）的字体颜色", "en": "Collapse content font color"}}, "collapseContentFontSize": {"cssKey": "collapse-content-font-size", "desc": "collapse content（面板）的字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "collapse content（面板）的字体大小", "en": "Collapse content font size"}}, "collapseContentLineHeight": {"cssKey": "collapse-content-line-height", "desc": "collapse content（面板）的字体行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "collapse content（面板）的字体行高", "en": "Collapse content font line height"}}, "collapseContentPadding": {"cssKey": "collapse-content-padding", "desc": "collapse content（面板）的内边距", "override": "", "value": "~`pxtorem(12)` ~`pxtorem(16)`", "jsValue": "@getRem@12 @getRem@16", "staticValue": "0.24rem 0.32rem", "localeDesc": {"ch": "collapse content（面板）的内边距", "en": "Collapse content padding"}}, "collapseDisabledHeaderColor": {"cssKey": "collapse-disabled-header-color", "desc": "collapse 禁用时header的字体颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "collapse 禁用时header的字体颜色", "en": "Header font color of disabled collapse"}}, "collapseHeaderBackground": {"cssKey": "collapse-header-background", "desc": "collapse header的背景颜色", "override": "", "value": "@container-background-color", "jsValue": "@global@containerBackgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "collapse header的背景颜色", "en": "Collapse header background color"}}, "collapseHeaderColor": {"cssKey": "collapse-header-color", "desc": "collapse header的字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "collapse header的字体颜色", "en": "Collapse header font color"}}, "collapseHeaderFontSize": {"cssKey": "collapse-header-font-size", "desc": "collapse header的字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "collapse header的字体大小", "en": "Collapse header font size"}}, "collapseHeaderHeight": {"cssKey": "collapse-header-height", "desc": "collapse header的高度", "override": "", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "collapse header的高度", "en": "Collapse header height"}}, "collapseHeaderIconColor": {"cssKey": "collapse-header-icon-color", "desc": "collapse header的图标的颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "collapse header的图标的颜色", "en": "Collapse header icon color"}}, "collapseHeaderLineHeight": {"cssKey": "collapse-header-line-height", "desc": "collapse header的字体行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "collapse header的字体行高", "en": "Collapse header font line height"}}, "collapseHeaderMarginLeft": {"cssKey": "collapse-header-margin-left", "desc": "collapse header的左侧的margin", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "collapse header的左侧的margin", "en": "Collapse header left margin"}}, "collapseHeaderPadding": {"cssKey": "collapse-header-padding", "desc": "collapse header的内边距", "override": "", "value": "~`pxtorem(16)` ~`pxtorem(16)` ~`pxtorem(16)` 0", "jsValue": "@getRem@16 @getRem@16 @getRem@16 0", "staticValue": "0.32rem 0.32rem 0.32rem 0", "localeDesc": {"ch": "collapse header的内边距", "en": "Collapse header padding"}}, "containerBackgroundColor": {"cssKey": "container-background-color", "desc": "一级容器背景色", "override": "", "value": "#FFFFFF", "jsValue": "#FFFFFF", "staticValue": "#FFFFFF", "localeDesc": {"ch": "一级容器背景色", "en": "Primary container background color"}}, "countDownColor": {"cssKey": "count-down-color", "desc": "倒计时默认字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "倒计时默认字体颜色", "en": "CountDown font color"}}, "countDownFontSize": {"cssKey": "count-down-font-size", "desc": "倒计时默认字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "倒计时默认字体大小", "en": "CountDown font size"}}, "countDownLineHeight": {"cssKey": "count-down-line-height", "desc": "倒计时默认字体行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "倒计时默认字体行高", "en": "CountDown font line height"}}, "dangerColor": {"cssKey": "danger-color", "desc": "基础危险态颜色", "override": "", "value": "#F53F3F", "jsValue": "#F53F3F", "staticValue": "#F53F3F", "localeDesc": {"ch": "基础危险态颜色", "en": "Base dangerous state color"}}, "darkActionSheetCancelBorderColor": {"cssKey": "dark-action-sheet-cancel-border-color", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkAvatarBackground": {"cssKey": "dark-avatar-background", "desc": "", "override": "", "value": "#306FFF", "jsValue": "#306FFF", "staticValue": "#306FFF"}, "darkAvatarDefaultOverlapBackground": {"cssKey": "dark-avatar-default-overlap-background", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkAvatarDescColor": {"cssKey": "dark-avatar-desc-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkAvatarGroupBorderColor": {"cssKey": "dark-avatar-group-border-color", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkAvatarNameColor": {"cssKey": "dark-avatar-name-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkAvatarTextFontColor": {"cssKey": "dark-avatar-text-font-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkBackgroundColor": {"cssKey": "dark-background-color", "desc": "", "override": "", "value": "#17171A", "jsValue": "#17171A", "staticValue": "#17171A"}, "darkBadgeBackgroundColor": {"cssKey": "dark-badge-background-color", "desc": "", "override": "", "value": "@dark-danger-color", "jsValue": "@global@darkDangerColor", "staticValue": "#F76965"}, "darkBadgeBorderColor": {"cssKey": "dark-badge-border-color", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkBadgeTextColor": {"cssKey": "dark-badge-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkButtonDefaultBackground": {"cssKey": "dark-button-default-background", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkButtonDefaultClickedBackground": {"cssKey": "dark-button-default-clicked-background", "desc": "", "override": "", "value": "@dark-primary-disabled-color", "jsValue": "@global@darkPrimaryDisabledColor", "staticValue": "#0E32A6"}, "darkButtonDefaultDisabledBackground": {"cssKey": "dark-button-default-disabled-background", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkButtonDefaultDisabledTextColor": {"cssKey": "dark-button-default-disabled-text-color", "desc": "", "override": "", "value": "@dark-primary-disabled-color", "jsValue": "@global@darkPrimaryDisabledColor", "staticValue": "#0E32A6"}, "darkButtonDefaultTextColor": {"cssKey": "dark-button-default-text-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkButtonGhostClickedBackground": {"cssKey": "dark-button-ghost-clicked-background", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkButtonGhostDisabledTextColor": {"cssKey": "dark-button-ghost-disabled-text-color", "desc": "", "override": "", "value": "@dark-primary-disabled-color", "jsValue": "@global@darkPrimaryDisabledColor", "staticValue": "#0E32A6"}, "darkButtonGhostTextColor": {"cssKey": "dark-button-ghost-text-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkButtonPrimaryBackground": {"cssKey": "dark-button-primary-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkButtonPrimaryClickedBackground": {"cssKey": "dark-button-primary-clicked-background", "desc": "", "override": "", "value": "#689FFF", "jsValue": "#689FFF", "staticValue": "#689FFF"}, "darkButtonPrimaryDisabledBackground": {"cssKey": "dark-button-primary-disabled-background", "desc": "", "override": "", "value": "@dark-primary-disabled-color", "jsValue": "@global@darkPrimaryDisabledColor", "staticValue": "#0E32A6"}, "darkButtonPrimaryDisabledTextColor": {"cssKey": "dark-button-primary-disabled-text-color", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkButtonPrimaryTextColor": {"cssKey": "dark-button-primary-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkCardBackgroundColor": {"cssKey": "dark-card-background-color", "desc": "", "override": "", "value": "hsla(0, 0%, 100%, 0.08)", "jsValue": "hsla(0, 0%, 100%, 0.08)", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkCarouselIndicatorActiveBackground": {"cssKey": "dark-carousel-indicator-active-background", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkCarouselIndicatorActiveInverseBackground": {"cssKey": "dark-carousel-indicator-active-inverse-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkCarouselIndicatorInverseBackground": {"cssKey": "dark-carousel-indicator-inverse-background", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkCarouselItemTextColor": {"cssKey": "dark-carousel-item-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkCellArrowColor": {"cssKey": "dark-cell-arrow-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkCellBackgroundColor": {"cssKey": "dark-cell-background-color", "desc": "", "override": "", "value": "@dark-container-background-color", "jsValue": "@global@darkContainerBackgroundColor", "staticValue": "#232324"}, "darkCellDescColor": {"cssKey": "dark-cell-desc-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkCellLabelColor": {"cssKey": "dark-cell-label-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkCellLabelIconColor": {"cssKey": "dark-cell-label-icon-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkCellTextColor": {"cssKey": "dark-cell-text-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkCheckboxIconCheckedColor": {"cssKey": "dark-checkbox-icon-checked-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkCheckboxIconCheckedDisabledColor": {"cssKey": "dark-checkbox-icon-checked-disabled-color", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkCheckboxIconColor": {"cssKey": "dark-checkbox-icon-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkCheckboxIconDisabledColor": {"cssKey": "dark-checkbox-icon-disabled-color", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkCircleProgressDisabledColor": {"cssKey": "dark-circle-progress-disabled-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkCircleProgressMiniTrackColor": {"cssKey": "dark-circle-progress-mini-track-color", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkCircleProgressPrimaryColor": {"cssKey": "dark-circle-progress-primary-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkCircleProgressTrackColor": {"cssKey": "dark-circle-progress-track-color", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkCollapseContentColor": {"cssKey": "dark-collapse-content-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkCollapseDisabledHeaderColor": {"cssKey": "dark-collapse-disabled-header-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkCollapseHeaderBackground": {"cssKey": "dark-collapse-header-background", "desc": "", "override": "", "value": "@dark-container-background-color", "jsValue": "@global@darkContainerBackgroundColor", "staticValue": "#232324"}, "darkCollapseHeaderColor": {"cssKey": "dark-collapse-header-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkCollapseHeaderIconColor": {"cssKey": "dark-collapse-header-icon-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkContainerBackgroundColor": {"cssKey": "dark-container-background-color", "desc": "", "override": "", "value": "#232324", "jsValue": "#232324", "staticValue": "#232324"}, "darkCountDownColor": {"cssKey": "dark-count-down-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkDangerColor": {"cssKey": "dark-danger-color", "desc": "", "override": "", "value": "#F76965", "jsValue": "#F76965", "staticValue": "#F76965"}, "darkDialogBodyAndroidColor": {"cssKey": "dark-dialog-body-android-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkDialogBodyIosColor": {"cssKey": "dark-dialog-body-ios-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkDialogButtonFooterColor": {"cssKey": "dark-dialog-button-footer-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkDialogButtonFooterPrimaryBackground": {"cssKey": "dark-dialog-button-footer-primary-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkDialogButtonFooterPrimaryColor": {"cssKey": "dark-dialog-button-footer-primary-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkDialogContentBackground": {"cssKey": "dark-dialog-content-background", "desc": "", "override": "", "value": "@dark-mask-content-background", "jsValue": "@global@darkMaskContentBackground", "staticValue": "#2A2A2B"}, "darkDialogFooterIosColor": {"cssKey": "dark-dialog-footer-ios-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkDialogHeaderAndroidColor": {"cssKey": "dark-dialog-header-android-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkDialogHeaderIosColor": {"cssKey": "dark-dialog-header-ios-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkDisabledColor": {"cssKey": "dark-disabled-color", "desc": "", "override": "", "value": "#5f5f60", "jsValue": "#5f5f60", "staticValue": "#5f5f60"}, "darkDividerContentFontColor": {"cssKey": "dark-divider-content-font-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkDividerLineColor": {"cssKey": "dark-divider-line-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkDropdownMenuColor": {"cssKey": "dark-dropdown-menu-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkDropdownMenuDisabledColor": {"cssKey": "dark-dropdown-menu-disabled-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkDropdownMenuIconColor": {"cssKey": "dark-dropdown-menu-icon-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkDropdownMenuIconSelectedColor": {"cssKey": "dark-dropdown-menu-icon-selected-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkDropdownMenuSelectedColor": {"cssKey": "dark-dropdown-menu-selected-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkDropdownMenuTipColor": {"cssKey": "dark-dropdown-menu-tip-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkDropdownMultiRowsOptionsItemBackground": {"cssKey": "dark-dropdown-multi-rows-options-item-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkDropdownMultiRowsOptionsItemColor": {"cssKey": "dark-dropdown-multi-rows-options-item-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkDropdownMultiRowsOptionsItemSelectedBackground": {"cssKey": "dark-dropdown-multi-rows-options-item-selected-background", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkDropdownMultiRowsOptionsItemSelectedColor": {"cssKey": "dark-dropdown-multi-rows-options-item-selected-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkDropdownOptionsBackgroundColor": {"cssKey": "dark-dropdown-options-background-color", "desc": "", "override": "", "value": "@dark-container-background-color", "jsValue": "@global@darkContainerBackgroundColor", "staticValue": "#232324"}, "darkDropdownOptionsItemColor": {"cssKey": "dark-dropdown-options-item-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkDropdownOptionsItemDisabledColor": {"cssKey": "dark-dropdown-options-item-disabled-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkDropdownOptionsItemSelectedColor": {"cssKey": "dark-dropdown-options-item-selected-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkEllipsisFloatEllipsisNodeBackground": {"cssKey": "dark-ellipsis-float-ellipsis-node-background", "desc": "", "override": "", "value": "linear-gradient(90deg, rgba(35, 35, 36, 0), #232324 20PX, #232324)", "jsValue": "linear-gradient(90deg, rgba(35, 35, 36, 0), #232324 20PX, #232324)", "staticValue": "linear-gradient(90deg, rgba(35, 35, 36, 0), #232324 20PX, #232324)"}, "darkFontColor": {"cssKey": "dark-font-color", "desc": "", "override": "", "value": "#f6f6f6", "jsValue": "#f6f6f6", "staticValue": "#f6f6f6"}, "darkFormItemBorderDividerColor": {"cssKey": "dark-form-item-border-divider-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkFormItemErrorMessageColor": {"cssKey": "dark-form-item-error-message-color", "desc": "", "override": "", "value": "@dark-danger-color", "jsValue": "@global@darkDangerColor", "staticValue": "#F76965"}, "darkFormItemLabelItemColor": {"cssKey": "dark-form-item-label-item-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkFormItemLabelItemRequiredAsteriskColor": {"cssKey": "dark-form-item-label-item-required-asterisk-color", "desc": "", "override": "", "value": "@dark-danger-color", "jsValue": "@global@darkDangerColor", "staticValue": "#F76965"}, "darkFormItemWarningMessageColor": {"cssKey": "dark-form-item-warning-message-color", "desc": "", "override": "", "value": "@dark-warning-color", "jsValue": "@global@darkWarningColor", "staticValue": "#FF9626"}, "darkGridBorderColor": {"cssKey": "dark-grid-border-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkImageLoadingIconColor": {"cssKey": "dark-image-loading-icon-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkImagePickerAddBackground": {"cssKey": "dark-image-picker-add-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkImagePickerAddIconColor": {"cssKey": "dark-image-picker-add-icon-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkImagePickerAddTextColor": {"cssKey": "dark-image-picker-add-text-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkImagePickerCloseColor": {"cssKey": "dark-image-picker-close-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkImagePickerErrorColor": {"cssKey": "dark-image-picker-error-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkImagePlaceholderBackground": {"cssKey": "dark-image-placeholder-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkImageRetryIconColor": {"cssKey": "dark-image-retry-icon-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkIndexBarBackground": {"cssKey": "dark-index-bar-background", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkIndexBarGroupActiveColor": {"cssKey": "dark-index-bar-group-active-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkIndexBarGroupTitleBackground": {"cssKey": "dark-index-bar-group-title-background", "desc": "", "override": "", "value": "#2e2e30", "jsValue": "#2e2e30", "staticValue": "#2e2e30"}, "darkIndexBarGroupTitleFontColor": {"cssKey": "dark-index-bar-group-title-font-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkIndexBarSidebarActiveColor": {"cssKey": "dark-index-bar-sidebar-active-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkIndexBarSidebarSweatColor": {"cssKey": "dark-index-bar-sidebar-sweat-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkIndexBarSidebarToastColor": {"cssKey": "dark-index-bar-sidebar-toast-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkInputCaretColor": {"cssKey": "dark-input-caret-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkInputClearIconColor": {"cssKey": "dark-input-clear-icon-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkInputDisabledColor": {"cssKey": "dark-input-disabled-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkInputPlaceholderColor": {"cssKey": "dark-input-placeholder-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkKeyboardBackground": {"cssKey": "dark-keyboard-background", "desc": "", "override": "", "value": "#232324", "jsValue": "#232324", "staticValue": "#232324"}, "darkKeyboardConfirmKeyBackground": {"cssKey": "dark-keyboard-confirm-key-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkKeyboardConfirmKeyColor": {"cssKey": "dark-keyboard-confirm-key-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkKeyboardKeyActiveBackground": {"cssKey": "dark-keyboard-key-active-background", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkKeyboardKeyBackground": {"cssKey": "dark-keyboard-key-background", "desc": "", "override": "", "value": "#2e2e30", "jsValue": "#2e2e30", "staticValue": "#2e2e30"}, "darkKeyboardKeyColor": {"cssKey": "dark-keyboard-key-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkLighterLineColor": {"cssKey": "dark-lighter-line-color", "desc": "", "override": "", "value": "#2e2e30", "jsValue": "#2e2e30", "staticValue": "#2e2e30"}, "darkLighterPrimaryColor": {"cssKey": "dark-lighter-primary-color", "desc": "", "override": "", "value": "#000D4D", "jsValue": "#000D4D", "staticValue": "#000D4D"}, "darkLineColor": {"cssKey": "dark-line-color", "desc": "", "override": "", "value": "#484849", "jsValue": "#484849", "staticValue": "#484849"}, "darkLoadMoreTextColor": {"cssKey": "dark-load-more-text-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkLoadingArcBackgroundColor": {"cssKey": "dark-loading-arc-background-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkLoadingColor": {"cssKey": "dark-loading-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkMaskContentBackground": {"cssKey": "dark-mask-content-background", "desc": "", "override": "", "value": "#2A2A2B", "jsValue": "#2A2A2B", "staticValue": "#2A2A2B"}, "darkMaskContentColor": {"cssKey": "dark-mask-content-color", "desc": "", "override": "", "value": "rgba(255, 255, 255, 0.9)", "jsValue": "rgba(255, 255, 255, 0.9)", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkNavBarBackground": {"cssKey": "dark-nav-bar-background", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkNavBarBottomBorderColor": {"cssKey": "dark-nav-bar-bottom-border-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkNavBarFontColor": {"cssKey": "dark-nav-bar-font-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkNoticeBarBackground": {"cssKey": "dark-notice-bar-background", "desc": "", "override": "", "value": "#4D1B00", "jsValue": "#4D1B00", "staticValue": "#4D1B00"}, "darkNoticeBarColor": {"cssKey": "dark-notice-bar-color", "desc": "", "override": "", "value": "@dark-warning-color", "jsValue": "@global@darkWarningColor", "staticValue": "#FF9626"}, "darkNoticeBarGradientBackground": {"cssKey": "dark-notice-bar-gradient-background", "desc": "", "override": "", "value": "linear-gradient(to right, #4D1B00, rgba(77, 27, 0, 0))", "jsValue": "linear-gradient(to right, #4D1B00, rgba(77, 27, 0, 0))", "staticValue": "linear-gradient(to right, #4D1B00, rgba(77, 27, 0, 0))"}, "darkNotifyErrorBackground": {"cssKey": "dark-notify-error-background", "desc": "", "override": "", "value": "@dark-danger-color", "jsValue": "@global@darkDangerColor", "staticValue": "#F76965"}, "darkNotifyFontColor": {"cssKey": "dark-notify-font-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkNotifyInfoFontColor": {"cssKey": "dark-notify-info-font-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkNotifySuccessBackground": {"cssKey": "dark-notify-success-background", "desc": "", "override": "", "value": "@dark-success-color", "jsValue": "@global@darkSuccessColor", "staticValue": "#27C346"}, "darkNotifyWarnBackground": {"cssKey": "dark-notify-warn-background", "desc": "", "override": "", "value": "@dark-warning-color", "jsValue": "@global@darkWarningColor", "staticValue": "#FF9626"}, "darkPaginationFieldDefaultBackground": {"cssKey": "dark-pagination-field-default-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkPaginationFieldDefaultTextColor": {"cssKey": "dark-pagination-field-default-text-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkPaginationFieldDisabledBackground": {"cssKey": "dark-pagination-field-disabled-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkPaginationFieldDisabledTextColor": {"cssKey": "dark-pagination-field-disabled-text-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkPaginationFieldPrimaryBackground": {"cssKey": "dark-pagination-field-primary-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkPaginationFieldPrimaryTextColor": {"cssKey": "dark-pagination-field-primary-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkPaginationFieldTextColor": {"cssKey": "dark-pagination-field-text-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkPaginationFieldTextPrimaryTextColor": {"cssKey": "dark-pagination-field-text-primary-text-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkPaginationItemDefaultTextColor": {"cssKey": "dark-pagination-item-default-text-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkPaginationItemPrimaryTextColor": {"cssKey": "dark-pagination-item-primary-text-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkPickerHeaderBackground": {"cssKey": "dark-picker-header-background", "desc": "", "override": "", "value": "@dark-mask-content-background", "jsValue": "@global@darkMaskContentBackground", "staticValue": "#2A2A2B"}, "darkPickerLeftBtnColor": {"cssKey": "dark-picker-left-btn-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkPickerRightBtnColor": {"cssKey": "dark-picker-right-btn-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkPickerViewMaskBottomBackground": {"cssKey": "dark-picker-view-mask-bottom-background", "desc": "", "override": "", "value": "linear-gradient(to top, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)", "jsValue": "linear-gradient(to top, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)", "staticValue": "linear-gradient(to top, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)"}, "darkPickerViewMaskTopBackground": {"cssKey": "dark-picker-view-mask-top-background", "desc": "", "override": "", "value": "linear-gradient(to bottom, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)", "jsValue": "linear-gradient(to bottom, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)", "staticValue": "linear-gradient(to bottom, rgba(42, 42, 43, 1) 0%, rgba(42, 42, 43, 0.7) 65%)"}, "darkPickerViewSelectionBorderColor": {"cssKey": "dark-picker-view-selection-border-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkPopoverContentColor": {"cssKey": "dark-popover-content-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkPopoverContentWhiteThemeBorderColor": {"cssKey": "dark-popover-content-white-theme-border-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkPopoverContentWhiteThemeColor": {"cssKey": "dark-popover-content-white-theme-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkPopoverContentWhiteThemeDisabledColor": {"cssKey": "dark-popover-content-white-theme-disabled-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkPopoverMenuActiveWhiteThemeBackground": {"cssKey": "dark-popover-menu-active-white-theme-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkPopoverMenuIconWhiteThemeColor": {"cssKey": "dark-popover-menu-icon-white-theme-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkPopoverWhiteThemeBackgroundColor": {"cssKey": "dark-popover-white-theme-background-color", "desc": "", "override": "", "value": "@dark-mask-content-background", "jsValue": "@global@darkMaskContentBackground", "staticValue": "#2A2A2B"}, "darkPopupContentBackground": {"cssKey": "dark-popup-content-background", "desc": "", "override": "", "value": "@dark-mask-content-background", "jsValue": "@global@darkMaskContentBackground", "staticValue": "#2A2A2B"}, "darkPrimaryColor": {"cssKey": "dark-primary-color", "desc": "", "override": "", "value": "#3C7EFF", "jsValue": "#3C7EFF", "staticValue": "#3C7EFF"}, "darkPrimaryDisabledColor": {"cssKey": "dark-primary-disabled-color", "desc": "", "override": "", "value": "#0E32A6", "jsValue": "#0E32A6", "staticValue": "#0E32A6"}, "darkProgressDisabledColor": {"cssKey": "dark-progress-disabled-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkProgressDisabledTextColor": {"cssKey": "dark-progress-disabled-text-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkProgressPrimaryColor": {"cssKey": "dark-progress-primary-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkProgressTextInnerColor": {"cssKey": "dark-progress-text-inner-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkProgressTrackColor": {"cssKey": "dark-progress-track-color", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkPullRefreshContentBackgroundColor": {"cssKey": "dark-pull-refresh-content-background-color", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkPullRefreshLabelBackgroundColor": {"cssKey": "dark-pull-refresh-label-background-color", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkPullRefreshLabelLoadingColor": {"cssKey": "dark-pull-refresh-label-loading-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkPullRefreshLabelTextColor": {"cssKey": "dark-pull-refresh-label-text-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkRateIconDisabledActiveColor": {"cssKey": "dark-rate-icon-disabled-active-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkRateIconNormalColor": {"cssKey": "dark-rate-icon-normal-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkSearchBarAssociationBackgroundColor": {"cssKey": "dark-search-bar-association-background-color", "desc": "", "override": "", "value": "@dark-container-background-color", "jsValue": "@global@darkContainerBackgroundColor", "staticValue": "#232324"}, "darkSearchBarAssociationItemColor": {"cssKey": "dark-search-bar-association-item-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkSearchBarAssociationItemHighlightColor": {"cssKey": "dark-search-bar-association-item-highlight-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkSearchBarBackgroundColor": {"cssKey": "dark-search-bar-background-color", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkSearchBarCancelBtnColor": {"cssKey": "dark-search-bar-cancel-btn-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkSearchBarClearIconColor": {"cssKey": "dark-search-bar-clear-icon-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkSearchBarInputCaretColor": {"cssKey": "dark-search-bar-input-caret-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkSearchBarInputPlaceholderColor": {"cssKey": "dark-search-bar-input-placeholder-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkSearchBarInputWrapperBackgroundColor": {"cssKey": "dark-search-bar-input-wrapper-background-color", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkSearchBarSearchIconColor": {"cssKey": "dark-search-bar-search-icon-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkSkeletonBackgroundColor": {"cssKey": "dark-skeleton-background-color", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkSkeletonGradientAnimationColor": {"cssKey": "dark-skeleton-gradient-animation-color", "desc": "", "override": "", "value": "hsla(0, 0%, 100%, 0.08)", "jsValue": "hsla(0, 0%, 100%, 0.08)", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkSliderLineActivatedColor": {"cssKey": "dark-slider-line-activated-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkSliderLineColor": {"cssKey": "dark-slider-line-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkSliderLineDisabledColor": {"cssKey": "dark-slider-line-disabled-color", "desc": "", "override": "", "value": "@dark-primary-disabled-color", "jsValue": "@global@darkPrimaryDisabledColor", "staticValue": "#0E32A6"}, "darkSliderTextColor": {"cssKey": "dark-slider-text-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkSliderThumbBackground": {"cssKey": "dark-slider-thumb-background", "desc": "", "override": "", "value": "@dark-mask-content-background", "jsValue": "@global@darkMaskContentBackground", "staticValue": "#2A2A2B"}, "darkStepperContentColor": {"cssKey": "dark-stepper-content-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkStepperDefaultBackgroundColor": {"cssKey": "dark-stepper-default-background-color", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkStepperDisableColor": {"cssKey": "dark-stepper-disable-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkStepperSquareBorderColor": {"cssKey": "dark-stepper-square-border-color", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkStepsDescriptionColor": {"cssKey": "dark-steps-description-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkStepsErrorIconNumBackground": {"cssKey": "dark-steps-error-icon-num-background", "desc": "", "override": "", "value": "@dark-danger-color", "jsValue": "@global@darkDangerColor", "staticValue": "#F76965"}, "darkStepsErrorIconSvgColor": {"cssKey": "dark-steps-error-icon-svg-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkStepsErrorTitleColor": {"cssKey": "dark-steps-error-title-color", "desc": "", "override": "", "value": "@dark-danger-color", "jsValue": "@global@darkDangerColor", "staticValue": "#F76965"}, "darkStepsFinishDotBorderColor": {"cssKey": "dark-steps-finish-dot-border-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkStepsFinishIconNumBackground": {"cssKey": "dark-steps-finish-icon-num-background", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkStepsFinishIconSvgColor": {"cssKey": "dark-steps-finish-icon-svg-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkStepsFinishTitleColor": {"cssKey": "dark-steps-finish-title-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkStepsIconNumColor": {"cssKey": "dark-steps-icon-num-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkStepsProcessDotBackground": {"cssKey": "dark-steps-process-dot-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkStepsProcessIconNumBackground": {"cssKey": "dark-steps-process-icon-num-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkStepsProcessIconNumColor": {"cssKey": "dark-steps-process-icon-num-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkStepsProcessTitleColor": {"cssKey": "dark-steps-process-title-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkStepsProcessWithConfigItemIconColor": {"cssKey": "dark-steps-process-with-config-item-icon-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkStepsTailFinishBackground": {"cssKey": "dark-steps-tail-finish-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkStepsTailStandardBackground": {"cssKey": "dark-steps-tail-standard-background", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkStepsWaitDescriptionColor": {"cssKey": "dark-steps-wait-description-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkStepsWaitDotBorderColor": {"cssKey": "dark-steps-wait-dot-border-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkStepsWaitIconNumBackground": {"cssKey": "dark-steps-wait-icon-num-background", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkStepsWaitTitleColor": {"cssKey": "dark-steps-wait-title-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkSubFontColor": {"cssKey": "dark-sub-font-color", "desc": "", "override": "", "value": "#c5c5c5", "jsValue": "#c5c5c5", "staticValue": "#c5c5c5"}, "darkSubInfoFontColor": {"cssKey": "dark-sub-info-font-color", "desc": "", "override": "", "value": "#929293", "jsValue": "#929293", "staticValue": "#929293"}, "darkSuccessColor": {"cssKey": "dark-success-color", "desc": "", "override": "", "value": "#27C346", "jsValue": "#27C346", "staticValue": "#27C346"}, "darkSwipeActionTextColor": {"cssKey": "dark-swipe-action-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkSwipeLoadLabelBackground": {"cssKey": "dark-swipe-load-label-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkSwipeLoadLabelTextColor": {"cssKey": "dark-swipe-load-label-text-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkSwitchAndroidBackground": {"cssKey": "dark-switch-android-background", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkSwitchAndroidCheckedBackground": {"cssKey": "dark-switch-android-checked-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkSwitchAndroidDisabledBackground": {"cssKey": "dark-switch-android-disabled-background", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkSwitchAndroidDisabledCheckedBackground": {"cssKey": "dark-switch-android-disabled-checked-background", "desc": "", "override": "", "value": "@dark-primary-disabled-color", "jsValue": "@global@darkPrimaryDisabledColor", "staticValue": "#0E32A6"}, "darkSwitchTextCheckedColor": {"cssKey": "dark-switch-text-checked-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkSwitchTextColor": {"cssKey": "dark-switch-text-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkTabBarActiveColor": {"cssKey": "dark-tab-bar-active-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTabBarColor": {"cssKey": "dark-tab-bar-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkTabsTabBarBackground": {"cssKey": "dark-tabs-tab-bar-background", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkTabsTabBarCardColor": {"cssKey": "dark-tabs-tab-bar-card-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTabsTabBarCardTextColor": {"cssKey": "dark-tabs-tab-bar-card-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkTabsTabBarLineActiveColor": {"cssKey": "dark-tabs-tab-bar-line-active-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTabsTabBarTagActiveBackground": {"cssKey": "dark-tabs-tab-bar-tag-active-background", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTabsTabBarTagActiveTextColor": {"cssKey": "dark-tabs-tab-bar-tag-active-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkTabsTabBarTagBackground": {"cssKey": "dark-tabs-tab-bar-tag-background", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkTabsTabBarTagTextColor": {"cssKey": "dark-tabs-tab-bar-tag-text-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkTabsUnderlineColor": {"cssKey": "dark-tabs-underline-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTagHollowBorderColor": {"cssKey": "dark-tag-hollow-border-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTagHollowColor": {"cssKey": "dark-tag-hollow-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTagListAddBackground": {"cssKey": "dark-tag-list-add-background", "desc": "", "override": "", "value": "@dark-lighter-line-color", "jsValue": "@global@darkLighterLineColor", "staticValue": "#2e2e30"}, "darkTagListAddBorderColor": {"cssKey": "dark-tag-list-add-border-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkTagListAddColor": {"cssKey": "dark-tag-list-add-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkTagPrimaryBackgroundColor": {"cssKey": "dark-tag-primary-background-color", "desc": "", "override": "", "value": "@dark-lighter-primary-color", "jsValue": "@global@darkLighterPrimaryColor", "staticValue": "#000D4D"}, "darkTagPrimaryBorderColor": {"cssKey": "dark-tag-primary-border-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTagPrimaryColor": {"cssKey": "dark-tag-primary-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTagSolidBackgroundColor": {"cssKey": "dark-tag-solid-background-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTagSolidBorderColor": {"cssKey": "dark-tag-solid-border-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTagSolidColor": {"cssKey": "dark-tag-solid-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkTextareaStatisticColor": {"cssKey": "dark-textarea-statistic-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkTimeLineAxisColor": {"cssKey": "dark-time-line-axis-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkTimeLineContentBackgroundColor": {"cssKey": "dark-time-line-content-background-color", "desc": "", "override": "", "value": "@dark-line-color", "jsValue": "@global@darkLineColor", "staticValue": "#484849"}, "darkTimeLineContentColor": {"cssKey": "dark-time-line-content-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkTimeLineDotBackgroundColor": {"cssKey": "dark-time-line-dot-background-color", "desc": "", "override": "", "value": "@dark-background-color", "jsValue": "@global@darkBackgroundColor", "staticValue": "#17171A"}, "darkTimeLineDotBorderColor": {"cssKey": "dark-time-line-dot-border-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkTimeLineLabelColor": {"cssKey": "dark-time-line-label-color", "desc": "", "override": "", "value": "@dark-sub-info-font-color", "jsValue": "@global@darkSubInfoFontColor", "staticValue": "#929293"}, "darkToastTextColor": {"cssKey": "dark-toast-text-color", "desc": "", "override": "", "value": "@dark-mask-content-color", "jsValue": "@global@darkMaskContentColor", "staticValue": "rgba(255, 255, 255, 0.9)"}, "darkUploaderDeleteIconColor": {"cssKey": "dark-uploader-delete-icon-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkUploaderDisabledDeleteIconColor": {"cssKey": "dark-uploader-disabled-delete-icon-color", "desc": "", "override": "", "value": "@dark-disabled-color", "jsValue": "@global@darkDisabledColor", "staticValue": "#5f5f60"}, "darkUploaderErrorTextColor": {"cssKey": "dark-uploader-error-text-color", "desc": "", "override": "", "value": "@dark-primary-color", "jsValue": "@global@darkPrimaryColor", "staticValue": "#3C7EFF"}, "darkUploaderFileIconColor": {"cssKey": "dark-uploader-file-icon-color", "desc": "", "override": "", "value": "@dark-sub-font-color", "jsValue": "@global@darkSubFontColor", "staticValue": "#c5c5c5"}, "darkUploaderItemBackgroundColor": {"cssKey": "dark-uploader-item-background-color", "desc": "", "override": "", "value": "@dark-card-background-color", "jsValue": "@global@darkCardBackgroundColor", "staticValue": "hsla(0, 0%, 100%, 0.08)"}, "darkUploaderItemTextColor": {"cssKey": "dark-uploader-item-text-color", "desc": "", "override": "", "value": "@dark-font-color", "jsValue": "@global@darkFontColor", "staticValue": "#f6f6f6"}, "darkUploaderItemTextErrorColor": {"cssKey": "dark-uploader-item-text-error-color", "desc": "", "override": "", "value": "@dark-danger-color", "jsValue": "@global@darkDangerColor", "staticValue": "#F76965"}, "darkUploaderLoadedIconColor": {"cssKey": "dark-uploader-loaded-icon-color", "desc": "", "override": "", "value": "@dark-success-color", "jsValue": "@global@darkSuccessColor", "staticValue": "#27C346"}, "darkWarningColor": {"cssKey": "dark-warning-color", "desc": "", "override": "", "value": "#FF9626", "jsValue": "#FF9626", "staticValue": "#FF9626"}, "dialogAndroidBodyFooterGutter": {"cssKey": "dialog-android-body-footer-gutter", "desc": "对话框 body 和 footer 的间距 (android)", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "对话框 body 和 footer 的间距 (android)", "en": "Dialog body and footer spacing (android)"}}, "dialogAndroidHeaderBodyGutter": {"cssKey": "dialog-android-header-body-gutter", "desc": "对话框 header 和 body 的间距 (android)", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "对话框 header 和 body 的间距 (android)", "en": "Dialog header and body gutter (android)"}}, "dialogAndroidHorizontalPadding": {"cssKey": "dialog-android-horizontal-padding", "desc": "对话框横向内边距 (android)", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "对话框横向内边距 (android)", "en": "Dialog horizontal padding (android)"}}, "dialogAndroidVerticalPadding": {"cssKey": "dialog-android-vertical-padding", "desc": "对话框纵向内边距 (android)", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "对话框纵向内边距 (android)", "en": "Dialog vertical padding (android)"}}, "dialogBodyAndroidColor": {"cssKey": "dialog-body-android-color", "desc": "对话框正文字体颜色 (android)", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "对话框正文字体颜色 (android)", "en": "Dialog body font color (android)"}}, "dialogBodyAndroidFontSize": {"cssKey": "dialog-body-android-font-size", "desc": "对话框正文字体大小 (android)", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "对话框正文字体大小 (android)", "en": "Dialog body font size (android)"}}, "dialogBodyAndroidLineHeight": {"cssKey": "dialog-body-android-line-height", "desc": "对话框正文字体行高 (android)", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "对话框正文字体行高 (android)", "en": "Dialog body font line height (android)"}}, "dialogBodyIosColor": {"cssKey": "dialog-body-ios-color", "desc": "对话框正文字体颜色 (ios)", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "对话框正文字体颜色 (ios)", "en": "Dialog body font color (ios)"}}, "dialogBodyIosFontSize": {"cssKey": "dialog-body-ios-font-size", "desc": "对话框正文字体大小 (ios)", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "对话框正文字体大小 (ios)", "en": "Dialog body font size (ios)"}}, "dialogBodyIosLineHeight": {"cssKey": "dialog-body-ios-line-height", "desc": "对话框正文字体行高 (ios)", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "对话框正文字体行高 (ios)", "en": "Dialog body font line height (ios)"}}, "dialogButtonFooterBorderRadius": {"cssKey": "dialog-button-footer-border-radius", "desc": "对话框（独立按钮样式）按钮圆角值", "override": "", "value": "~`pxtorem(30)`", "jsValue": "@getRem@30", "staticValue": "0.6rem", "localeDesc": {"ch": "对话框（独立按钮样式）按钮圆角值", "en": "Dialog (independent button style) button border radius"}}, "dialogButtonFooterColor": {"cssKey": "dialog-button-footer-color", "desc": "对话框（独立按钮样式）普通按钮字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "对话框（独立按钮样式）普通按钮字体颜色", "en": "Dialog (independent button style) normal button font color"}}, "dialogButtonFooterGutter": {"cssKey": "dialog-button-footer-gutter", "desc": "对话框（独立按钮样式）按钮间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "对话框（独立按钮样式）按钮间距", "en": "Dialog (independent button style) button gutter"}}, "dialogButtonFooterHeight": {"cssKey": "dialog-button-footer-height", "desc": "对话框（独立按钮样式）按钮高度", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "对话框（独立按钮样式）按钮高度", "en": "Dialog (independent button style) button height"}}, "dialogButtonFooterPrimaryBackground": {"cssKey": "dialog-button-footer-primary-background", "desc": "对话框（独立按钮样式）主按钮背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "对话框（独立按钮样式）主按钮背景色", "en": "Dialog (independent button style) primary button background color"}}, "dialogButtonFooterPrimaryColor": {"cssKey": "dialog-button-footer-primary-color", "desc": "对话框（独立按钮样式）主按钮字体颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "对话框（独立按钮样式）主按钮字体颜色", "en": "Dialog (independent button style) primary button font color"}}, "dialogContentAndroidBorderRadius": {"cssKey": "dialog-content-android-border-radius", "desc": "对话框内容面板圆角值 (android)", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "对话框内容面板圆角值 (android)", "en": "Dialog content panel border radius value (android)"}}, "dialogContentAndroidWidth": {"cssKey": "dialog-content-android-width", "desc": "对话框面板宽度 (android)", "override": "", "value": "~`px<PERSON><PERSON>(280)`", "jsValue": "@getRem@280", "staticValue": "5.6rem", "localeDesc": {"ch": "对话框面板宽度 (android)", "en": "Dialog content panel width (android)"}}, "dialogContentBackground": {"cssKey": "dialog-content-background", "desc": "对话框内容面板背景色", "override": "", "value": "@mask-content-background", "jsValue": "@global@maskContentBackground", "staticValue": "#FFFFFF", "localeDesc": {"ch": "对话框内容面板背景色", "en": "Dialog content panel background color"}}, "dialogContentBorderRadius": {"cssKey": "dialog-content-border-radius", "desc": "对话框内容面板圆角值 (ios)", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "对话框内容面板圆角值 (ios)", "en": "Dialog content panel border radius value (ios)"}}, "dialogContentWidth": {"cssKey": "dialog-content-width", "desc": "对话框面板宽度 (ios)", "override": "", "value": "~`pxtorem(270)`", "jsValue": "@getRem@270", "staticValue": "5.4rem", "localeDesc": {"ch": "对话框面板宽度 (ios)", "en": "Dialog content panel width (ios)"}}, "dialogFooterAndroidButtonGutter": {"cssKey": "dialog-footer-android-button-gutter", "desc": "对话框按钮区按钮间距 (android)", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "对话框按钮区按钮间距 (android)", "en": "Dialog footer button gutter (android)"}}, "dialogFooterAndroidColor": {"cssKey": "dialog-footer-android-color", "desc": "对话框按钮区字体颜色 (android)", "override": "", "value": "#1a74ff", "jsValue": "#1a74ff", "staticValue": "#1a74ff", "localeDesc": {"ch": "对话框按钮区字体颜色 (android)", "en": "Dialog footer font color (android)"}}, "dialogFooterAndroidFontSize": {"cssKey": "dialog-footer-android-font-size", "desc": "对话框按钮区字体大小 (android)", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "对话框按钮区字体大小 (android)", "en": "Dialog footer font size (android)"}}, "dialogFooterAndroidLineHeight": {"cssKey": "dialog-footer-android-line-height", "desc": "对话框按钮区字体行高 (android)", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "对话框按钮区字体行高 (android)", "en": "Dialog footer line height (android)"}}, "dialogFooterIosColor": {"cssKey": "dialog-footer-ios-color", "desc": "对话框按钮区字体颜色 (ios)", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "对话框按钮区字体颜色 (ios)", "en": "Dialog footer font color (ios)"}}, "dialogFooterIosFontSize": {"cssKey": "dialog-footer-ios-font-size", "desc": "对话框按钮区字体大小 (ios)", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "对话框按钮区字体大小 (ios)", "en": "Dialog footer font size (ios)"}}, "dialogFooterIosHeight": {"cssKey": "dialog-footer-ios-height", "desc": "对话框按钮区高度 (ios)", "override": "", "value": "~`px<PERSON><PERSON>(44)`", "jsValue": "@getRem@44", "staticValue": "0.88rem", "localeDesc": {"ch": "对话框按钮区高度 (ios)", "en": "Dialog footer height (ios)"}}, "dialogHeaderAndroidColor": {"cssKey": "dialog-header-android-color", "desc": "对话框标题字体颜色 (android)", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "对话框标题字体颜色 (android)", "en": "Dialog header font color (android)"}}, "dialogHeaderAndroidFontSize": {"cssKey": "dialog-header-android-font-size", "desc": "对话框标题字体大小 (android)", "override": "", "value": "~`px<PERSON><PERSON>(17)`", "jsValue": "@getRem@17", "staticValue": "0.34rem", "localeDesc": {"ch": "对话框标题字体大小 (android)", "en": "Dialog header font size (android)"}}, "dialogHeaderAndroidLineHeight": {"cssKey": "dialog-header-android-line-height", "desc": "对话框标题字体行高 (android)", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "对话框标题字体行高 (android)", "en": "Dialog title font line height (android)"}}, "dialogHeaderIosColor": {"cssKey": "dialog-header-ios-color", "desc": "对话框标题字体颜色 (ios)", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "对话框标题字体颜色 (ios)", "en": "Dialog header font color (ios)"}}, "dialogHeaderIosFontSize": {"cssKey": "dialog-header-ios-font-size", "desc": "对话框标题字体大小 (ios)", "override": "", "value": "~`px<PERSON><PERSON>(17)`", "jsValue": "@getRem@17", "staticValue": "0.34rem", "localeDesc": {"ch": "对话框标题字体大小 (ios)", "en": "Dialog header font size (ios)"}}, "dialogHeaderIosLineHeight": {"cssKey": "dialog-header-ios-line-height", "desc": "对话框标题字体行高 (ios)", "override": "", "value": "~`px<PERSON>m(26)`", "jsValue": "@getRem@26", "staticValue": "0.52rem", "localeDesc": {"ch": "对话框标题字体行高 (ios)", "en": "Dialog header font line height (ios)"}}, "dialogIosHeaderBodyGutter": {"cssKey": "dialog-ios-header-body-gutter", "desc": "对话框 header 和 body 的间距 (ios)", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "对话框 header 和 body 的间距 (ios)", "en": "Dialog header and body gutter (ios)"}}, "dialogIosHorizontalPadding": {"cssKey": "dialog-ios-horizontal-padding", "desc": "对话框横向内边距 (ios)", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "对话框横向内边距 (ios)", "en": "Dialog horizontal padding (ios)"}}, "dialogIosVerticalPadding": {"cssKey": "dialog-ios-vertical-padding", "desc": "对话框纵向内边距 (ios)", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "对话框纵向内边距 (ios)", "en": "Dialog vertical padding (ios)"}}, "dialogMaskBackground": {"cssKey": "dialog-mask-background", "desc": "对话框蒙层背景色", "override": "", "value": "@mask-background", "jsValue": "@global@maskBackground", "staticValue": "rgba(0, 0, 0, 0.6)", "localeDesc": {"ch": "对话框蒙层背景色", "en": "Dialog mask background color"}}, "disabledColor": {"cssKey": "disabled-color", "desc": "基础禁用态字体颜色", "override": "", "value": "#c9cdd4", "jsValue": "#c9cdd4", "staticValue": "#c9cdd4", "localeDesc": {"ch": "基础禁用态字体颜色", "en": "Base disabled font color"}}, "dividerContentFontColor": {"cssKey": "divider-content-font-color", "desc": "分割线文本字体颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "分割线文本字体颜色", "en": "Font color of divider content"}}, "dividerContentFontSize": {"cssKey": "divider-content-font-size", "desc": "分割线文本字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "分割线文本字体大小", "en": "Font size of divider content"}}, "dividerContentPadding": {"cssKey": "divider-content-padding", "desc": "分割线文本左右padding", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "分割线文本左右padding", "en": "Padding of content"}}, "dividerLeftWidth": {"cssKey": "divider-left-width", "desc": "分割线左对齐时左边长度", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "分割线左对齐时左边长度"}}, "dividerLineColor": {"cssKey": "divider-line-color", "desc": "分割线线条颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "分割线线条颜色", "en": "Color of divider line"}}, "dividerLineThickness": {"cssKey": "divider-line-thickness", "desc": "分割线线条粗细", "override": "", "value": "1PX", "jsValue": "1PX", "staticValue": "1PX", "localeDesc": {"ch": "分割线线条粗细", "en": "Thickness of divider line"}}, "dividerPadding": {"cssKey": "divider-padding", "desc": "分割线上下padding", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "分割线上下padding", "en": "Top and Bottom padding of divider"}}, "dividerRightWidth": {"cssKey": "divider-right-width", "desc": "分割线右对齐时右边长度", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "分割线右对齐时右边长度"}}, "dropdownMaskBackgroundColor": {"cssKey": "dropdown-mask-background-color", "desc": "dropdown的弹出框中mask的背景颜色", "override": "", "value": "@mask-background", "jsValue": "@global@maskBackground", "staticValue": "rgba(0, 0, 0, 0.6)", "localeDesc": {"ch": "dropdown的弹出框中mask的背景颜色", "en": "Dropdown mask background color"}}, "dropdownMenuColor": {"cssKey": "dropdown-menu-color", "desc": "dropdownMenu的选择框每一项的字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "dropdownMenu的选择框每一项的字体颜色", "en": "Menu item font color of DropdownMenu"}}, "dropdownMenuDisabledColor": {"cssKey": "dropdown-menu-disabled-color", "desc": "dropdownMenu的选择框被禁用项的字体颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "dropdownMenu的选择框被禁用项的字体颜色", "en": "Font color of the disabled menu item in the DropdownMenu"}}, "dropdownMenuFontSize": {"cssKey": "dropdown-menu-font-size", "desc": "dropdownMenu的选择框每一项的字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "dropdownMenu的选择框每一项的字体大小", "en": "Menu item font size of DropdownMenu"}}, "dropdownMenuIconColor": {"cssKey": "dropdown-menu-icon-color", "desc": "dropdownMenu的选择框中图标颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "dropdownMenu的选择框中图标颜色", "en": "DropdownMenu icon color"}}, "dropdownMenuIconMarginLeft": {"cssKey": "dropdown-menu-icon-margin-left", "desc": "dropdownMenu的选择框中图标的左侧margin", "override": "", "value": "4PX", "jsValue": "4PX", "staticValue": "4PX", "localeDesc": {"ch": "dropdownMenu的选择框中图标的左侧margin", "en": "Left margin of the icon in the DropdownMenu"}}, "dropdownMenuIconSelectedColor": {"cssKey": "dropdown-menu-icon-selected-color", "desc": "dropdownMenu的选择框中图标被选中时的颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "dropdownMenu的选择框中图标被选中时的颜色", "en": "DropdownMenu selected icon color"}}, "dropdownMenuIconSize": {"cssKey": "dropdown-menu-icon-size", "desc": "dropdownMenu的选择框中图标的大小（宽高）", "override": "", "value": "12PX", "jsValue": "12PX", "staticValue": "12PX", "localeDesc": {"ch": "dropdownMenu的选择框中图标的大小（宽高）", "en": "DropdownMenu icon size"}}, "dropdownMenuLabelMaxWidth": {"cssKey": "dropdown-menu-label-max-width", "desc": "dropdownMenu的选择框中选择项的最大宽度", "override": "", "value": "~`px<PERSON><PERSON>(96)`", "jsValue": "@getRem@96", "staticValue": "1.92rem", "localeDesc": {"ch": "dropdownMenu的选择框中选择项的最大宽度", "en": "Maximum width of the menu item in the DropdownMenu"}}, "dropdownMenuLineHeight": {"cssKey": "dropdown-menu-line-height", "desc": "dropdownMenu的选择框每一项的字体行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "dropdownMenu的选择框每一项的字体行高", "en": "Menu item font line height of DropdownMenu"}}, "dropdownMenuPadding": {"cssKey": "dropdown-menu-padding", "desc": "dropdownMenu的选择框每一项的padding", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "dropdownMenu的选择框每一项的padding", "en": "Menu item padding of DropdownMenu"}}, "dropdownMenuSelectedColor": {"cssKey": "dropdown-menu-selected-color", "desc": "dropdownMenu的选择框被选中项的字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "dropdownMenu的选择框被选中项的字体颜色", "en": "Font color of the selected menu item in the DropdownMenu"}}, "dropdownMenuTipColor": {"cssKey": "dropdown-menu-tip-color", "desc": "dropdownMenu的选择框中选择指引（选择项名称）的字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "dropdownMenu的选择框中选择指引（选择项名称）的字体颜色", "en": "Selection tip font color of the menu item in the DropdownMenu"}}, "dropdownMenuTipMinWidth": {"cssKey": "dropdown-menu-tip-min-width", "desc": "dropdownMenu的选择框中选择指引（选择项名称）的最小宽度", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "dropdownMenu的选择框中选择指引（选择项名称）的最小宽度", "en": "Selection tip minimum width of the menu item in the DropdownMenu"}}, "dropdownMenuTipPaddingRight": {"cssKey": "dropdown-menu-tip-padding-right", "desc": "dropdownMenu的选择框中选择指引（选择项名称）的右侧padding", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "dropdownMenu的选择框中选择指引（选择项名称）的右侧padding", "en": "Selection tip right padding of the menu item in the DropdownMenu"}}, "dropdownMultiRowsOptionsContainerMargin": {"cssKey": "dropdown-multi-rows-options-container-margin", "desc": "dropdown的弹出框中多列样式下容器的外边距", "override": "", "value": "0 ~`pxtorem(-12)` ~`pxtorem(-12)` 0", "jsValue": "0 @getRem@-12 @getRem@-12 0", "staticValue": "0 -0.24rem -0.24rem 0", "localeDesc": {"ch": "dropdown的弹出框中多列样式下容器的外边距", "en": "Options container margin in multi-column Dropdown"}}, "dropdownMultiRowsOptionsContainerPadding": {"cssKey": "dropdown-multi-rows-options-container-padding", "desc": "dropdown的弹出框中多列样式下容器的内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "dropdown的弹出框中多列样式下容器的内边距", "en": "Options container padding in multi-column Dropdown"}}, "dropdownMultiRowsOptionsGutter": {"cssKey": "dropdown-multi-rows-options-gutter", "desc": "dropdown的弹出框中多列样式下选项之间的间距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项之间的间距", "en": "Gutter between options in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemBackground": {"cssKey": "dropdown-multi-rows-options-item-background", "desc": "dropdown的弹出框中多列样式下选项的背景色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项的背景色", "en": "Options item background color in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemBorderRadius": {"cssKey": "dropdown-multi-rows-options-item-border-radius", "desc": "dropdown的弹出框中多列样式下选项的圆角值", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项的圆角值", "en": "Options item border radius in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemColor": {"cssKey": "dropdown-multi-rows-options-item-color", "desc": "dropdown的弹出框中多列样式下选项的字体颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项的字体颜色", "en": "Options item font color in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemFontSize": {"cssKey": "dropdown-multi-rows-options-item-font-size", "desc": "dropdown的弹出框中多列样式下选项的字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项的字体大小", "en": "Options font size in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemLineHeight": {"cssKey": "dropdown-multi-rows-options-item-line-height", "desc": "dropdown的弹出框中多列样式下选项的字体行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项的字体行高", "en": "Options item font line height in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemPadding": {"cssKey": "dropdown-multi-rows-options-item-padding", "desc": "dropdown的弹出框中多列样式下选项的内边距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项的内边距", "en": "Options item padding in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemSelectedBackground": {"cssKey": "dropdown-multi-rows-options-item-selected-background", "desc": "dropdown的弹出框中多列样式下选项被选中时的背景色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项被选中时的背景色", "en": "Selected options item background color in multi-column Dropdown"}}, "dropdownMultiRowsOptionsItemSelectedColor": {"cssKey": "dropdown-multi-rows-options-item-selected-color", "desc": "dropdown的弹出框中多列样式下选项被选中时的字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "dropdown的弹出框中多列样式下选项被选中时的字体颜色", "en": "Selected options item font color in multi-column Dropdown"}}, "dropdownOptionsBackgroundColor": {"cssKey": "dropdown-options-background-color", "desc": "dropdown的弹出框的背景颜色", "override": "", "value": "@container-background-color", "jsValue": "@global@containerBackgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "dropdown的弹出框的背景颜色", "en": "Dropdown options background color"}}, "dropdownOptionsItemColor": {"cssKey": "dropdown-options-item-color", "desc": "dropdown的弹出框中选项的字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "dropdown的弹出框中选项的字体颜色", "en": "Dropdown options item font color"}}, "dropdownOptionsItemDisabledColor": {"cssKey": "dropdown-options-item-disabled-color", "desc": "dropdown的弹出框中选项被禁选时的字体颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "dropdown的弹出框中选项被禁选时的字体颜色", "en": "Dropdown disabled options item font color"}}, "dropdownOptionsItemFontSize": {"cssKey": "dropdown-options-item-font-size", "desc": "dropdown的弹出框中选项的字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "dropdown的弹出框中选项的字体大小", "en": "Dropdown option itme font size"}}, "dropdownOptionsItemIconRight": {"cssKey": "dropdown-options-item-icon-right", "desc": "dropdown的弹出框中选项图标绝对定位时，距离右侧的距离", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "dropdown的弹出框中选项图标绝对定位时，距离右侧的距离", "en": "The right distance of Dropdown options item whose icon is absolutely positioned"}}, "dropdownOptionsItemLineHeight": {"cssKey": "dropdown-options-item-line-height", "desc": "dropdown的弹出框中选项的字体行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "dropdown的弹出框中选项的字体行高", "en": "Dropdown option itme font line height"}}, "dropdownOptionsItemPadding": {"cssKey": "dropdown-options-item-padding", "desc": "dropdown的弹出框中选项的padding", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "dropdown的弹出框中选项的padding", "en": "Dropdown options padding"}}, "dropdownOptionsItemSelectedColor": {"cssKey": "dropdown-options-item-selected-color", "desc": "dropdown的弹出框中选项被选中时的字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "dropdown的弹出框中选项被选中时的字体颜色", "en": "Dropdown selected options item font color"}}, "ellipsisDefaultTextSize": {"cssKey": "ellipsis-default-text-size", "desc": "文字缩略组件默认字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "文字缩略组件默认字体大小", "en": "Ellipsis default font size"}}, "ellipsisFloatEllipsisNodeBackground": {"cssKey": "ellipsis-float-ellipsis-node-background", "desc": "文字缩略组件浮动模式下的缩略符背景色", "override": "", "value": "linear-gradient(90deg, rgba(255, 255, 255, 0), #ffffff 20PX, #ffffff)", "jsValue": "linear-gradient(90deg, rgba(255, 255, 255, 0), #ffffff 20PX, #ffffff)", "staticValue": "linear-gradient(90deg, rgba(255, 255, 255, 0), #ffffff 20PX, #ffffff)", "localeDesc": {"ch": "文字缩略组件浮动模式下的缩略符背景色", "en": "Background of floating ellipsis node"}}, "ellipsisFloatEllipsisNodePaddingLeft": {"cssKey": "ellipsis-float-ellipsis-node-padding-left", "desc": "文字缩略组件浮动模式下的缩略符左边距", "override": "", "value": "20PX", "jsValue": "20PX", "staticValue": "20PX", "localeDesc": {"ch": "文字缩略组件浮动模式下的缩略符左边距", "en": "Padding left of floating ellipsis node"}}, "fixedZIndex": {"cssKey": "fixed-z-index", "desc": "固定组件zIndex基础值", "override": "", "value": "100", "jsValue": "100", "staticValue": "100", "localeDesc": {"ch": "固定组件zIndex基础值", "en": "The zIndex base value of the fixed component"}}, "fontColor": {"cssKey": "font-color", "desc": "基础字体颜色", "override": "", "value": "#1d2129", "jsValue": "#1d2129", "staticValue": "#1d2129", "localeDesc": {"ch": "基础字体颜色", "en": "Base font color"}}, "formItemBorderDividerColor": {"cssKey": "form-item-border-divider-color", "desc": "表单项分割线颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "表单项分割线颜色", "en": "Form item divider color"}}, "formItemErrorMessageColor": {"cssKey": "form-item-error-message-color", "desc": "表单项错误提示颜色", "override": "", "value": "@danger-color", "jsValue": "@global@dangerColor", "staticValue": "#F53F3F", "localeDesc": {"ch": "表单项错误提示颜色", "en": "Form item error message color"}}, "formItemLabelItemColor": {"cssKey": "form-item-label-item-color", "desc": "表单项标签颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "表单项标签颜色", "en": "Color of Form label"}}, "formItemLabelItemFontSize": {"cssKey": "form-item-label-item-font-size", "desc": "", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem"}, "formItemLabelItemGutter": {"cssKey": "form-item-label-item-gutter", "desc": "表单项标签右边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "表单项标签右边距", "en": "Right padding of Form label"}}, "formItemLabelItemLineHeight": {"cssKey": "form-item-label-item-line-height", "desc": "表单项标签行高", "override": "", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "表单项标签行高", "en": "Lineheight of Form label"}}, "formItemLabelItemRequiredAsteriskColor": {"cssKey": "form-item-label-item-required-asterisk-color", "desc": "表单项必选项星号颜色", "override": "", "value": "@danger-color", "jsValue": "@global@dangerColor", "staticValue": "#F53F3F", "localeDesc": {"ch": "表单项必选项星号颜色", "en": "Form item asterisk color"}}, "formItemLabelItemWidth": {"cssKey": "form-item-label-item-width", "desc": "表单项标签宽度", "override": "", "value": "~`px<PERSON><PERSON>(96)`", "jsValue": "@getRem@96", "staticValue": "1.92rem", "localeDesc": {"ch": "表单项标签宽度", "en": "Right padding of Form label"}}, "formItemWarningMessageColor": {"cssKey": "form-item-warning-message-color", "desc": "表单项警告提示颜色", "override": "", "value": "@warning-color", "jsValue": "@global@warningColor", "staticValue": "#FF7D00", "localeDesc": {"ch": "表单项警告提示颜色", "en": "Form item warning message color"}}, "fullScreenZIndex": {"cssKey": "full-screen-z-index", "desc": "全屏组件zIndex基础值", "override": "", "value": "1000", "jsValue": "1000", "staticValue": "1000", "localeDesc": {"ch": "全屏组件zIndex基础值", "en": "The zIndex base value of the full screen component"}}, "gridBorderColor": {"cssKey": "grid-border-color", "desc": "宫格组件分割线颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "宫格组件分割线颜色", "en": "Grid border color"}}, "gridBorderSize": {"cssKey": "grid-border-size", "desc": "宫格组件分割线长度", "override": "", "value": "66.66%", "jsValue": "66.66%", "staticValue": "66.66%", "localeDesc": {"ch": "宫格组件分割线长度", "en": "Grid border length"}}, "gridHorizontalContentMarginTop": {"cssKey": "grid-horizontal-content-margin-top", "desc": "宫格组件水平布局中描述内容顶部间距", "override": "", "value": "~`pxtorem(0)`", "jsValue": "@getRem@0", "staticValue": "0", "localeDesc": {"ch": "宫格组件水平布局中描述内容顶部间距", "en": "Top margin of content of horizontal Grid"}}, "gridHorizontalTextMarginLeft": {"cssKey": "grid-horizontal-text-margin-left", "desc": "宫格组件水平布局中文字区域左侧间距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "宫格组件水平布局中文字区域左侧间距", "en": "Left margin of Text of horizontal Grid"}}, "gridIconHeight": {"cssKey": "grid-icon-height", "desc": "宫格组件每个单元格图标的高度", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "宫格组件每个单元格图标的高度", "en": "Grid icon height"}}, "gridIconWidth": {"cssKey": "grid-icon-width", "desc": "宫格组件每个单元格图标的宽度", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "宫格组件每个单元格图标的宽度", "en": "Grid icon width"}}, "gridVerticalContentFontSize": {"cssKey": "grid-vertical-content-font-size", "desc": "宫格组件垂直布局中描述内容字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "宫格组件垂直布局中描述内容字体大小", "en": "Content font size of vertical Grid"}}, "gridVerticalContentLineHeight": {"cssKey": "grid-vertical-content-line-height", "desc": "宫格组件垂直布局中描述内容字体行高", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "宫格组件垂直布局中描述内容字体行高", "en": "Content font line height of vertical Grid"}}, "gridVerticalContentMarginTop": {"cssKey": "grid-vertical-content-margin-top", "desc": "宫格组件垂直布局中描述内容顶部间距", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "宫格组件垂直布局中描述内容顶部间距", "en": "Top margin of content of vertical Grid"}}, "gridVerticalTextMarginTop": {"cssKey": "grid-vertical-text-margin-top", "desc": "宫格组件垂直布局中文字区域顶部间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "宫格组件垂直布局中文字区域顶部间距", "en": "Top margin of Text of vertical Grid"}}, "gridVerticalTitleFontSize": {"cssKey": "grid-vertical-title-font-size", "desc": "宫格组件垂直布局中标题字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "宫格组件垂直布局中标题字体大小", "en": "Title font size of vertical Grid"}}, "gridVerticalTitleLineHeight": {"cssKey": "grid-vertical-title-line-height", "desc": "宫格组件垂直布局中标题字体行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "宫格组件垂直布局中标题字体行高", "en": "Title font line height of vertical Grid"}}, "imageInnerFontSize": {"cssKey": "image-inner-font-size", "desc": "图片加载或重试时默认字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "图片加载或重试时默认字体大小", "en": "Default font size when image loads or retries"}}, "imageLoadingIconColor": {"cssKey": "image-loading-icon-color", "desc": "图片 loading 态图标及文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "图片 loading 态图标及文字颜色", "en": "Image loading status icon color  and text color"}}, "imageMaskBackground": {"cssKey": "image-mask-background", "desc": "图片加载或重试时默认蒙层背景色", "override": "", "value": "@mask-background", "jsValue": "@global@maskBackground", "staticValue": "rgba(0, 0, 0, 0.6)", "localeDesc": {"ch": "图片加载或重试时默认蒙层背景色", "en": "Default mask background color when image loads or retries"}}, "imagePickerAddBackground": {"cssKey": "image-picker-add-background", "desc": "图片选择器添加图片按钮的背景色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "图片选择器添加图片按钮的背景色", "en": "Background of the add button of ImagePicker"}}, "imagePickerAddIconColor": {"cssKey": "image-picker-add-icon-color", "desc": "图片选择器添加图片按钮的图标颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "图片选择器添加图片按钮的图标颜色", "en": "Icon color of the add button of ImagePicker"}}, "imagePickerAddIconFontSize": {"cssKey": "image-picker-add-icon-font-size", "desc": "图片选择器添加图片按钮的图标大小", "override": "", "value": "~`pxtorem(30)`", "jsValue": "@getRem@30", "staticValue": "0.6rem", "localeDesc": {"ch": "图片选择器添加图片按钮的图标大小", "en": "Icon size of the add button of ImagePicker"}}, "imagePickerAddTextColor": {"cssKey": "image-picker-add-text-color", "desc": "图片选择器添加图片按钮的字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "图片选择器添加图片按钮的字体颜色", "en": "Font color of the add button of ImagePicker"}}, "imagePickerAddTextFontSize": {"cssKey": "image-picker-add-text-font-size", "desc": "图片选择器添加图片按钮的字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "图片选择器添加图片按钮的字体大小", "en": "Font size of the add button of ImagePicker"}}, "imagePickerBorderRadius": {"cssKey": "image-picker-border-radius", "desc": "图片选择器中图片的圆角值", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "图片选择器中图片的圆角值", "en": "Border radius of the image in ImagePicker"}}, "imagePickerCloseBackground": {"cssKey": "image-picker-close-background", "desc": "图片选择器关闭按钮背景色", "override": "", "value": "rgba(0, 0, 0, 0.3)", "jsValue": "rgba(0, 0, 0, 0.3)", "staticValue": "rgba(0, 0, 0, 0.3)", "localeDesc": {"ch": "图片选择器关闭按钮背景色", "en": "Background of the close button of ImagePicker"}}, "imagePickerCloseBorderRadius": {"cssKey": "image-picker-close-border-radius", "desc": "图片选择器关闭按钮圆角值", "override": "", "value": "0 ~`pxtorem(2)`", "jsValue": "0 @getRem@2", "staticValue": "0 0.04rem", "localeDesc": {"ch": "图片选择器关闭按钮圆角值", "en": "Border radius of the close button of ImagePicker"}}, "imagePickerCloseColor": {"cssKey": "image-picker-close-color", "desc": "图片选择器关闭按钮字体颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "图片选择器关闭按钮字体颜色", "en": "Font color of the close button of ImagePicker"}}, "imagePickerCloseFontSize": {"cssKey": "image-picker-close-font-size", "desc": "图片选择器关闭按钮字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "图片选择器关闭按钮字体大小", "en": "Font size of the close button of ImagePicker"}}, "imagePickerCloseHeight": {"cssKey": "image-picker-close-height", "desc": "图片选择器关闭按钮高度", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "图片选择器关闭按钮高度", "en": "Height of the close button of ImagePicker"}}, "imagePickerCloseWidth": {"cssKey": "image-picker-close-width", "desc": "图片选择器关闭按钮宽度", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "图片选择器关闭按钮宽度", "en": "Width of the close button of ImagePicker"}}, "imagePickerDisabledOpacity": {"cssKey": "image-picker-disabled-opacity", "desc": "图片选择器禁用状态下的透明度", "override": "", "value": "0.7", "jsValue": "0.7", "staticValue": "0.7", "localeDesc": {"ch": "图片选择器禁用状态下的透明度", "en": "Opacity of disabled ImagePicker"}}, "imagePickerErrorBackground": {"cssKey": "image-picker-error-background", "desc": "图片选择器中图片错误状态下的背景色", "override": "", "value": "rgba(0, 0, 0, 0.5)", "jsValue": "rgba(0, 0, 0, 0.5)", "staticValue": "rgba(0, 0, 0, 0.5)", "localeDesc": {"ch": "图片选择器中图片错误状态下的背景色", "en": "Background for image error state in the image picker"}}, "imagePickerErrorColor": {"cssKey": "image-picker-error-color", "desc": "图片选择器中图片错误状态下的文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "图片选择器中图片错误状态下的文字颜色", "en": "Text color for image error state in the image picker"}}, "imagePickerFontSize": {"cssKey": "image-picker-font-size", "desc": "图片选择器内部字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "图片选择器内部字体大小", "en": "Font size of ImagePicker"}}, "imagePlaceholderBackground": {"cssKey": "image-placeholder-background", "desc": "图片 placeholder 背景色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "图片 placeholder 背景色", "en": "Image placeholder background color"}}, "imagePreviewIndicatorBackground": {"cssKey": "image-preview-indicator-background", "desc": "图片预览指示器背景色", "override": "", "value": "linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3))", "jsValue": "linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3))", "staticValue": "linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3))", "localeDesc": {"ch": "图片预览指示器背景色", "en": "ImagePreview indicator background color"}}, "imagePreviewIndicatorFontSize": {"cssKey": "image-preview-indicator-font-size", "desc": "图片预览指示器文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "图片预览指示器文字大小", "en": "ImagePreview indicator font size"}}, "imagePreviewIndicatorPadding": {"cssKey": "image-preview-indicator-padding", "desc": "图片预览指示器内边距", "override": "", "value": "~`pxtorem(12)` ~`pxtorem(20)`", "jsValue": "@getRem@12 @getRem@20", "staticValue": "0.24rem 0.4rem", "localeDesc": {"ch": "图片预览指示器内边距", "en": "ImagePreview indicator padding"}}, "imagePreviewMaskBackground": {"cssKey": "image-preview-mask-background", "desc": "图片预览蒙层背景色", "override": "", "value": "rgba(0, 0, 0, 0.9)", "jsValue": "rgba(0, 0, 0, 0.9)", "staticValue": "rgba(0, 0, 0, 0.9)", "localeDesc": {"ch": "图片预览蒙层背景色", "en": "ImagePreview mask background color"}}, "imagePreviewThumbTransition": {"cssKey": "image-preview-thumb-transition", "desc": "图片预览小图放大时的动画曲线", "override": "", "value": "all cubic-bezier(0.34, 0.69, 0.1, 1)", "jsValue": "all cubic-bezier(0.34, 0.69, 0.1, 1)", "staticValue": "all cubic-bezier(0.34, 0.69, 0.1, 1)", "localeDesc": {"ch": "图片预览小图放大时的动画曲线", "en": "Animation curve when ImagePreview thumbnail is enlarged"}}, "imageRetryFontSize": {"cssKey": "image-retry-font-size", "desc": "图片重试时字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "图片重试时字体大小", "en": "Font size when image retries"}}, "imageRetryIconColor": {"cssKey": "image-retry-icon-color", "desc": "图片 retry 态图标及文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "图片 retry 态图标及文字颜色", "en": "Image retry status icon and text color"}}, "imageTransitionFunction": {"cssKey": "image-transition-function", "desc": "图片加载完成展现时的动画曲线", "override": "", "value": "cubic-bezier(0.39, 0.575, 0.565, 1)", "jsValue": "cubic-bezier(0.39, 0.575, 0.565, 1)", "staticValue": "cubic-bezier(0.39, 0.575, 0.565, 1)", "localeDesc": {"ch": "图片加载完成展现时的动画曲线", "en": "The animation curve when the image is loaded and displayed"}}, "indexBarBackground": {"cssKey": "index-bar-background", "desc": "索引栏背景颜色", "override": "", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "索引栏背景颜色", "en": "IndexBar background color"}}, "indexBarGroupActiveColor": {"cssKey": "index-bar-group-active-color", "desc": "索引栏，激活状态下的，索引文字颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "索引栏，激活状态下的，索引文字颜色", "en": "IndexBar, In active state, index text color"}}, "indexBarGroupItemFontSize": {"cssKey": "index-bar-group-item-font-size", "desc": "索引栏内容子项字号大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "索引栏内容子项字号大小", "en": "IndexBar content sub-item font size"}}, "indexBarGroupItemHeight": {"cssKey": "index-bar-group-item-height", "desc": "索引栏内容子项高度", "override": "", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "索引栏内容子项高度", "en": "IndexBar content subitem height"}}, "indexBarGroupLeftSpacing": {"cssKey": "index-bar-group-left-spacing", "desc": "索引栏内容左填充宽度", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "索引栏内容左填充宽度", "en": "IndexBar content left padding width"}}, "indexBarGroupTitleBackground": {"cssKey": "index-bar-group-title-background", "desc": "索引栏标题背景颜色", "override": "", "value": "#f7f8fa", "jsValue": "#f7f8fa", "staticValue": "#f7f8fa", "localeDesc": {"ch": "索引栏标题背景颜色", "en": "IndexBar title background color"}}, "indexBarGroupTitleFontColor": {"cssKey": "index-bar-group-title-font-color", "desc": "索引栏标题字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "索引栏标题字体颜色", "en": "IndexBar title text color"}}, "indexBarGroupTitleFontSize": {"cssKey": "index-bar-group-title-font-size", "desc": "索引栏索引标题字号", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "索引栏索引标题字号", "en": "IndexBar index title font size"}}, "indexBarGroupTitleHeight": {"cssKey": "index-bar-group-title-height", "desc": "索引栏索引标题高度", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "索引栏索引标题高度", "en": "IndexBar index header height"}}, "indexBarSidebarActiveColor": {"cssKey": "index-bar-sidebar-active-color", "desc": "索引栏侧边栏激活索引颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "索引栏侧边栏激活索引颜色", "en": "IndexBar sidebar active index color"}}, "indexBarSidebarItemFontSize": {"cssKey": "index-bar-sidebar-item-font-size", "desc": "索引栏侧边栏子项字号大小", "override": "", "value": "~`pxtorem(10)`", "jsValue": "@getRem@10", "staticValue": "0.2rem", "localeDesc": {"ch": "索引栏侧边栏子项字号大小", "en": "The font size of the subitems in the sidebar of the IndexBar"}}, "indexBarSidebarItemLineHeight": {"cssKey": "index-bar-sidebar-item-line-height", "desc": "索引栏侧边栏子项字号行高", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "索引栏侧边栏子项字号行高", "en": "The line height of the subitems in the sidebar of the IndexBar"}}, "indexBarSidebarItemPadding": {"cssKey": "index-bar-sidebar-item-padding", "desc": "索引栏侧边栏子项高度", "override": "", "value": "~`pxtorem(2)` ~`pxtorem(8)`", "jsValue": "@getRem@2 @getRem@8", "staticValue": "0.04rem 0.16rem", "localeDesc": {"ch": "索引栏侧边栏子项高度", "en": "IndexBar sidebar child item height"}}, "indexBarSidebarItemWidth": {"cssKey": "index-bar-sidebar-item-width", "desc": "索引栏侧边栏子项宽度", "override": "", "value": "~`pxtorem(10)`", "jsValue": "@getRem@10", "staticValue": "0.2rem", "localeDesc": {"ch": "索引栏侧边栏子项宽度", "en": "IndexBar sidebar child item width"}}, "indexBarSidebarSweatBackground": {"cssKey": "index-bar-sidebar-sweat-background", "desc": "索引栏侧边栏水滴提示气泡背景颜色", "override": "", "value": "#333333", "jsValue": "#333333", "staticValue": "#333333", "localeDesc": {"ch": "索引栏侧边栏水滴提示气泡背景颜色", "en": "IndexBar sidebar water drop prompt bubble background color"}}, "indexBarSidebarSweatColor": {"cssKey": "index-bar-sidebar-sweat-color", "desc": "索引栏侧边栏水滴提示文案颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "索引栏侧边栏水滴提示文案颜色", "en": "The color of the water drop prompt text in the sidebar of the IndexBar"}}, "indexBarSidebarSweatFontSize": {"cssKey": "index-bar-sidebar-sweat-font-size", "desc": "索引栏侧边栏水滴字号大小", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "索引栏侧边栏水滴字号大小", "en": "IndexBar sidebar water drop font size"}}, "indexBarSidebarSweatPadding": {"cssKey": "index-bar-sidebar-sweat-padding", "desc": "索引栏侧边栏水滴提示气泡的内边距", "override": "", "value": "0 ~`pxtorem(8)`", "jsValue": "0 @getRem@8", "staticValue": "0 0.16rem", "localeDesc": {"ch": "索引栏侧边栏水滴提示气泡的内边距", "en": "The inner margin of the water drop prompt bubble in the sidebar of the IndexBar"}}, "indexBarSidebarSweatRadius": {"cssKey": "index-bar-sidebar-sweat-radius", "desc": "索引栏侧边栏水滴直径", "override": "", "value": "~`pxtorem(50)`", "jsValue": "@getRem@50", "staticValue": "1rem", "localeDesc": {"ch": "索引栏侧边栏水滴直径", "en": "IndexBar sidebar droplet diameter"}}, "indexBarSidebarSweatRight": {"cssKey": "index-bar-sidebar-sweat-right", "desc": "索引栏侧边栏水滴离侧边栏的距离", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "索引栏侧边栏水滴离侧边栏的距离", "en": "The distance between the water droplets in the sidebar of the IndexBar and the sidebar"}}, "indexBarSidebarSweatTriangleBorder": {"cssKey": "index-bar-sidebar-sweat-triangle-border", "desc": "索引栏侧边栏三角气泡的border", "override": "", "value": "~`pxtorem(18)` solid transparent", "jsValue": "@getRem@18 solid transparent", "staticValue": "0.36rem solid transparent", "localeDesc": {"ch": "索引栏侧边栏三角气泡的border", "en": "The border of the triangle bubble in the sidebar of the IndexBar"}}, "indexBarSidebarSweatTrianglePosition": {"cssKey": "index-bar-sidebar-sweat-triangle-position", "desc": "索引栏侧边栏三角气泡位置", "override": "", "value": "~`pxtorem(-28)`", "jsValue": "@getRem@-28", "staticValue": "-0.56rem", "localeDesc": {"ch": "索引栏侧边栏三角气泡位置", "en": "The position of the triangle bubble in the sidebar of the IndexBar"}}, "indexBarSidebarToastBackground": {"cssKey": "index-bar-sidebar-toast-background", "desc": "索引栏侧边栏轻提示背景颜色", "override": "", "value": "rgba(0, 0, 0, 0.8)", "jsValue": "rgba(0, 0, 0, 0.8)", "staticValue": "rgba(0, 0, 0, 0.8)", "localeDesc": {"ch": "索引栏侧边栏轻提示背景颜色", "en": "IndexBar sidebar light prompt background color"}}, "indexBarSidebarToastColor": {"cssKey": "index-bar-sidebar-toast-color", "desc": "索引栏侧边栏轻提示文案颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "索引栏侧边栏轻提示文案颜色", "en": "The color of the light prompt copy in the sidebar of the IndexBar"}}, "indexBarSidebarToastFontSize": {"cssKey": "index-bar-sidebar-toast-font-size", "desc": "索引栏侧边栏轻提示字号大小", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "索引栏侧边栏轻提示字号大小", "en": "IndexBar sidebar light prompt font size"}}, "indexBarSidebarToastHeight": {"cssKey": "index-bar-sidebar-toast-height", "desc": "索引栏侧边栏轻提示方框高度", "override": "", "value": "~`pxtorem(48)`", "jsValue": "@getRem@48", "staticValue": "0.96rem", "localeDesc": {"ch": "索引栏侧边栏轻提示方框高度", "en": "The height of the light prompt box in the sidebar of the IndexBar"}}, "indexBarSidebarToastPadding": {"cssKey": "index-bar-sidebar-toast-padding", "desc": "索引栏侧边栏轻提示内边距", "override": "", "value": "0 ~`pxtorem(8)`", "jsValue": "0 @getRem@8", "staticValue": "0 0.16rem", "localeDesc": {"ch": "索引栏侧边栏轻提示内边距", "en": "IndexBar sidebar light prompt padding"}}, "indexBarSidebarToastRadius": {"cssKey": "index-bar-sidebar-toast-radius", "desc": "索引栏侧边栏轻提示圆角大小", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "索引栏侧边栏轻提示圆角大小", "en": "The sidebar of the IndexBar lightly prompts the size of the rounded corners"}}, "inputCaretColor": {"cssKey": "input-caret-color", "desc": "输入框 光标颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "输入框 光标颜色", "en": "Input caret color"}}, "inputClearIconColor": {"cssKey": "input-clear-icon-color", "desc": "输入框 清除图标颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "输入框 清除图标颜色", "en": "Input clear icon color"}}, "inputClearIconFontSize": {"cssKey": "input-clear-icon-font-size", "desc": "输入框 清除图标大小", "override": "", "value": "16PX", "jsValue": "16PX", "staticValue": "16PX", "localeDesc": {"ch": "输入框 清除图标大小", "en": "Input clear icon font size"}}, "inputDisabledColor": {"cssKey": "input-disabled-color", "desc": "输入框 禁用状态文字色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "输入框 禁用状态文字色", "en": "Input font color in the disable state"}}, "inputHeight": {"cssKey": "input-height", "desc": "输入框 高度", "override": "", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "输入框 高度", "en": "Input height"}}, "inputHorizontalPadding": {"cssKey": "input-horizontal-padding", "desc": "输入框 外包层水平内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "输入框 外包层水平内边距", "en": "Input horizontal padding of wrapper layer"}}, "inputLabelGutter": {"cssKey": "input-label-gutter", "desc": "输入框 前置内容右内边距", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "输入框 前置内容右内边距", "en": "Input right padding of label"}}, "inputLabelMinWidth": {"cssKey": "input-label-min-width", "desc": "输入框 标签最小高度", "override": "", "value": "~`px<PERSON>m(64)`", "jsValue": "@getRem@64", "staticValue": "1.28rem", "localeDesc": {"ch": "输入框 标签最小高度", "en": "Input label minimum height"}}, "inputPlaceholderColor": {"cssKey": "input-placeholder-color", "desc": "输入框 占位文本颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "输入框 占位文本颜色", "en": "Input font color of placeholder"}}, "inputTextFontSize": {"cssKey": "input-text-font-size", "desc": "输入框 文字字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "输入框 文字字体大小", "en": "Input text font size"}}, "inputTextLineHeight": {"cssKey": "input-text-line-height", "desc": "输入框 文字行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "输入框 文字行高", "en": "Input text line height"}}, "inputVerticalPadding": {"cssKey": "input-vertical-padding", "desc": "输入框 外包层垂直内边距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "输入框 外包层垂直内边距", "en": "Input vertical padding of wrapper layer"}}, "keyboardBackground": {"cssKey": "keyboard-background", "desc": "键盘背景颜色", "override": "", "value": "#f2f3f5", "jsValue": "#f2f3f5", "staticValue": "#f2f3f5", "localeDesc": {"ch": "键盘背景颜色", "en": "Keyboard background color"}}, "keyboardConfirmKeyBackground": {"cssKey": "keyboard-confirm-key-background", "desc": "键盘右边一列确认键背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "键盘右边一列确认键背景色", "en": "Keyboard right column confirm background"}}, "keyboardConfirmKeyColor": {"cssKey": "keyboard-confirm-key-color", "desc": "键盘右边一列确认键字体颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "键盘右边一列确认键字体颜色", "en": "Keyboard right column confirm color"}}, "keyboardConfirmKeyFontSize": {"cssKey": "keyboard-confirm-key-font-size", "desc": "键盘右边一列确认键字体大小", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "键盘右边一列确认键字体大小", "en": "Keyboard right column confirm font size"}}, "keyboardContentPadding": {"cssKey": "keyboard-content-padding", "desc": "键盘内边距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "键盘内边距", "en": "Keyboard padding"}}, "keyboardKeyActiveBackground": {"cssKey": "keyboard-key-active-background", "desc": "键盘按钮背景色激活状态下", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "键盘按钮背景色激活状态下", "en": "Keyboard key button background in active"}}, "keyboardKeyBackground": {"cssKey": "keyboard-key-background", "desc": "键盘按钮背景色", "override": "", "value": "#ffffff", "jsValue": "#ffffff", "staticValue": "#ffffff", "localeDesc": {"ch": "键盘按钮背景色", "en": "Keyboard key button background"}}, "keyboardKeyBorderRadius": {"cssKey": "keyboard-key-border-radius", "desc": "键盘按钮圆角", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "键盘按钮圆角", "en": "Keyboard key button rounded"}}, "keyboardKeyColor": {"cssKey": "keyboard-key-color", "desc": "键盘按钮字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "键盘按钮字体颜色", "en": "Keyboard key button font color"}}, "keyboardKeyFontSize": {"cssKey": "keyboard-key-font-size", "desc": "键盘按钮字体大小", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "键盘按钮字体大小", "en": "Keyboard key button font size"}}, "keyboardKeyFontWeight": {"cssKey": "keyboard-key-font-weight", "desc": "键盘按钮字重", "override": "", "value": "500", "jsValue": "500", "staticValue": "500", "localeDesc": {"ch": "键盘按钮字重", "en": "Keyboard key button font weight"}}, "keyboardKeyHeight": {"cssKey": "keyboard-key-height", "desc": "键盘按钮高度", "override": "", "value": "~`pxtorem(48)`", "jsValue": "@getRem@48", "staticValue": "0.96rem", "localeDesc": {"ch": "键盘按钮高度", "en": "Keyboard key button height"}}, "keyboardKeyIconSize": {"cssKey": "keyboard-key-icon-size", "desc": "键盘按钮中的图标大小", "override": "", "value": "~`px<PERSON>m(26)`", "jsValue": "@getRem@26", "staticValue": "0.52rem", "localeDesc": {"ch": "键盘按钮中的图标大小", "en": "Keyboard key button icon size"}}, "keyboardKeyLineHeight": {"cssKey": "keyboard-key-line-height", "desc": "键盘按钮字体行高", "override": "", "value": "~`pxtorem(30)`", "jsValue": "@getRem@30", "staticValue": "0.6rem", "localeDesc": {"ch": "键盘按钮字体行高", "en": "Keyboard key button font line height"}}, "keyboardUnifiedMargin": {"cssKey": "keyboard-unified-margin", "desc": "键盘统一边距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "键盘统一边距", "en": "Keyboard unified margin"}}, "lighterLineColor": {"cssKey": "lighter-line-color", "desc": "更浅的线条色", "override": "", "value": "#f2f3f5", "jsValue": "#f2f3f5", "staticValue": "#f2f3f5", "localeDesc": {"ch": "更浅的线条色", "en": "Lighter line color"}}, "lighterPrimaryColor": {"cssKey": "lighter-primary-color", "desc": "更浅的主题色", "override": "", "value": "#E8F3FF", "jsValue": "#E8F3FF", "staticValue": "#E8F3FF", "localeDesc": {"ch": "更浅的主题色", "en": "Lighter theme color"}}, "lineColor": {"cssKey": "line-color", "desc": "基础线条颜色", "override": "", "value": "#e5e6eb", "jsValue": "#e5e6eb", "staticValue": "#e5e6eb", "localeDesc": {"ch": "基础线条颜色", "en": "Base line color"}}, "loadMoreFontSize": {"cssKey": "load-more-font-size", "desc": "加载更多文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "加载更多文字大小", "en": "Load more font size"}}, "loadMoreTextColor": {"cssKey": "load-more-text-color", "desc": "加载更多文字颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "加载更多文字颜色", "en": "Load more font color"}}, "loadingArcBackgroundColor": {"cssKey": "loading-arc-background-color", "desc": "loading type=arc 时底圈的背景色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "loading type=arc 时底圈的背景色", "en": "Circle background color when loading type=arc"}}, "loadingColor": {"cssKey": "loading-color", "desc": "loading 主颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "loading 主颜色", "en": "Loading primary color"}}, "loadingDotGutter": {"cssKey": "loading-dot-gutter", "desc": "loading type=dot 时圆点间距", "override": "", "value": "~`pxtorem(6)`", "jsValue": "@getRem@6", "staticValue": "0.12rem", "localeDesc": {"ch": "loading type=dot 时圆点间距", "en": "Dot gutter when loading type=dot"}}, "loadingDotSize": {"cssKey": "loading-dot-size", "desc": "loading type=dot 时圆点大小", "override": "", "value": "6PX", "jsValue": "6PX", "staticValue": "6PX", "localeDesc": {"ch": "loading type=dot 时圆点大小", "en": "Dot size when loading type=dot"}}, "maskBackground": {"cssKey": "mask-background", "desc": "基础蒙层背景色", "override": "", "value": "rgba(0, 0, 0, 0.6)", "jsValue": "rgba(0, 0, 0, 0.6)", "staticValue": "rgba(0, 0, 0, 0.6)", "localeDesc": {"ch": "基础蒙层背景色", "en": "Base mask background color"}}, "maskContentBackground": {"cssKey": "mask-content-background", "desc": "蒙层内容面板背景色", "override": "", "value": "#FFFFFF", "jsValue": "#FFFFFF", "staticValue": "#FFFFFF", "localeDesc": {"ch": "蒙层内容面板背景色", "en": "Content panel background color of Mask"}}, "maskContentColor": {"cssKey": "mask-content-color", "desc": "蒙层内容字体颜色", "override": "", "value": "#FFFFFF", "jsValue": "#FFFFFF", "staticValue": "#FFFFFF", "localeDesc": {"ch": "蒙层内容字体颜色", "en": "Mask content font color"}}, "navBarBackIconHeight": {"cssKey": "nav-bar-back-icon-height", "desc": "navBar 返回图标高度", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "navBar 返回图标高度", "en": "Back icon height of navBar"}}, "navBarBackground": {"cssKey": "nav-bar-background", "desc": "navBar 背景色", "override": "", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "navBar 背景色", "en": "NavBar background color"}}, "navBarBottomBorderColor": {"cssKey": "nav-bar-bottom-border-color", "desc": "navBar 下边框颜色", "override": "nav-bar-line-color", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "navBar 下边框颜色", "en": "NavBar bottom border color"}}, "navBarFontColor": {"cssKey": "nav-bar-font-color", "desc": "navBar 字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "navBar 字体颜色", "en": "NavBar font color"}}, "navBarHeight": {"cssKey": "nav-bar-height", "desc": "navBar 高度", "override": "", "value": "~`px<PERSON><PERSON>(44)`", "jsValue": "@getRem@44", "staticValue": "0.88rem", "localeDesc": {"ch": "navBar 高度", "en": "NavBar height"}}, "navBarTitleFontSize": {"cssKey": "nav-bar-title-font-size", "desc": "navBar 标题区域字体大小", "override": "", "value": "~`px<PERSON><PERSON>(17)`", "jsValue": "@getRem@17", "staticValue": "0.34rem", "localeDesc": {"ch": "navBar 标题区域字体大小", "en": "Title font size of navBar"}}, "navBarTitlePadding": {"cssKey": "nav-bar-title-padding", "desc": "navBar 标题区域左右内边距", "override": "", "value": "0 ~`px<PERSON><PERSON>(46)`", "jsValue": "0 @getRem@46", "staticValue": "0 0.92rem", "localeDesc": {"ch": "navBar 标题区域左右内边距", "en": "Left and right padding of navBar title"}}, "navBarTitleTextFontSize": {"cssKey": "nav-bar-title-text-font-size", "desc": "navBar 标题区域文字字体大小", "override": "", "value": "~`px<PERSON><PERSON>(17)`", "jsValue": "@getRem@17", "staticValue": "0.34rem", "localeDesc": {"ch": "navBar 标题区域文字字体大小", "en": "Title text font size of navBar"}}, "navBarTwoSidesFontSize": {"cssKey": "nav-bar-two-sides-font-size", "desc": "navBar 左右两侧字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "navBar 左右两侧字体大小", "en": "Font size of left and right side of navBar"}}, "navBarTwoSidesPadding": {"cssKey": "nav-bar-two-sides-padding", "desc": "navBar 左右两侧内边距", "override": "", "value": "0 ~`px<PERSON><PERSON>(16)`", "jsValue": "0 @getRem@16", "staticValue": "0 0.32rem", "localeDesc": {"ch": "navBar 左右两侧内边距", "en": "Left and right padding of navBar"}}, "noticeBarBackground": {"cssKey": "notice-bar-background", "desc": "通知栏背景色", "override": "", "value": "#FFF7E8", "jsValue": "#FFF7E8", "staticValue": "#FFF7E8", "localeDesc": {"ch": "通知栏背景色", "en": "NoticeBar background"}}, "noticeBarColor": {"cssKey": "notice-bar-color", "desc": "通知栏文字颜色", "override": "", "value": "@warning-color", "jsValue": "@global@warningColor", "staticValue": "#FF7D00", "localeDesc": {"ch": "通知栏文字颜色", "en": "NoticeBar font color"}}, "noticeBarGradientBackground": {"cssKey": "notice-bar-gradient-background", "desc": "通知栏文字滚动时两侧渐变色", "override": "", "value": "linear-gradient(to right, #fff7e8, rgba(255, 247, 232, 0))", "jsValue": "linear-gradient(to right, #fff7e8, rgba(255, 247, 232, 0))", "staticValue": "linear-gradient(to right, #fff7e8, rgba(255, 247, 232, 0))", "localeDesc": {"ch": "通知栏文字滚动时两侧渐变色", "en": "The gradient color on both sides of the NoticeBar text when scrolling"}}, "noticeBarGradientWidth": {"cssKey": "notice-bar-gradient-width", "desc": "通知栏两侧渐变的宽度", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "通知栏两侧渐变的宽度", "en": "Gradient width on both sides of NoticeBar"}}, "noticeBarHorizontalPadding": {"cssKey": "notice-bar-horizontal-padding", "desc": "通知栏内容之间的横向间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "通知栏内容之间的横向间距", "en": "Horizontal padding between the content and the wrapper of NoticeBar"}}, "noticeBarIconFontSize": {"cssKey": "notice-bar-icon-font-size", "desc": "通知栏图标大小", "override": "", "value": "16PX", "jsValue": "16PX", "staticValue": "16PX", "localeDesc": {"ch": "通知栏图标大小", "en": "NoticeBar icon size"}}, "noticeBarLineHeight": {"cssKey": "notice-bar-line-height", "desc": "通知栏在可换行时的文字行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "通知栏在可换行时的文字行高", "en": "NoticeBar line height"}}, "noticeBarSingleLineHeight": {"cssKey": "notice-bar-single-line-height", "desc": "通知栏不可换行时所占高度", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "通知栏不可换行时所占高度", "en": "NoticeBar single-line text height"}}, "noticeBarTextFontSize": {"cssKey": "notice-bar-text-font-size", "desc": "通知栏文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "通知栏文字大小", "en": "NoticeBar font size"}}, "noticeBarVerticalPadding": {"cssKey": "notice-bar-vertical-padding", "desc": "通知栏内容与容器的纵向间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "通知栏内容与容器的纵向间距", "en": "Vertical padding between the content and the wrapper of NoticeBar"}}, "noticeBarWrapperPadding": {"cssKey": "notice-bar-wrapper-padding", "desc": "通知栏外层容器内边距", "override": "", "value": "0 ~`px<PERSON><PERSON>(16)`", "jsValue": "0 @getRem@16", "staticValue": "0 0.32rem", "localeDesc": {"ch": "通知栏外层容器内边距", "en": "NoticeBar wrapper padding"}}, "notifyErrorBackground": {"cssKey": "notify-error-background", "desc": "notify 错误通知背景色", "override": "", "value": "@danger-color", "jsValue": "@global@dangerColor", "staticValue": "#F53F3F", "localeDesc": {"ch": "notify 错误通知背景色", "en": "Background color of error notify"}}, "notifyFontColor": {"cssKey": "notify-font-color", "desc": "notify 字体颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "notify 字体颜色", "en": "Font color of notify"}}, "notifyFontSize": {"cssKey": "notify-font-size", "desc": "notify 字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "notify 字体大小", "en": "Font size of notify"}}, "notifyInfoFontColor": {"cssKey": "notify-info-font-color", "desc": "notify info 类型样式下字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "notify info 类型样式下字体颜色", "en": "Font color of info notify"}}, "notifyMinHeight": {"cssKey": "notify-min-height", "desc": "notify 内容最小高度", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "notify 内容最小高度", "en": "Min height of notify"}}, "notifySuccessBackground": {"cssKey": "notify-success-background", "desc": "notify 成功通知背景色", "override": "", "value": "@success-color", "jsValue": "@global@successColor", "staticValue": "#00B42A", "localeDesc": {"ch": "notify 成功通知背景色", "en": "Background color of success notify"}}, "notifyWarnBackground": {"cssKey": "notify-warn-background", "desc": "notify 警告通知背景色", "override": "", "value": "@warning-color", "jsValue": "@global@warningColor", "staticValue": "#FF7D00", "localeDesc": {"ch": "notify 警告通知背景色", "en": "Background color of wran notify"}}, "paginationCenterFieldGutter": {"cssKey": "pagination-center-field-gutter", "desc": "分页器居中对齐时翻页按钮与页码的间距", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "分页器居中对齐时翻页按钮与页码的间距", "en": "The spacing between the button and the page number when pagination is centered"}}, "paginationFieldBtnIconTextGutter": {"cssKey": "pagination-field-btn-icon-text-gutter", "desc": "分页器翻页按钮文字与图标的间距", "override": "", "value": "~`pxtorem(11)`", "jsValue": "@getRem@11", "staticValue": "0.22rem", "localeDesc": {"ch": "分页器翻页按钮文字与图标的间距", "en": "The spacing between the text and the icon of the pagination button"}}, "paginationFieldBtnTextFontSize": {"cssKey": "pagination-field-btn-text-font-size", "desc": "分页器翻页按钮文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "分页器翻页按钮文字大小", "en": "Font size of pagination button text"}}, "paginationFieldButtonBorderRadius": {"cssKey": "pagination-field-button-border-radius", "desc": "分页器翻页按钮圆角值", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "分页器翻页按钮圆角值", "en": "Border radius of pagination button"}}, "paginationFieldButtonMinHeight": {"cssKey": "pagination-field-button-min-height", "desc": "分页器翻页按钮最小高度", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "分页器翻页按钮最小高度", "en": "Min height of pagination button"}}, "paginationFieldButtonPadding": {"cssKey": "pagination-field-button-padding", "desc": "分页器翻页按钮内边距", "override": "", "value": "~`pxtorem(6)` ~`pxtorem(16)`", "jsValue": "@getRem@6 @getRem@16", "staticValue": "0.12rem 0.32rem", "localeDesc": {"ch": "分页器翻页按钮内边距", "en": "Padding of pagination button"}}, "paginationFieldDefaultBackground": {"cssKey": "pagination-field-default-background", "desc": "分页器翻页默认按钮背景色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "分页器翻页默认按钮背景色", "en": "Background color of the default pagination button"}}, "paginationFieldDefaultTextColor": {"cssKey": "pagination-field-default-text-color", "desc": "分页器翻页按钮默认字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "分页器翻页按钮默认字体颜色", "en": "Font color of the default pagination button"}}, "paginationFieldDisabledBackground": {"cssKey": "pagination-field-disabled-background", "desc": "分页器翻页按钮禁用背景颜色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "分页器翻页按钮禁用背景颜色", "en": "Background color of the disabled pagination button"}}, "paginationFieldDisabledTextColor": {"cssKey": "pagination-field-disabled-text-color", "desc": "分页器翻页按钮禁用字体颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "分页器翻页按钮禁用字体颜色", "en": "Font color of the disabled pagination button"}}, "paginationFieldFontSize": {"cssKey": "pagination-field-font-size", "desc": "分页器字体大小", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "分页器字体大小", "en": "Font size of pagination"}}, "paginationFieldLineHeight": {"cssKey": "pagination-field-line-height", "desc": "分页器字体行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "分页器字体行高", "en": "Line height of pagination"}}, "paginationFieldPrimaryBackground": {"cssKey": "pagination-field-primary-background", "desc": "分页器翻页primary按钮背景颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "分页器翻页primary按钮背景颜色", "en": "Background color of the primary pagination button"}}, "paginationFieldPrimaryTextColor": {"cssKey": "pagination-field-primary-text-color", "desc": "分页器翻页primary按钮字体颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "分页器翻页primary按钮字体颜色", "en": "Font color of the primary pagination button"}}, "paginationFieldTextColor": {"cssKey": "pagination-field-text-color", "desc": "分页器翻页文字按钮字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "分页器翻页文字按钮字体颜色", "en": "Font color of the pagination button text"}}, "paginationFieldTextPrimaryTextColor": {"cssKey": "pagination-field-text-primary-text-color", "desc": "分页器翻页文字primary按钮字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "分页器翻页文字primary按钮字体颜色", "en": "Font color of the primary pagination button text"}}, "paginationItemDefaultTextColor": {"cssKey": "pagination-item-default-text-color", "desc": "分页器页码显示默认颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "分页器页码显示默认颜色", "en": "Text color of the default page number"}}, "paginationItemFontSize": {"cssKey": "pagination-item-font-size", "desc": "分页器页码显示文字大小", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "分页器页码显示文字大小", "en": "Font size of the page number"}}, "paginationItemLineHeight": {"cssKey": "pagination-item-line-height", "desc": "分页器页码显示文字行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "分页器页码显示文字行高", "en": "Line height of the page number"}}, "paginationItemPrimaryTextColor": {"cssKey": "pagination-item-primary-text-color", "desc": "分页器页码显示当前页文字颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "分页器页码显示当前页文字颜色", "en": "Text color of the primary page number"}}, "paginationPadding": {"cssKey": "pagination-padding", "desc": "分页器内边距", "override": "", "value": "~`pxtorem(11)` ~`pxtorem(16)`", "jsValue": "@getRem@11 @getRem@16", "staticValue": "0.22rem 0.32rem", "localeDesc": {"ch": "分页器内边距", "en": "Padding of Pagination"}}, "pickerButtonFontSize": {"cssKey": "picker-button-font-size", "desc": "选择器按钮文字大小", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "选择器按钮文字大小", "en": "Picker button font size"}}, "pickerButtonPadding": {"cssKey": "picker-button-padding", "desc": "选择器按钮内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "选择器按钮内边距", "en": "Picker button padding"}}, "pickerHeaderBackground": {"cssKey": "picker-header-background", "desc": "选择器顶部操作栏背景色", "override": "", "value": "@mask-content-background", "jsValue": "@global@maskContentBackground", "staticValue": "#FFFFFF", "localeDesc": {"ch": "选择器顶部操作栏背景色", "en": "Top action bar background color of <PERSON><PERSON>"}}, "pickerHeaderHeight": {"cssKey": "picker-header-height", "desc": "选择器顶部操作栏高度", "override": "", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "选择器顶部操作栏高度", "en": "Top action bar height of Picker"}}, "pickerLeftBtnColor": {"cssKey": "picker-left-btn-color", "desc": "选择器左侧按钮字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "选择器左侧按钮字体颜色", "en": "Left button font color of <PERSON><PERSON>"}}, "pickerRightBtnColor": {"cssKey": "picker-right-btn-color", "desc": "选择器右侧按钮字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "选择器右侧按钮字体颜色", "en": "Right button font color of <PERSON><PERSON>"}}, "pickerTitleFontSize": {"cssKey": "picker-title-font-size", "desc": "选择器标题文字大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "选择器标题文字大小", "en": "Picker title font size"}}, "pickerTitlePadding": {"cssKey": "picker-title-padding", "desc": "选择器标题内边距", "override": "", "value": "0 ~`px<PERSON>m(60)`", "jsValue": "0 @getRem@60", "staticValue": "0 1.2rem", "localeDesc": {"ch": "选择器标题内边距", "en": "Picker title padding"}}, "pickerViewCellHeight": {"cssKey": "picker-view-cell-height", "desc": "选择器选项高度", "override": "picker-cell-height", "value": "~`px<PERSON><PERSON>(44)`", "jsValue": "@getRem@44", "staticValue": "0.88rem", "localeDesc": {"ch": "选择器选项高度", "en": "Pickerview option height"}}, "pickerViewFontSize": {"cssKey": "picker-view-font-size", "desc": "选择器选项字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "选择器选项字体大小", "en": "Picker option font size"}}, "pickerViewMaskBottomBackground": {"cssKey": "picker-view-mask-bottom-background", "desc": "选择器下半区蒙层渐变颜色", "override": "picker-mask-bottom-background", "value": "linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)", "jsValue": "linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)", "staticValue": "linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)", "localeDesc": {"ch": "选择器下半区蒙层渐变颜色", "en": "Gradient color of the mask layer in the lower half of the Pickerview"}}, "pickerViewMaskTopBackground": {"cssKey": "picker-view-mask-top-background", "desc": "选择器上半区蒙层渐变颜色", "override": "picker-mask-top-background", "value": "linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)", "jsValue": "linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)", "staticValue": "linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.7) 65%)", "localeDesc": {"ch": "选择器上半区蒙层渐变颜色", "en": "Gradient color of the mask layer in the upper half of the Pickerview"}}, "pickerViewSelectionBorderColor": {"cssKey": "picker-view-selection-border-color", "desc": "选择器选中选择框的边框颜色", "override": "picker-selection-border-color", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "选择器选中选择框的边框颜色", "en": "Border color of selection bar of Pickerview"}}, "pickerViewWrapperHeight": {"cssKey": "picker-view-wrapper-height", "desc": "选择器容器高度", "override": "picker-wrapper-height", "value": "~`pxtorem(220)`", "jsValue": "@getRem@220", "staticValue": "4.4rem", "localeDesc": {"ch": "选择器容器高度", "en": "Pickerview wrapper height"}}, "pickerWrapperBorderRadius": {"cssKey": "picker-wrapper-border-radius", "desc": "选择器弹窗顶部圆角值", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "选择器弹窗顶部圆角值", "en": "Picker wrapper border radius"}}, "pickerWrapperShadow": {"cssKey": "picker-wrapper-shadow", "desc": "选择器弹窗阴影值", "override": "", "value": "0 2PX 8PX rgba(0, 0, 0, .15)", "jsValue": "0 2PX 8PX rgba(0, 0, 0, .15)", "staticValue": "0 2PX 8PX rgba(0, 0, 0, .15)", "localeDesc": {"ch": "选择器弹窗阴影值", "en": "Picker wrapper shadow"}}, "popoverArrowBorderRadius": {"cssKey": "popover-arrow-border-radius", "desc": "气泡尖角圆角大小", "override": "", "value": "1PX", "jsValue": "1PX", "staticValue": "1PX", "localeDesc": {"ch": "气泡尖角圆角大小", "en": "Popover arrow border radius"}}, "popoverArrowSize": {"cssKey": "popover-arrow-size", "desc": "气泡尖角大小", "override": "popover-arrow-width", "value": "9PX", "jsValue": "9PX", "staticValue": "9PX", "localeDesc": {"ch": "气泡尖角大小", "en": "Popover arrow size"}}, "popoverBackgroundColor": {"cssKey": "popover-background-color", "desc": "气泡背景", "override": "", "value": "#000000", "jsValue": "#000000", "staticValue": "#000000", "localeDesc": {"ch": "气泡背景", "en": "Popover background color"}}, "popoverContentAndroidPadding": {"cssKey": "popover-content-android-padding", "desc": "Android设备气泡内容区padding", "override": "", "value": "~`pxtorem(10)` ~`pxtorem(12)` ~`pxtorem(6)`", "jsValue": "@getRem@10 @getRem@12 @getRem@6", "staticValue": "0.2rem 0.24rem 0.12rem", "localeDesc": {"ch": "Android设备气泡内容区padding", "en": "Popover content padding in Android"}}, "popoverContentBorderColor": {"cssKey": "popover-content-border-color", "desc": "气泡内容border颜色", "override": "", "value": "rgba(247, 248, 250, 0.1)", "jsValue": "rgba(247, 248, 250, 0.1)", "staticValue": "rgba(247, 248, 250, 0.1)", "localeDesc": {"ch": "气泡内容border颜色", "en": "Popover content border color"}}, "popoverContentColor": {"cssKey": "popover-content-color", "desc": "气泡内容文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "气泡内容文字颜色", "en": "Popover content font color"}}, "popoverContentDisabledColor": {"cssKey": "popover-content-disabled-color", "desc": "气泡内容禁用态文字颜色", "override": "", "value": "rgba(255, 255, 255, 0.3)", "jsValue": "rgba(255, 255, 255, 0.3)", "staticValue": "rgba(255, 255, 255, 0.3)", "localeDesc": {"ch": "气泡内容禁用态文字颜色", "en": "Popover content disabled font color"}}, "popoverContentFontSize": {"cssKey": "popover-content-font-size", "desc": "气泡内容文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "气泡内容文字大小", "en": "Popover content font size"}}, "popoverContentLineHeight": {"cssKey": "popover-content-line-height", "desc": "气泡内容文字行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "气泡内容文字行高", "en": "Popover content font line height"}}, "popoverContentPadding": {"cssKey": "popover-content-padding", "desc": "气泡内容区padding", "override": "", "value": "~`pxtorem(8)` ~`pxtorem(12)`", "jsValue": "@getRem@8 @getRem@12", "staticValue": "0.16rem 0.24rem", "localeDesc": {"ch": "气泡内容区padding", "en": "Popover content padding"}}, "popoverContentWhiteThemeBorderColor": {"cssKey": "popover-content-white-theme-border-color", "desc": "白色主题气泡内容border颜色", "override": "popover-content-border-color-white-theme", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "白色主题气泡内容border颜色", "en": "Content border color of Popover in white theme"}}, "popoverContentWhiteThemeColor": {"cssKey": "popover-content-white-theme-color", "desc": "白色主题气泡内容文字颜色", "override": "popover-content-color-white-theme", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "白色主题气泡内容文字颜色", "en": "Content font color of Popover in white theme"}}, "popoverContentWhiteThemeDisabledColor": {"cssKey": "popover-content-white-theme-disabled-color", "desc": "白色主题气泡内容禁用态文字颜色", "override": "popover-content-disabled-color-white-theme", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "白色主题气泡内容禁用态文字颜色", "en": "Content disabled font color of Popover in white theme"}}, "popoverHorizontalMenuIconMargin": {"cssKey": "popover-horizontal-menu-icon-margin", "desc": "横向菜单项margin", "override": "", "value": "~`pxtorem(0)` ~`pxtorem(0)` ~`pxtorem(8)` ~`pxtorem(0)`", "jsValue": "@getRem@0 @getRem@0 @getRem@8 @getRem@0", "staticValue": "0 0 0.16rem 0", "localeDesc": {"ch": "横向菜单项margin", "en": "Horizontal Popover menu item margin"}}, "popoverHorizontalMenuItemPadding": {"cssKey": "popover-horizontal-menu-item-padding", "desc": "横向菜单项padding", "override": "", "value": "~`pxtorem(12)` ~`pxtorem(0)`", "jsValue": "@getRem@12 @getRem@0", "staticValue": "0.24rem 0", "localeDesc": {"ch": "横向菜单项padding", "en": "Horizontal Popover menu item padding"}}, "popoverHorizontalMenuItemSize": {"cssKey": "popover-horizontal-menu-item-size", "desc": "横向菜单项大小", "override": "", "value": "~`px<PERSON><PERSON>(72)`", "jsValue": "@getRem@72", "staticValue": "1.44rem", "localeDesc": {"ch": "横向菜单项大小", "en": "Horizontal Popover menu item size"}}, "popoverHorizontalMenuMaxWidth": {"cssKey": "popover-horizontal-menu-max-width", "desc": "横向菜单气泡最大宽度", "override": "", "value": "~`px<PERSON><PERSON>(288)`", "jsValue": "@getRem@288", "staticValue": "5.76rem", "localeDesc": {"ch": "横向菜单气泡最大宽度", "en": "Horizontal Popover menu maximum width"}}, "popoverIconDividerColor": {"cssKey": "popover-icon-divider-color", "desc": "关闭图标左侧的分割线颜色", "override": "", "value": "rgba(255, 255, 255, 0.3)", "jsValue": "rgba(255, 255, 255, 0.3)", "staticValue": "rgba(255, 255, 255, 0.3)", "localeDesc": {"ch": "关闭图标左侧的分割线颜色", "en": "Divider color to the left of close icon of <PERSON>over"}}, "popoverIconDividerHeight": {"cssKey": "popover-icon-divider-height", "desc": "关闭图标左侧的分割线高度", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "关闭图标左侧的分割线高度", "en": "Divider height to the left of close icon of <PERSON>over"}}, "popoverIconPadding": {"cssKey": "popover-icon-padding", "desc": "关闭icon padding", "override": "", "value": "~`pxtorem(0)` ~`pxtorem(10)` ~`pxtorem(0)` ~`pxtorem(11)`", "jsValue": "@getRem@0 @getRem@10 @getRem@0 @getRem@11", "staticValue": "0 0.2rem 0 0.22rem", "localeDesc": {"ch": "关闭icon padding", "en": "Popover close icon padding"}}, "popoverIconSize": {"cssKey": "popover-icon-size", "desc": "关闭icon大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "关闭icon大小", "en": "Popover close icon size"}}, "popoverInnerBackgroundShadow": {"cssKey": "popover-inner-background-shadow", "desc": "气泡内容背景阴影值", "override": "", "value": "0 2PX 8PX 0 rgba(0, 0, 0, .1)", "jsValue": "0 2PX 8PX 0 rgba(0, 0, 0, .1)", "staticValue": "0 2PX 8PX 0 rgba(0, 0, 0, .1)", "localeDesc": {"ch": "气泡内容背景阴影值", "en": "Popover content background shadow"}}, "popoverInnerBorderRadius": {"cssKey": "popover-inner-border-radius", "desc": "气泡内容圆角大小", "override": "", "value": "4PX", "jsValue": "4PX", "staticValue": "4PX", "localeDesc": {"ch": "气泡内容圆角大小", "en": "Popover content border radius"}}, "popoverInnerBottomArrowShadow": {"cssKey": "popover-inner-bottom-arrow-shadow", "desc": "气泡内容底部箭头阴影值", "override": "", "value": "-6PX -6PX 8PX 0 rgba(0, 0, 0, .04)", "jsValue": "-6PX -6PX 8PX 0 rgba(0, 0, 0, .04)", "staticValue": "-6PX -6PX 8PX 0 rgba(0, 0, 0, .04)", "localeDesc": {"ch": "气泡内容底部箭头阴影值", "en": "Arrow shadow at the bottom of Popover content"}}, "popoverInnerOpacity": {"cssKey": "popover-inner-opacity", "desc": "气泡内容透明度", "override": "", "value": "0.8", "jsValue": "0.8", "staticValue": "0.8", "localeDesc": {"ch": "气泡内容透明度", "en": "Popover content opacity"}}, "popoverInnerTopArrowShadow": {"cssKey": "popover-inner-top-arrow-shadow", "desc": "气泡内容顶部箭头阴影值", "override": "", "value": "6PX 6PX 8PX 0 rgba(0, 0, 0, .04)", "jsValue": "6PX 6PX 8PX 0 rgba(0, 0, 0, .04)", "staticValue": "6PX 6PX 8PX 0 rgba(0, 0, 0, .04)", "localeDesc": {"ch": "气泡内容顶部箭头阴影值", "en": "Arrow shadow at the top of Popover content"}}, "popoverInnerTransition": {"cssKey": "popover-inner-transition", "desc": "气泡内容动画曲线", "override": "", "value": "opacity .3s ease-in-out", "jsValue": "opacity .3s ease-in-out", "staticValue": "opacity .3s ease-in-out", "localeDesc": {"ch": "气泡内容动画曲线", "en": "Popover content animation curve"}}, "popoverInnerWhiteThemeOpacity": {"cssKey": "popover-inner-white-theme-opacity", "desc": "白色主题气泡内容透明度", "override": "popover-inner-opacity-white-theme", "value": "1", "jsValue": "1", "staticValue": "1", "localeDesc": {"ch": "白色主题气泡内容透明度", "en": "Content opacity of Popover in white theme"}}, "popoverMaskBackground": {"cssKey": "popover-mask-background", "desc": "气泡遮罩背景色", "override": "", "value": "@mask-background", "jsValue": "@global@maskBackground", "staticValue": "rgba(0, 0, 0, 0.6)", "localeDesc": {"ch": "气泡遮罩背景色", "en": "Popover mask background color"}}, "popoverMenuActiveBackground": {"cssKey": "popover-menu-active-background", "desc": "气泡菜单选中态背景色", "override": "", "value": "#242425", "jsValue": "#242425", "staticValue": "#242425", "localeDesc": {"ch": "气泡菜单选中态背景色", "en": "Selected option background color of PopoverMenu"}}, "popoverMenuActiveWhiteThemeBackground": {"cssKey": "popover-menu-active-white-theme-background", "desc": "白色主题气泡菜单选中态背景色", "override": "popover-menu-active-background-white-theme", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "白色主题气泡菜单选中态背景色", "en": "Selected option background color of PopoverMenu in white theme"}}, "popoverMenuContentPadding": {"cssKey": "popover-menu-content-padding", "desc": "气泡菜单内容padding", "override": "", "value": "0 ~`px<PERSON><PERSON>(12)`", "jsValue": "0 @getRem@12", "staticValue": "0 0.24rem", "localeDesc": {"ch": "气泡菜单内容padding", "en": "PopoverMenu content padding"}}, "popoverMenuIconWhiteThemeColor": {"cssKey": "popover-menu-icon-white-theme-color", "desc": "白色主题气泡菜单的icon颜色", "override": "popover-menu-icon-color-white-theme", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "白色主题气泡菜单的icon颜色", "en": "Icon color of PopoverMenu in white theme"}}, "popoverShadowColor": {"cssKey": "popover-shadow-color", "desc": "气泡阴影颜色", "override": "", "value": "rgba(0, 0, 0, 0.1)", "jsValue": "rgba(0, 0, 0, 0.1)", "staticValue": "rgba(0, 0, 0, 0.1)", "localeDesc": {"ch": "气泡阴影颜色", "en": "Popover shadow color"}}, "popoverTextSuffixEdge": {"cssKey": "popover-text-suffix-edge", "desc": "textSuffix 元素和气泡边缘的距离", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "textSuffix 元素和气泡边缘的距离", "en": "Popover textSuffix element padding"}}, "popoverWhiteThemeBackgroundColor": {"cssKey": "popover-white-theme-background-color", "desc": "白色主题气泡背景", "override": "popover-background-color-white-theme", "value": "@mask-content-background", "jsValue": "@global@maskContentBackground", "staticValue": "#FFFFFF", "localeDesc": {"ch": "白色主题气泡背景", "en": "Background color  of Popover in white theme"}}, "popupContentBackground": {"cssKey": "popup-content-background", "desc": "弹窗内容面板背景色", "override": "", "value": "@mask-content-background", "jsValue": "@global@maskContentBackground", "staticValue": "#FFFFFF", "localeDesc": {"ch": "弹窗内容面板背景色", "en": "Popup content panel background color"}}, "popupEnterTransition": {"cssKey": "popup-enter-transition", "desc": "打开弹窗时的变化曲线", "override": "", "value": "all 450ms cubic-bezier(0.34, 0.69, 0.1, 1)", "jsValue": "all 450ms cubic-bezier(0.34, 0.69, 0.1, 1)", "staticValue": "all 450ms cubic-bezier(0.34, 0.69, 0.1, 1)", "localeDesc": {"ch": "打开弹窗时的变化曲线", "en": "The change curve when opening the popup"}}, "popupExitTransition": {"cssKey": "popup-exit-transition", "desc": "关闭弹窗时的变化曲线", "override": "", "value": "all 240ms cubic-bezier(0.34, 0.69, 0.1, 1)", "jsValue": "all 240ms cubic-bezier(0.34, 0.69, 0.1, 1)", "staticValue": "all 240ms cubic-bezier(0.34, 0.69, 0.1, 1)", "localeDesc": {"ch": "关闭弹窗时的变化曲线", "en": "Change curve when closing the popup"}}, "popupMaskBackground": {"cssKey": "popup-mask-background", "desc": "弹窗蒙层背景色", "override": "", "value": "@mask-background", "jsValue": "@global@maskBackground", "staticValue": "rgba(0, 0, 0, 0.6)", "localeDesc": {"ch": "弹窗蒙层背景色", "en": "Popup mask background color"}}, "prefix": {"cssKey": "prefix", "desc": "类前缀，需配合 context-provider 中的 prefixCls 使用", "override": "", "value": "arco", "jsValue": "arco", "staticValue": "arco", "localeDesc": {"ch": "类前缀，需配合 context-provider 中的 prefixCls 使用", "en": "Classname prefix, it needs to be used with prefixCls in context-provider"}}, "primaryColor": {"cssKey": "primary-color", "desc": "基础主题色", "override": "", "value": "#165DFF", "jsValue": "#165DFF", "staticValue": "#165DFF", "localeDesc": {"ch": "基础主题色", "en": "Base theme color"}}, "primaryDisabledColor": {"cssKey": "primary-disabled-color", "desc": "基础禁用态颜色", "override": "", "value": "#94BFFF", "jsValue": "#94BFFF", "staticValue": "#94BFFF", "localeDesc": {"ch": "基础禁用态颜色", "en": "Base disabled color"}}, "progressDisabledColor": {"cssKey": "progress-disabled-color", "desc": "进度条 不可用状态进度条颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "进度条 不可用状态进度条颜色", "en": "Progress bar disabled track color"}}, "progressDisabledTextColor": {"cssKey": "progress-disabled-text-color", "desc": "进度条 不可用状态文字颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "进度条 不可用状态文字颜色", "en": "Progress bar disabled font color"}}, "progressInnerTrackHeight": {"cssKey": "progress-inner-track-height", "desc": "进度条 百分比内显轨道高度", "override": "", "value": "18PX", "jsValue": "18PX", "staticValue": "18PX", "localeDesc": {"ch": "进度条 百分比内显轨道高度", "en": "Progress bar track height which percentage is in"}}, "progressLinearGradientEndColor": {"cssKey": "progress-linear-gradient-end-color", "desc": "进度条 自定义进度条颜色渐变结束的颜色", "override": "", "value": "#14CAFF", "jsValue": "#14CAFF", "staticValue": "#14CAFF", "localeDesc": {"ch": "进度条 自定义进度条颜色渐变结束的颜色", "en": "End gradient color of Progress bar track color"}}, "progressLinearGradientStartColor": {"cssKey": "progress-linear-gradient-start-color", "desc": "进度条 自定义进度条颜色渐变开始的颜色", "override": "", "value": "#4776E6", "jsValue": "#4776E6", "staticValue": "#4776E6", "localeDesc": {"ch": "进度条 自定义进度条颜色渐变开始的颜色", "en": "Start gradient color of Progress bar track color"}}, "progressLinearGradientTextColor": {"cssKey": "progress-linear-gradient-text-color", "desc": "进度条 自定义进度条文字颜色", "override": "", "value": "#3C89EC", "jsValue": "#3C89EC", "staticValue": "#3C89EC", "localeDesc": {"ch": "进度条 自定义进度条文字颜色", "en": "Progress bar font color"}}, "progressNavTrackColor": {"cssKey": "progress-nav-track-color", "desc": "进度条 导航式进度条轨道颜色", "override": "", "value": "transparent", "jsValue": "transparent", "staticValue": "transparent", "localeDesc": {"ch": "进度条 导航式进度条轨道颜色", "en": "Navigation progress bar track color"}}, "progressNavTrackHeight": {"cssKey": "progress-nav-track-height", "desc": "进度条 导航式进度条轨道高度", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "进度条 导航式进度条轨道高度", "en": "Navigation progress bar track height"}}, "progressPrimaryColor": {"cssKey": "progress-primary-color", "desc": "进度条 进度条颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "进度条 进度条颜色", "en": "Progress bar color"}}, "progressTextFollowBorderRadius": {"cssKey": "progress-text-follow-border-radius", "desc": "进度条 跟随进度样式的文字圆角值", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "进度条 跟随进度样式的文字圆角值", "en": "Border radius of text followed by Progress bar"}}, "progressTextFollowFontSize": {"cssKey": "progress-text-follow-font-size", "desc": "进度条 跟随进度样式的文字大小", "override": "", "value": "~`px<PERSON>m(13)`", "jsValue": "@getRem@13", "staticValue": "0.26rem", "localeDesc": {"ch": "进度条 跟随进度样式的文字大小", "en": "Font size of text followed by Progress bar"}}, "progressTextFollowHeight": {"cssKey": "progress-text-follow-height", "desc": "进度条 跟随进度样式的文字高度", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "进度条 跟随进度样式的文字高度", "en": "Text height followed by Progress bar"}}, "progressTextFollowWidth": {"cssKey": "progress-text-follow-width", "desc": "进度条 跟随进度样式的文字宽度", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "进度条 跟随进度样式的文字宽度", "en": "Text width followed by Progress bar"}}, "progressTextFontSize": {"cssKey": "progress-text-font-size", "desc": "进度条 文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "进度条 文字大小", "en": "Progress bar font size"}}, "progressTextGutter": {"cssKey": "progress-text-gutter", "desc": "进度条 文字在两侧时与进度条的间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "进度条 文字在两侧时与进度条的间距", "en": "The distance between the progress bar and text when the text is on both sides"}}, "progressTextInnerColor": {"cssKey": "progress-text-inner-color", "desc": "进度条 百分比内显文字颜色", "override": "progress-text-inner", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "进度条 百分比内显文字颜色", "en": "Progress bar font color which percentage is in"}}, "progressTrackColor": {"cssKey": "progress-track-color", "desc": "进度条 轨道颜色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "进度条 轨道颜色", "en": "Progress bar track color"}}, "progressTrackHeight": {"cssKey": "progress-track-height", "desc": "进度条 轨道高度", "override": "", "value": "4PX", "jsValue": "4PX", "staticValue": "4PX", "localeDesc": {"ch": "进度条 轨道高度", "en": "Progress bar track height"}}, "pullRefreshContentBackgroundColor": {"cssKey": "pull-refresh-content-background-color", "desc": "下拉刷新组件容器背景色", "override": "", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "下拉刷新组件容器背景色", "en": "PullRefresh content background color"}}, "pullRefreshLabelBackgroundColor": {"cssKey": "pull-refresh-label-background-color", "desc": "下拉刷新组件状态文案内容区背景色", "override": "pull-refresh-wrapper-background-color", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "下拉刷新组件状态文案内容区背景色", "en": "PullRefresh label background color"}}, "pullRefreshLabelFontSize": {"cssKey": "pull-refresh-label-font-size", "desc": "下拉刷新组件状态文案字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "下拉刷新组件状态文案字体大小", "en": "PullRefresh label font size"}}, "pullRefreshLabelLoadingColor": {"cssKey": "pull-refresh-label-loading-color", "desc": "下拉刷组件 loading 图标颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "下拉刷组件 loading 图标颜色", "en": "PullRefresh loading icon color"}}, "pullRefreshLabelTextColor": {"cssKey": "pull-refresh-label-text-color", "desc": "下拉刷新组件状态文案字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "下拉刷新组件状态文案字体颜色", "en": "PullRefresh label font color"}}, "rateIconActiveColor": {"cssKey": "rate-icon-active-color", "desc": "评分图标选中高亮色", "override": "", "value": "#FFB400", "jsValue": "#FFB400", "staticValue": "#FFB400", "localeDesc": {"ch": "评分图标选中高亮色", "en": "Active color of Rate icon"}}, "rateIconDisabledActiveColor": {"cssKey": "rate-icon-disabled-active-color", "desc": "评分图标禁用时选中颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "评分图标禁用时选中颜色", "en": "Active color of disabled Rate icon"}}, "rateIconNormalColor": {"cssKey": "rate-icon-normal-color", "desc": "评分图标未选中颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "评分图标未选中颜色", "en": "Normal color of Rate icon"}}, "rateIconOffset": {"cssKey": "rate-icon-offset", "desc": "评分图标间距(点击热区含间距)", "override": "", "value": "6PX", "jsValue": "6PX", "staticValue": "6PX", "localeDesc": {"ch": "评分图标间距(点击热区含间距)", "en": "Rating icon spacing (click hotspot includes spacing)"}}, "rateIconSize": {"cssKey": "rate-icon-size", "desc": "评分图标大小", "override": "", "value": "24PX", "jsValue": "24PX", "staticValue": "24PX", "localeDesc": {"ch": "评分图标大小", "en": "Rate icon size"}}, "scrollerBuffer": {"cssKey": "scroller-buffer", "desc": "隐藏滚动条预留宽度", "override": "", "value": "10PX", "jsValue": "10PX", "staticValue": "10PX", "localeDesc": {"ch": "隐藏滚动条预留宽度", "en": "Hidden scrollbar reserved width"}}, "searchBarAssociationBackgroundColor": {"cssKey": "search-bar-association-background-color", "desc": "搜索联想框背景颜色", "override": "", "value": "@container-background-color", "jsValue": "@global@containerBackgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "搜索联想框背景颜色", "en": "Background color of SearchBar association"}}, "searchBarAssociationItemColor": {"cssKey": "search-bar-association-item-color", "desc": "搜索联想框候选项普通文字颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "搜索联想框候选项普通文字颜色", "en": "Color of SearchBar association item"}}, "searchBarAssociationItemFontSize": {"cssKey": "search-bar-association-item-font-size", "desc": "搜索联想框候选项字体大小", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "搜索联想框候选项字体大小", "en": "Fontsize of SearchBar association item"}}, "searchBarAssociationItemHeight": {"cssKey": "search-bar-association-item-height", "desc": "搜索联想框候选项高度", "override": "", "value": "~`pxtorem(52)`", "jsValue": "@getRem@52", "staticValue": "1.04rem", "localeDesc": {"ch": "搜索联想框候选项高度", "en": "Height of SearchBar association item"}}, "searchBarAssociationItemHighlightColor": {"cssKey": "search-bar-association-item-highlight-color", "desc": "搜索联想框候选项高亮文案颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "搜索联想框候选项高亮文案颜色", "en": "Color of SearchBar association item highlight text"}}, "searchBarAssociationItemPadding": {"cssKey": "search-bar-association-item-padding", "desc": "搜索联想框候选项内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "搜索联想框候选项内边距", "en": "Padding of SearchBar association item"}}, "searchBarBackgroundColor": {"cssKey": "search-bar-background-color", "desc": "搜索栏背景颜色", "override": "", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "搜索栏背景颜色", "en": "BackgroundColor of SearchBar"}}, "searchBarCancelBtnColor": {"cssKey": "search-bar-cancel-btn-color", "desc": "搜索栏右侧取消按钮颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "搜索栏右侧取消按钮颜色", "en": "Color of SearchBar cancel button"}}, "searchBarCancelBtnFontSize": {"cssKey": "search-bar-cancel-btn-font-size", "desc": "搜索栏右侧取消按钮文字大小", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "搜索栏右侧取消按钮文字大小", "en": "Font size of SearchBar cancel button"}}, "searchBarCancelBtnMarginLeft": {"cssKey": "search-bar-cancel-btn-margin-left", "desc": "搜索栏右侧取消按钮的左边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "搜索栏右侧取消按钮的左边距", "en": "Left margin of SearchBar cancel button"}}, "searchBarClearIconColor": {"cssKey": "search-bar-clear-icon-color", "desc": "搜索栏清除按钮的颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "搜索栏清除按钮的颜色", "en": "Color of SearchBar clear icon"}}, "searchBarClearIconFontSize": {"cssKey": "search-bar-clear-icon-font-size", "desc": "搜索栏清除按钮的大小", "override": "", "value": "16PX", "jsValue": "16PX", "staticValue": "16PX", "localeDesc": {"ch": "搜索栏清除按钮的大小", "en": "Size of SearchBar clear icon"}}, "searchBarClearIconPaddingLeft": {"cssKey": "search-bar-clear-icon-padding-left", "desc": "搜索栏清除按钮与输入框间距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "搜索栏清除按钮与输入框间距", "en": "Padding left of SearchBar clear icon"}}, "searchBarInputCaretColor": {"cssKey": "search-bar-input-caret-color", "desc": "搜索输入框光标颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "搜索输入框光标颜色", "en": "Color of SearchBar input caret"}}, "searchBarInputHeight": {"cssKey": "search-bar-input-height", "desc": "搜索输入框高度", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "搜索输入框高度", "en": "SearchBar input height"}}, "searchBarInputPlaceholderColor": {"cssKey": "search-bar-input-placeholder-color", "desc": "搜索输入框提示文案颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "搜索输入框提示文案颜色", "en": "Color of SearchBar input placeholder"}}, "searchBarInputWrapperBackgroundColor": {"cssKey": "search-bar-input-wrapper-background-color", "desc": "搜索输入框容器背景颜色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "搜索输入框容器背景颜色", "en": "SearchBar input container background color"}}, "searchBarInputWrapperFontSize": {"cssKey": "search-bar-input-wrapper-font-size", "desc": "搜索输入框容器字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "搜索输入框容器字体大小", "en": "SearchBar input container font size"}}, "searchBarInputWrapperHeight": {"cssKey": "search-bar-input-wrapper-height", "desc": "搜索输入框容器高度", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "搜索输入框容器高度", "en": "SearchBar input container height"}}, "searchBarInputWrapperPadding": {"cssKey": "search-bar-input-wrapper-padding", "desc": "搜索输入框容器内边距大小", "override": "", "value": "~`pxtorem(8)` ~`pxtorem(14)`", "jsValue": "@getRem@8 @getRem@14", "staticValue": "0.16rem 0.28rem", "localeDesc": {"ch": "搜索输入框容器内边距大小", "en": "SearchBar input container padding"}}, "searchBarPadding": {"cssKey": "search-bar-padding", "desc": "搜索栏内边距大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "搜索栏内边距大小", "en": "Padding of SearchBar"}}, "searchBarPrefixMarginRight": {"cssKey": "search-bar-prefix-margin-right", "desc": "搜索栏搜索栏左侧插入内容的右侧外边距", "override": "", "value": "~`pxtorem(9)`", "jsValue": "@getRem@9", "staticValue": "0.18rem", "localeDesc": {"ch": "搜索栏搜索栏左侧插入内容的右侧外边距", "en": "Right margin of SearchBar prefix"}}, "searchBarRoundShapeBorderRadius": {"cssKey": "search-bar-round-shape-border-radius", "desc": "圆形搜索栏的圆角大小", "override": "", "value": "~`pxtorem(9999)`", "jsValue": "@getRem@9999", "staticValue": "199.98rem", "localeDesc": {"ch": "圆形搜索栏的圆角大小", "en": "Size of the rounded corners of the round SearchBar"}}, "searchBarSearchIconColor": {"cssKey": "search-bar-search-icon-color", "desc": "搜索栏搜索按钮的颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "搜索栏搜索按钮的颜色", "en": "Color of SearchBar search icon"}}, "searchBarSearchIconFontSize": {"cssKey": "search-bar-search-icon-font-size", "desc": "搜索栏按钮大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "搜索栏按钮大小", "en": "Fontsize of SearchBar search icon"}}, "searchBarSquareShapeBorderRadius": {"cssKey": "search-bar-square-shape-border-radius", "desc": "方形搜索栏的圆角大小", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "方形搜索栏的圆角大小", "en": "Size of the rounded corners of the square SearchBar"}}, "skeletonAvatarSize": {"cssKey": "skeleton-avatar-size", "desc": "骨架屏头像大小", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "骨架屏头像大小", "en": "Skeleton avatar size"}}, "skeletonBackgroundColor": {"cssKey": "skeleton-background-color", "desc": "骨架屏元素背景色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "骨架屏元素背景色", "en": "Skeleton element background color"}}, "skeletonBorderRadius": {"cssKey": "skeleton-border-radius", "desc": "骨架屏元素圆角", "override": "", "value": "~`pxtorem(0)`", "jsValue": "@getRem@0", "staticValue": "0", "localeDesc": {"ch": "骨架屏元素圆角", "en": "Skeleton element border radius"}}, "skeletonBreathAnimationDuration": {"cssKey": "skeleton-breath-animation-duration", "desc": "骨架屏呼吸动效时间", "override": "", "value": "1.5s", "jsValue": "1.5s", "staticValue": "1.5s", "localeDesc": {"ch": "骨架屏呼吸动效时间", "en": "Skeleton element breath animation duration"}}, "skeletonBreathOpacity": {"cssKey": "skeleton-breath-opacity", "desc": "骨架屏呼吸动效透明度", "override": "", "value": "0.4", "jsValue": "0.4", "staticValue": "0.4", "localeDesc": {"ch": "骨架屏呼吸动效透明度", "en": "Skeleton element breath animation opacity"}}, "skeletonGradientAnimationColor": {"cssKey": "skeleton-gradient-animation-color", "desc": "骨架屏扫光动效高亮色", "override": "", "value": "rgba(255, 255, 255, 0.6)", "jsValue": "rgba(255, 255, 255, 0.6)", "staticValue": "rgba(255, 255, 255, 0.6)", "localeDesc": {"ch": "骨架屏扫光动效高亮色", "en": "Skeleton element gradient animation highlight color"}}, "skeletonGradientAnimationDuration": {"cssKey": "skeleton-gradient-animation-duration", "desc": "骨架屏扫光动效时间", "override": "", "value": "1.5s", "jsValue": "1.5s", "staticValue": "1.5s", "localeDesc": {"ch": "骨架屏扫光动效时间", "en": "Skeleton element gradient animation duration"}}, "skeletonGradientAnimationTimingFunction": {"cssKey": "skeleton-gradient-animation-timing-function", "desc": "骨架屏扫光动效曲线", "override": "", "value": "cubic-bezier(0.42, 0, 0.58, 1)", "jsValue": "cubic-bezier(0.42, 0, 0.58, 1)", "staticValue": "cubic-bezier(0.42, 0, 0.58, 1)", "localeDesc": {"ch": "骨架屏扫光动效曲线", "en": "Skeleton element gradient animation timing function"}}, "skeletonGridIconSize": {"cssKey": "skeleton-grid-icon-size", "desc": "骨架屏金刚位图标区宽度", "override": "", "value": "~`pxtorem(32)`", "jsValue": "@getRem@32", "staticValue": "0.64rem", "localeDesc": {"ch": "骨架屏金刚位图标区宽度", "en": "Skeleton grid item icon width"}}, "skeletonGridTextHeight": {"cssKey": "skeleton-grid-text-height", "desc": "骨架屏金刚位文字区高度", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "骨架屏金刚位文字区高度", "en": "Skeleton grid item text height"}}, "skeletonGridTextWidth": {"cssKey": "skeleton-grid-text-width", "desc": "骨架屏金刚位文字区宽度", "override": "", "value": "~`px<PERSON>m(64)`", "jsValue": "@getRem@64", "staticValue": "1.28rem", "localeDesc": {"ch": "骨架屏金刚位文字区宽度", "en": "Skeleton grid item text width"}}, "skeletonLargeGutter": {"cssKey": "skeleton-large-gutter", "desc": "骨架屏元素外边距，大尺寸", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "骨架屏元素外边距，大尺寸"}}, "skeletonMediumGutter": {"cssKey": "skeleton-medium-gutter", "desc": "骨架屏元素外边距，中尺寸", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "骨架屏元素外边距，中尺寸"}}, "skeletonParagraphHeight": {"cssKey": "skeleton-paragraph-height", "desc": "骨架屏段落行高度", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "骨架屏段落行高度", "en": "Skeleton paragraph line height"}}, "skeletonParagraphMarginTop": {"cssKey": "skeleton-paragraph-margin-top", "desc": "骨架屏各段落行间距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "骨架屏各段落行间距", "en": "Margin top between skeleton paragraph lines"}}, "skeletonTitleHeight": {"cssKey": "skeleton-title-height", "desc": "骨架屏标题高度", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "骨架屏标题高度", "en": "Skeleton title height"}}, "sliderHasMarkPaddingBottom": {"cssKey": "slider-has-mark-padding-bottom", "desc": "slider 有标记时的底部内边距", "override": "", "value": "~`px<PERSON>m(35)`", "jsValue": "@getRem@35", "staticValue": "0.7rem", "localeDesc": {"ch": "slider 有标记时的底部内边距", "en": "Bottom padding of Slider with mask"}}, "sliderHorizontalMarkLabelTop": {"cssKey": "slider-horizontal-mark-label-top", "desc": "slider 横向滑动条节点文案距离节点的顶部距离", "override": "", "value": "~`px<PERSON><PERSON>(19)`", "jsValue": "@getRem@19", "staticValue": "0.38rem", "localeDesc": {"ch": "slider 横向滑动条节点文案距离节点的顶部距离", "en": "Label distance  of horizontal slider from the top of the mark"}}, "sliderLabelFontSize": {"cssKey": "slider-label-font-size", "desc": "slider 两侧标注文字大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "slider 两侧标注文字大小", "en": "The size of the text on both sides of the slider"}}, "sliderLabelGutter": {"cssKey": "slider-label-gutter", "desc": "slider 两侧标注与滑动条的间距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "slider 两侧标注与滑动条的间距", "en": "The distance between the labels on both sides of the slider and the slider"}}, "sliderLineActivatedColor": {"cssKey": "slider-line-activated-color", "desc": "slider 被选中时的线条颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "slider 被选中时的线条颜色", "en": "Active slider line Color"}}, "sliderLineBorderRadius": {"cssKey": "slider-line-border-radius", "desc": "slider 线条的边框半径（首尾）", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "slider 线条的边框半径（首尾）", "en": "Slider line border radius"}}, "sliderLineColor": {"cssKey": "slider-line-color", "desc": "slider 线条颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "slider 线条颜色", "en": "Slider line color"}}, "sliderLineDisabledColor": {"cssKey": "slider-line-disabled-color", "desc": "slider 被禁用时的线条颜色", "override": "", "value": "@primary-disabled-color", "jsValue": "@global@primaryDisabledColor", "staticValue": "#94BFFF", "localeDesc": {"ch": "slider 被禁用时的线条颜色", "en": "Disabled slider line Color"}}, "sliderMarkBorderRadius": {"cssKey": "slider-mark-border-radius", "desc": "slider 滑动条节点的边框半径", "override": "", "value": "50%", "jsValue": "50%", "staticValue": "50%", "localeDesc": {"ch": "slider 滑动条节点的边框半径", "en": "Slider mark border radius"}}, "sliderMarkHeight": {"cssKey": "slider-mark-height", "desc": "slider 滑动条节点的高度", "override": "", "value": "6PX", "jsValue": "6PX", "staticValue": "6PX", "localeDesc": {"ch": "slider 滑动条节点的高度", "en": "Slider mark height"}}, "sliderMarkLabelFontSize": {"cssKey": "slider-mark-label-font-size", "desc": "slider 滑动条节点文案的字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "slider 滑动条节点文案的字体大小", "en": "Slider mark label font size"}}, "sliderMarkLabelLineHeight": {"cssKey": "slider-mark-label-line-height", "desc": "slider 滑动条节点文案的字体行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "slider 滑动条节点文案的字体行高", "en": "Slider mark label font line height"}}, "sliderMarkWidth": {"cssKey": "slider-mark-width", "desc": "slider 滑动条节点的宽度", "override": "", "value": "6PX", "jsValue": "6PX", "staticValue": "6PX", "localeDesc": {"ch": "slider 滑动条节点的宽度", "en": "Slider mark width"}}, "sliderMaskPadding": {"cssKey": "slider-mask-padding", "desc": "slider 可点区域两侧留白边距", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "slider 可点区域两侧留白边距", "en": "Slider mask padding"}}, "sliderPadding": {"cssKey": "slider-padding", "desc": "slider 组件的内边距", "override": "", "value": "~`pxtorem(11)` ~`pxtorem(16)`", "jsValue": "@getRem@11 @getRem@16", "staticValue": "0.22rem 0.32rem", "localeDesc": {"ch": "slider 组件的内边距", "en": "Slider padding"}}, "sliderPopoverArrowSize": {"cssKey": "slider-popover-arrow-size", "desc": "slider 气泡箭头大小", "override": "", "value": "6PX", "jsValue": "6PX", "staticValue": "6PX", "localeDesc": {"ch": "slider 气泡箭头大小", "en": "Slider popover arrow size"}}, "sliderPopoverFontSize": {"cssKey": "slider-popover-font-size", "desc": "slider 气泡内容字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "slider 气泡内容字体大小", "en": "Slider popover content font size"}}, "sliderPopoverGutter": {"cssKey": "slider-popover-gutter", "desc": "slider 气泡底部的外边距（垂直时是左外边距）", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "slider 气泡底部的外边距（垂直时是左外边距）", "en": "Bottom margin of slider popover  (left margin when vertical)"}}, "sliderPopoverLineHeight": {"cssKey": "slider-popover-line-height", "desc": "slider 气泡内容字体行高", "override": "", "value": "~`px<PERSON><PERSON>(17)`", "jsValue": "@getRem@17", "staticValue": "0.34rem", "localeDesc": {"ch": "slider 气泡内容字体行高", "en": "Slider popover content font line height"}}, "sliderTextColor": {"cssKey": "slider-text-color", "desc": "slider 标记文案的字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "slider 标记文案的字体颜色", "en": "Slider text font Color"}}, "sliderThumbBackground": {"cssKey": "slider-thumb-background", "desc": "slider 滑块的背景色", "override": "slider-thumb-color", "value": "@mask-content-background", "jsValue": "@global@maskContentBackground", "staticValue": "#FFFFFF", "localeDesc": {"ch": "slider 滑块的背景色", "en": "Slider button background"}}, "sliderThumbBorderRadius": {"cssKey": "slider-thumb-border-radius", "desc": "slider 滑块的边框半径", "override": "", "value": "50%", "jsValue": "50%", "staticValue": "50%", "localeDesc": {"ch": "slider 滑块的边框半径", "en": "Slider button border radius"}}, "sliderThumbBoxShadow": {"cssKey": "slider-thumb-box-shadow", "desc": "slider 滑块的阴影", "override": "", "value": "0 2PX 8PX rgba(0, 0, 0, .1)", "jsValue": "0 2PX 8PX rgba(0, 0, 0, .1)", "staticValue": "0 2PX 8PX rgba(0, 0, 0, .1)", "localeDesc": {"ch": "slider 滑块的阴影", "en": "Slider button box shadow"}}, "sliderThumbHeight": {"cssKey": "slider-thumb-height", "desc": "slider 滑块的高度", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "slider 滑块的高度", "en": "Slider button height"}}, "sliderThumbWidth": {"cssKey": "slider-thumb-width", "desc": "slider 滑块的宽度", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "slider 滑块的宽度", "en": "Slider button width"}}, "sliderVerticalMarkLabelRight": {"cssKey": "slider-vertical-mark-label-right", "desc": "slider 纵向滑动条节点文案距离节点的右距离", "override": "", "value": "~`px<PERSON>m(13)`", "jsValue": "@getRem@13", "staticValue": "0.26rem", "localeDesc": {"ch": "slider 纵向滑动条节点文案距离节点的右距离", "en": "Label distance  of horizoverticalntal slider from the right of the mark"}}, "stepperButtonIconSize": {"cssKey": "stepper-button-icon-size", "desc": "步进器按钮图标尺寸", "override": "", "value": "~`pxtorem(10)`", "jsValue": "@getRem@10", "staticValue": "0.2rem", "localeDesc": {"ch": "步进器按钮图标尺寸", "en": "Button icon size of <PERSON><PERSON>"}}, "stepperButtonSize": {"cssKey": "stepper-button-size", "desc": "步进器按钮尺寸", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "步进器按钮尺寸", "en": "Button size of Stepper"}}, "stepperContentColor": {"cssKey": "stepper-content-color", "desc": "步进器内容字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "步进器内容字体颜色", "en": "Content text color of <PERSON><PERSON>"}}, "stepperDefaultBackgroundColor": {"cssKey": "stepper-default-background-color", "desc": "步进器默认背景颜色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "步进器默认背景颜色", "en": "Background default colr of <PERSON><PERSON>"}}, "stepperDisableColor": {"cssKey": "stepper-disable-color", "desc": "步进器禁用状态字体颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "步进器禁用状态字体颜色", "en": "Text color of <PERSON><PERSON> in disable status"}}, "stepperFontSize": {"cssKey": "stepper-font-size", "desc": "步进器字体大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "步进器字体大小", "en": "Font size of Stepper"}}, "stepperInputHeight": {"cssKey": "stepper-input-height", "desc": "步进器输入框长度", "override": "", "value": "~`px<PERSON><PERSON>(28)`", "jsValue": "@getRem@28", "staticValue": "0.56rem", "localeDesc": {"ch": "步进器输入框长度", "en": "Input height of Stepper"}}, "stepperInputMargin": {"cssKey": "stepper-input-margin", "desc": "步进器输入框外边距", "override": "", "value": "0 1PX", "jsValue": "0 1PX", "staticValue": "0 1PX", "localeDesc": {"ch": "步进器输入框外边距", "en": "Input margin of Stepper"}}, "stepperInputWidth": {"cssKey": "stepper-input-width", "desc": "步进器输入框宽度", "override": "", "value": "~`pxtorem(40)`", "jsValue": "@getRem@40", "staticValue": "0.8rem", "localeDesc": {"ch": "步进器输入框宽度", "en": "Input width of Stepper"}}, "stepperRoundButtonBorderRadius": {"cssKey": "stepper-round-button-border-radius", "desc": "步进器圆角按钮边框半径", "override": "", "value": "50%", "jsValue": "50%", "staticValue": "50%", "localeDesc": {"ch": "步进器圆角按钮边框半径", "en": "Round button border radius of Stepper"}}, "stepperRoundInputBackgroundColor": {"cssKey": "stepper-round-input-background-color", "desc": "步进器圆角输入框背景颜色", "override": "", "value": "transparent", "jsValue": "transparent", "staticValue": "transparent", "localeDesc": {"ch": "步进器圆角输入框背景颜色", "en": "Rount input background color of <PERSON><PERSON>"}}, "stepperSquareBackgroundColor": {"cssKey": "stepper-square-background-color", "desc": "步进器方角样式背景颜色", "override": "", "value": "transparent", "jsValue": "transparent", "staticValue": "transparent", "localeDesc": {"ch": "步进器方角样式背景颜色", "en": "Background color of Stepper square style"}}, "stepperSquareBorderColor": {"cssKey": "stepper-square-border-color", "desc": "步进器方角边框样式", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "步进器方角边框样式", "en": "Square border style of Stepper"}}, "stepperSquareBorderRadius": {"cssKey": "stepper-square-border-radius", "desc": "步进器方角边框半径", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "步进器方角边框半径", "en": "Square border radius of Stepper"}}, "stepperWidth": {"cssKey": "stepper-width", "desc": "步进器整体宽度", "override": "", "value": "~`px<PERSON><PERSON>(98)`", "jsValue": "@getRem@98", "staticValue": "1.96rem", "localeDesc": {"ch": "步进器整体宽度", "en": "Width of Stepper"}}, "stepsDescriptionColor": {"cssKey": "steps-description-color", "desc": "steps 步骤默认描述文字颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "steps 步骤默认描述文字颜色", "en": "Steps description font color"}}, "stepsDescriptionFontSize": {"cssKey": "steps-description-font-size", "desc": "steps 描述文字大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "steps 描述文字大小", "en": "Steps description font size"}}, "stepsDescriptionLineHeight": {"cssKey": "steps-description-line-height", "desc": "steps 描述文字行高", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "steps 描述文字行高", "en": "Steps description font line height"}}, "stepsDescriptionMarginTop": {"cssKey": "steps-description-margin-top", "desc": "steps 描述文字外边距", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "steps 描述文字外边距", "en": "Steps description top margin"}}, "stepsDotBorderWidth": {"cssKey": "steps-dot-border-width", "desc": "steps 迷你版圆点边框宽度", "override": "", "value": "1.5PX", "jsValue": "1.5PX", "staticValue": "1.5PX", "localeDesc": {"ch": "steps 迷你版圆点边框宽度", "en": "Dot border width of mini Steps"}}, "stepsDotHeight": {"cssKey": "steps-dot-height", "desc": "steps 迷你版圆点高度", "override": "", "value": "8PX", "jsValue": "8PX", "staticValue": "8PX", "localeDesc": {"ch": "steps 迷你版圆点高度", "en": "Dot height of mini Steps"}}, "stepsDotWidth": {"cssKey": "steps-dot-width", "desc": "steps 迷你版圆点宽度", "override": "", "value": "8PX", "jsValue": "8PX", "staticValue": "8PX", "localeDesc": {"ch": "steps 迷你版圆点宽度", "en": "Dot width of mini Steps"}}, "stepsErrorIconNumBackground": {"cssKey": "steps-error-icon-num-background", "desc": "steps 无文字错误步骤背景色", "override": "", "value": "@danger-color", "jsValue": "@global@dangerColor", "staticValue": "#F53F3F", "localeDesc": {"ch": "steps 无文字错误步骤背景色", "en": "Non-text steps background color in error state"}}, "stepsErrorIconSvgColor": {"cssKey": "steps-error-icon-svg-color", "desc": "steps 错误步骤图标颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "steps 错误步骤图标颜色", "en": "Error icon color of Steps"}}, "stepsErrorIconSvgFontSize": {"cssKey": "steps-error-icon-svg-font-size", "desc": "steps 错误步骤图标大小", "override": "", "value": "8PX", "jsValue": "8PX", "staticValue": "8PX", "localeDesc": {"ch": "steps 错误步骤图标大小", "en": "Error icon size of Steps"}}, "stepsErrorTitleColor": {"cssKey": "steps-error-title-color", "desc": "steps 错误步骤标题颜色", "override": "", "value": "@danger-color", "jsValue": "@global@dangerColor", "staticValue": "#F53F3F", "localeDesc": {"ch": "steps 错误步骤标题颜色", "en": "Steps title color in error state"}}, "stepsFinishDotBorderColor": {"cssKey": "steps-finish-dot-border-color", "desc": "steps 迷你版完成步骤边框颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "steps 迷你版完成步骤边框颜色", "en": "Mini Steps border color in finish state"}}, "stepsFinishIconNumBackground": {"cssKey": "steps-finish-icon-num-background", "desc": "steps 无文字步骤背景色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "steps 无文字步骤背景色", "en": "Non-text steps background color in finish state"}}, "stepsFinishIconSvgColor": {"cssKey": "steps-finish-icon-svg-color", "desc": "steps 完成步骤图标颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "steps 完成步骤图标颜色", "en": "Finish icon color of Steps"}}, "stepsFinishTitleColor": {"cssKey": "steps-finish-title-color", "desc": "steps 完成步骤标题颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "steps 完成步骤标题颜色", "en": "Steps title color in finish state"}}, "stepsHorizontalContentMarginTop": {"cssKey": "steps-horizontal-content-margin-top", "desc": "steps 横向步骤条内容区上外边距", "override": "", "value": "~`pxtorem(5)`", "jsValue": "@getRem@5", "staticValue": "0.1rem", "localeDesc": {"ch": "steps 横向步骤条内容区上外边距", "en": "Horizontal Steps content top margin"}}, "stepsIconHeight": {"cssKey": "steps-icon-height", "desc": "steps 步骤图标高度", "override": "", "value": "18PX", "jsValue": "18PX", "staticValue": "18PX", "localeDesc": {"ch": "steps 步骤图标高度", "en": "Steps icon height"}}, "stepsIconNumColor": {"cssKey": "steps-icon-num-color", "desc": "steps 默认步骤字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "steps 默认步骤字体颜色", "en": "Steps font color"}}, "stepsIconNumFontSize": {"cssKey": "steps-icon-num-font-size", "desc": "steps 无文字步骤字体大小", "override": "", "value": "12PX", "jsValue": "12PX", "staticValue": "12PX", "localeDesc": {"ch": "steps 无文字步骤字体大小", "en": "Non-text steps font size"}}, "stepsIconNumLineHeight": {"cssKey": "steps-icon-num-line-height", "desc": "steps 无文字步骤字体行高", "override": "", "value": "18PX", "jsValue": "18PX", "staticValue": "18PX", "localeDesc": {"ch": "steps 无文字步骤字体行高", "en": "Non-text steps font line height"}}, "stepsIconSvgStandardFontSize": {"cssKey": "steps-icon-svg-standard-font-size", "desc": "steps 步骤默认图标大小", "override": "", "value": "10PX", "jsValue": "10PX", "staticValue": "10PX", "localeDesc": {"ch": "steps 步骤默认图标大小", "en": "Steps icon size"}}, "stepsIconWidth": {"cssKey": "steps-icon-width", "desc": "steps 步骤图标宽度", "override": "", "value": "18PX", "jsValue": "18PX", "staticValue": "18PX", "localeDesc": {"ch": "steps 步骤图标宽度", "en": "Steps icon width"}}, "stepsPadding": {"cssKey": "steps-padding", "desc": "steps 上下内边距", "override": "", "value": "~`pxtorem(16)` 0", "jsValue": "@getRem@16 0", "staticValue": "0.32rem 0", "localeDesc": {"ch": "steps 上下内边距", "en": "Steps vertical padding"}}, "stepsProcessDotBackground": {"cssKey": "steps-process-dot-background", "desc": "steps 迷你版进行中步骤背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "steps 迷你版进行中步骤背景色", "en": "Mini Steps background color in processing state"}}, "stepsProcessIconNumBackground": {"cssKey": "steps-process-icon-num-background", "desc": "steps 无文字进行中步骤背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "steps 无文字进行中步骤背景色", "en": "Non-text steps background color in processing state"}}, "stepsProcessIconNumColor": {"cssKey": "steps-process-icon-num-color", "desc": "steps 进行中步骤字体颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "steps 进行中步骤字体颜色", "en": "Steps font color in processing state"}}, "stepsProcessTitleColor": {"cssKey": "steps-process-title-color", "desc": "steps 进行中步骤标题颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "steps 进行中步骤标题颜色", "en": "Steps title color in processing state"}}, "stepsProcessWithConfigItemIconColor": {"cssKey": "steps-process-with-config-item-icon-color", "desc": "steps mixin 函数进行中步骤自定义图标颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "steps mixin 函数进行中步骤自定义图标颜色", "en": "Custom icon color of Steps in processing state"}}, "stepsTailBorderRadius": {"cssKey": "steps-tail-border-radius", "desc": "steps 分割线圆角值", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "steps 分割线圆角值", "en": "Border radius of steps dividing"}}, "stepsTailFinishBackground": {"cssKey": "steps-tail-finish-background", "desc": "steps 步骤分割线完成态颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "steps 步骤分割线完成态颜色", "en": "Steps dividing line color in finish state"}}, "stepsTailHorizontalGutter": {"cssKey": "steps-tail-horizontal-gutter", "desc": "steps 水平分割线与图标中心点的距离", "override": "", "value": "18PX", "jsValue": "18PX", "staticValue": "18PX", "localeDesc": {"ch": "steps 水平分割线与图标中心点的距离", "en": "The distance between the horizontal dividing line and the center point of the icon"}}, "stepsTailHorizontalLeft": {"cssKey": "steps-tail-horizontal-left", "desc": "steps 水平分割线的向右偏移距离，一般是 stepsIconHeight 值的一半", "override": "", "value": "9PX", "jsValue": "9PX", "staticValue": "9PX", "localeDesc": {"ch": "steps 水平分割线的向右偏移距离，一般是 stepsIconHeight 值的一半", "en": "The offset distance to the right of the horizontal dividing line of steps, generally half the value of stepsIconHeight"}}, "stepsTailHorizontalPadding": {"cssKey": "steps-tail-horizontal-padding", "desc": "steps 水平分割线左右间距", "override": "", "value": "0 @steps-tail-horizontal-gutter", "jsValue": "0 @global@stepsTailHorizontalGutter", "staticValue": "0 18PX", "localeDesc": {"ch": "steps 水平分割线左右间距", "en": "Left and right spacing of the horizontal dividing line of Steps"}}, "stepsTailStandardBackground": {"cssKey": "steps-tail-standard-background", "desc": "steps 步骤分割线默认颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "steps 步骤分割线默认颜色", "en": "Steps dividing line color"}}, "stepsTailStandardSize": {"cssKey": "steps-tail-standard-size", "desc": "steps 步骤分割线默认粗细", "override": "", "value": "1PX", "jsValue": "1PX", "staticValue": "1PX", "localeDesc": {"ch": "steps 步骤分割线默认粗细", "en": "Steps dividing line thickness"}}, "stepsTailVerticalGutter": {"cssKey": "steps-tail-vertical-gutter", "desc": "steps 垂直分割线与图标中心点的距离", "override": "", "value": "14PX", "jsValue": "14PX", "staticValue": "14PX", "localeDesc": {"ch": "steps 垂直分割线与图标中心点的距离", "en": "The distance between the vertical dividing line and the center point of the icon"}}, "stepsTailVerticalPadding": {"cssKey": "steps-tail-vertical-padding", "desc": "steps 垂直分割线上下间距", "override": "", "value": "@steps-tail-vertical-gutter 0", "jsValue": "@global@stepsTailVerticalGutter 0", "staticValue": "14PX 0", "localeDesc": {"ch": "steps 垂直分割线上下间距", "en": "Left and right spacing of the vertical dividing line of Steps"}}, "stepsTailVerticalTop": {"cssKey": "steps-tail-vertical-top", "desc": "steps 垂直分割线的向下偏移距离，一般是 stepsIconHeight 值的一半", "override": "", "value": "9PX", "jsValue": "9PX", "staticValue": "9PX", "localeDesc": {"ch": "steps 垂直分割线的向下偏移距离，一般是 stepsIconHeight 值的一半", "en": "The offset distance to the bottom of the vertical dividing line of steps, generally half the value of stepsIconHeight"}}, "stepsTitleFontSize": {"cssKey": "steps-title-font-size", "desc": "steps 标题文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "steps 标题文字大小", "en": "Steps title font size"}}, "stepsTitleLineHeight": {"cssKey": "steps-title-line-height", "desc": "steps 标题文字行高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "steps 标题文字行高", "en": "Steps title font line height"}}, "stepsVerticalContentMarginLeft": {"cssKey": "steps-vertical-content-margin-left", "desc": "steps 竖向步骤条内容左外边距", "override": "", "value": "~`px<PERSON>m(13)`", "jsValue": "@getRem@13", "staticValue": "0.26rem", "localeDesc": {"ch": "steps 竖向步骤条内容左外边距", "en": "Vertical Steps content left margin"}}, "stepsVerticalContentPaddingBottom": {"cssKey": "steps-vertical-content-padding-bottom", "desc": "steps 竖向步骤条内容下内边距", "override": "", "value": "~`pxtorem(25)`", "jsValue": "@getRem@25", "staticValue": "0.5rem", "localeDesc": {"ch": "steps 竖向步骤条内容下内边距", "en": "Vertical Steps content bottom padding"}}, "stepsVerticalPaddingBottom": {"cssKey": "steps-vertical-padding-bottom", "desc": "steps 竖向步骤条下内边距", "override": "", "value": "0", "jsValue": "0", "staticValue": "0", "localeDesc": {"ch": "steps 竖向步骤条下内边距", "en": "Vertical steps bottom padding"}}, "stepsVerticalPaddingLeft": {"cssKey": "steps-vertical-padding-left", "desc": "steps 竖向步骤条左内边距", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "steps 竖向步骤条左内边距", "en": "Vertical steps left padding"}}, "stepsWaitDescriptionColor": {"cssKey": "steps-wait-description-color", "desc": "steps 等待中步骤描述文字颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "steps 等待中步骤描述文字颜色", "en": "Steps description font color in waiting state"}}, "stepsWaitDotBorderColor": {"cssKey": "steps-wait-dot-border-color", "desc": "steps 迷你版等待中步骤边框颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "steps 迷你版等待中步骤边框颜色", "en": "Mini Steps border color in waiting state"}}, "stepsWaitIconNumBackground": {"cssKey": "steps-wait-icon-num-background", "desc": "steps 无文字等待中步骤背景色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "steps 无文字等待中步骤背景色", "en": "Non-text steps background color in waiting state"}}, "stepsWaitTitleColor": {"cssKey": "steps-wait-title-color", "desc": "steps 等待中步骤标题颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "steps 等待中步骤标题颜色", "en": "Steps title color in waiting state"}}, "subFontColor": {"cssKey": "sub-font-color", "desc": "副标题字体颜色", "override": "", "value": "#4e5969", "jsValue": "#4e5969", "staticValue": "#4e5969", "localeDesc": {"ch": "副标题字体颜色", "en": "Subtitle font color"}}, "subInfoFontColor": {"cssKey": "sub-info-font-color", "desc": "附加信息字体颜色", "override": "", "value": "#86909c", "jsValue": "#86909c", "staticValue": "#86909c", "localeDesc": {"ch": "附加信息字体颜色", "en": "Additional information font color"}}, "successColor": {"cssKey": "success-color", "desc": "基础成功态颜色", "override": "", "value": "#00B42A", "jsValue": "#00B42A", "staticValue": "#00B42A", "localeDesc": {"ch": "基础成功态颜色", "en": "Base success state color"}}, "swipeActionCloseTransition": {"cssKey": "swipe-action-close-transition", "desc": "SwipeAction 菜单关闭时的动画曲线", "override": "", "value": "cubic-bezier(0.34, 0.69, 0.1, 1)", "jsValue": "cubic-bezier(0.34, 0.69, 0.1, 1)", "staticValue": "cubic-bezier(0.34, 0.69, 0.1, 1)", "localeDesc": {"ch": "SwipeAction 菜单关闭时的动画曲线"}}, "swipeActionIconHeight": {"cssKey": "swipe-action-icon-height", "desc": "swipeAction Icon 高", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "swipeAction Icon 高"}}, "swipeActionIconMarginRight": {"cssKey": "swipe-action-icon-margin-right", "desc": "swipeAction Icon的右边距", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "swipeAction Icon的右边距"}}, "swipeActionIconWidth": {"cssKey": "swipe-action-icon-width", "desc": "swipeAction Icon 宽", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "swipeAction Icon 宽"}}, "swipeActionInfoBounceBuffer": {"cssKey": "swipe-action-info-bounce-buffer", "desc": "swipeAction 菜单弹性效果预留底部色块宽度", "override": "", "value": "~`pxtorem(30)`", "jsValue": "@getRem@30", "staticValue": "0.6rem", "localeDesc": {"ch": "swipeAction 菜单弹性效果预留底部色块宽度"}}, "swipeActionInfoPadding": {"cssKey": "swipe-action-info-padding", "desc": "SwipeAction 菜单的内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "SwipeAction 菜单的内边距"}}, "swipeActionOpenTransition": {"cssKey": "swipe-action-open-transition", "desc": "SwipeAction 菜单打开时的动画曲线", "override": "", "value": "cubic-bezier(0.2, 0.8, 0.32, 1.28)", "jsValue": "cubic-bezier(0.2, 0.8, 0.32, 1.28)", "staticValue": "cubic-bezier(0.2, 0.8, 0.32, 1.28)", "localeDesc": {"ch": "SwipeAction 菜单打开时的动画曲线"}}, "swipeActionTextColor": {"cssKey": "swipe-action-text-color", "desc": "SwipeAction 文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "SwipeAction 文字颜色"}}, "swipeActionTextFontSize": {"cssKey": "swipe-action-text-font-size", "desc": "SwipeAction 文字大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "SwipeAction 文字大小"}}, "swipeActionTextLineHeight": {"cssKey": "swipe-action-text-line-height", "desc": "SwipeAction 文字行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "SwipeAction 文字行高"}}, "swipeLoadLabelBackground": {"cssKey": "swipe-load-label-background", "desc": "swipeLoad 滑动区域的背景色", "override": "swipe-load-label-color", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "swipeLoad 滑动区域的背景色", "en": "Background color of SwipeLoad loading area"}}, "swipeLoadLabelBorderRadius": {"cssKey": "swipe-load-label-border-radius", "desc": "swipeLoad 滑动区域的边框半径", "override": "", "value": "50%", "jsValue": "50%", "staticValue": "50%", "localeDesc": {"ch": "swipeLoad 滑动区域的边框半径", "en": "Border radius of SwipeLoad loading area"}}, "swipeLoadLabelTextColor": {"cssKey": "swipe-load-label-text-color", "desc": "swipeLoad 加载提示区域的字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "swipeLoad 加载提示区域的字体颜色", "en": "Font color of SwipeLoad loading area"}}, "swipeLoadLabelTextFontSize": {"cssKey": "swipe-load-label-text-font-size", "desc": "swipeLoad 加载提示区域的字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "swipeLoad 加载提示区域的字体大小", "en": "Font size of SwipeLoad loading area"}}, "swipeLoadLabelTextMarginLeft": {"cssKey": "swipe-load-label-text-margin-left", "desc": "swipeLoad 加载提示区域的左外边距", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "swipeLoad 加载提示区域的左外边距", "en": "Left margin of SwipeLoad loading area"}}, "swipeLoadLabelTextWidth": {"cssKey": "swipe-load-label-text-width", "desc": "swipeLoad 加载提示区域的宽度", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "swipeLoad 加载提示区域的宽度", "en": "SwipeLoad loading area width"}}, "switchAndroidBackground": {"cssKey": "switch-android-background", "desc": "switch 安卓开关背景色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "switch 安卓开关背景色", "en": "Switch background color in Android"}}, "switchAndroidCheckedBackground": {"cssKey": "switch-android-checked-background", "desc": "switch 安卓开关打开时背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "switch 安卓开关打开时背景色", "en": "Background color when switch is on in Android"}}, "switchAndroidCheckedInnerTransform": {"cssKey": "switch-android-checked-inner-transform", "desc": "switch 安卓开关打开时横向偏移量", "override": "", "value": "translateX(16PX)", "jsValue": "translateX(16PX)", "staticValue": "translateX(16PX)", "localeDesc": {"ch": "switch 安卓开关打开时横向偏移量", "en": "The horizontal offset of switch button when the switch is on in Android"}}, "switchAndroidDisabledBackground": {"cssKey": "switch-android-disabled-background", "desc": "switch 安卓开关默认关闭禁用点击时背景色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "switch 安卓开关默认关闭禁用点击时背景色", "en": "Default background color of disable switch which is close in Android"}}, "switchAndroidDisabledCheckedBackground": {"cssKey": "switch-android-disabled-checked-background", "desc": "switch 安卓开关默认打开禁用点击时背景色", "override": "", "value": "@primary-disabled-color", "jsValue": "@global@primaryDisabledColor", "staticValue": "#94BFFF", "localeDesc": {"ch": "switch 安卓开关默认打开禁用点击时背景色", "en": "Background color of disable switch which is open in Android"}}, "switchAndroidFullyBorderRadius": {"cssKey": "switch-android-fully-border-radius", "desc": "switch 安卓圆角开关圆角大小", "override": "", "value": "20PX", "jsValue": "20PX", "staticValue": "20PX", "localeDesc": {"ch": "switch 安卓圆角开关圆角大小", "en": "Border radius of switch button in Android"}}, "switchAndroidHeight": {"cssKey": "switch-android-height", "desc": "switch 安卓开关整体高度", "override": "", "value": "24PX", "jsValue": "24PX", "staticValue": "24PX", "localeDesc": {"ch": "switch 安卓开关整体高度", "en": "Switch height in Android"}}, "switchAndroidInnerBoxShadow": {"cssKey": "switch-android-inner-box-shadow", "desc": "switch 安卓开关阴影", "override": "", "value": "0 2PX 4PX 0 rgba(0, 0, 0, 0.08)", "jsValue": "0 2PX 4PX 0 rgba(0, 0, 0, 0.08)", "staticValue": "0 2PX 4PX 0 rgba(0, 0, 0, 0.08)", "localeDesc": {"ch": "switch 安卓开关阴影", "en": "Switch button box shadow in Android"}}, "switchAndroidInnerDiameterSize": {"cssKey": "switch-android-inner-diameter-size", "desc": "switch 安卓开关直径", "override": "switch-android-inner-diameter", "value": "20PX", "jsValue": "20PX", "staticValue": "20PX", "localeDesc": {"ch": "switch 安卓开关直径", "en": "Switch button diameter in Android"}}, "switchAndroidPadding": {"cssKey": "switch-android-padding", "desc": "switch 安卓开关内边距", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "switch 安卓开关内边距", "en": "Switch padding in Android"}}, "switchAndroidSemiBorderRadius": {"cssKey": "switch-android-semi-border-radius", "desc": "switch 安卓直角开关圆角大小", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "switch 安卓直角开关圆角大小", "en": "Border radius of semi switch button in Android"}}, "switchAndroidTextFontSize": {"cssKey": "switch-android-text-font-size", "desc": "switch 安卓开关文字大小", "override": "", "value": "12PX", "jsValue": "12PX", "staticValue": "12PX", "localeDesc": {"ch": "switch 安卓开关文字大小", "en": "Switch font size in Android"}}, "switchAndroidTextGapSize": {"cssKey": "switch-android-text-gap-size", "desc": "switch 安卓开关文字左右间距", "override": "switch-android-text-gap", "value": "5PX", "jsValue": "5PX", "staticValue": "5PX", "localeDesc": {"ch": "switch 安卓开关文字左右间距", "en": "Left and right spacing of switch in Android"}}, "switchAndroidWidth": {"cssKey": "switch-android-width", "desc": "switch 安卓开关整体宽度", "override": "", "value": "40PX", "jsValue": "40PX", "staticValue": "40PX", "localeDesc": {"ch": "switch 安卓开关整体宽度", "en": "Switch width in Android"}}, "switchInnerBackground": {"cssKey": "switch-inner-background", "desc": "switch 开关背景色", "override": "", "value": "#FFFFFF", "jsValue": "#FFFFFF", "staticValue": "#FFFFFF", "localeDesc": {"ch": "switch 开关背景色", "en": "Switch background color"}}, "switchInnerFullyBorderRadius": {"cssKey": "switch-inner-fully-border-radius", "desc": "switch 圆角开关圆角大小", "override": "", "value": "50%", "jsValue": "50%", "staticValue": "50%", "localeDesc": {"ch": "switch 圆角开关圆角大小", "en": "Border radius of switch button"}}, "switchInnerSemiBorderRadius": {"cssKey": "switch-inner-semi-border-radius", "desc": "switch 直角开关圆角大小", "override": "", "value": "1PX", "jsValue": "1PX", "staticValue": "1PX", "localeDesc": {"ch": "switch 直角开关圆角大小", "en": "Border radius of Semi switch button"}}, "switchInnerTransition": {"cssKey": "switch-inner-transition", "desc": "switch 开关过渡动画", "override": "", "value": "all .2s", "jsValue": "all .2s", "staticValue": "all .2s", "localeDesc": {"ch": "switch 开关过渡动画", "en": "Switch transition animation"}}, "switchIosBackground": {"cssKey": "switch-ios-background", "desc": "switch iOS 开关背景色", "override": "", "value": "rgba(17, 17, 17, .15)", "jsValue": "rgba(17, 17, 17, .15)", "staticValue": "rgba(17, 17, 17, .15)", "localeDesc": {"ch": "switch iOS 开关背景色", "en": "Switch background color in iOS"}}, "switchIosCheckedBackground": {"cssKey": "switch-ios-checked-background", "desc": "switch iOS 开关打开时背景色", "override": "", "value": "#34C759", "jsValue": "#34C759", "staticValue": "#34C759", "localeDesc": {"ch": "switch iOS 开关打开时背景色", "en": "Background color when switch is on in iOS"}}, "switchIosCheckedInnerTransform": {"cssKey": "switch-ios-checked-inner-transform", "desc": "switch iOS 开关打开时横向偏移量", "override": "", "value": "translateX(~`pxtorem(20)`)", "jsValue": "translateX(@getRem@20)", "staticValue": "translateX(0.4rem)", "localeDesc": {"ch": "switch iOS 开关打开时横向偏移量", "en": "The horizontal offset of switch button when the switch is on in iOS"}}, "switchIosDisabledBackground": {"cssKey": "switch-ios-disabled-background", "desc": "switch iOS 开关默认关闭禁用点击时背景色", "override": "", "value": "rgba(120, 120, 128, .16)", "jsValue": "rgba(120, 120, 128, .16)", "staticValue": "rgba(120, 120, 128, .16)", "localeDesc": {"ch": "switch iOS 开关默认关闭禁用点击时背景色", "en": "Background color of disable switch which is close in iOS"}}, "switchIosDisabledCheckedBackground": {"cssKey": "switch-ios-disabled-checked-background", "desc": "switch iOS 开关默认打开禁用点击时背景色", "override": "", "value": "#4DD865", "jsValue": "#4DD865", "staticValue": "#4DD865", "localeDesc": {"ch": "switch iOS 开关默认打开禁用点击时背景色", "en": "Background color of disable switch which is open in iOS"}}, "switchIosDisabledCheckedOpacity": {"cssKey": "switch-ios-disabled-checked-opacity", "desc": "switch iOS 开关默认打开禁用点击时透明度", "override": "", "value": "0.3", "jsValue": "0.3", "staticValue": "0.3", "localeDesc": {"ch": "switch iOS 开关默认打开禁用点击时透明度", "en": "Opacity of disable switch which is open in iOS"}}, "switchIosFullyBorderRadius": {"cssKey": "switch-ios-fully-border-radius", "desc": "switch iOS 圆角开关圆角大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "switch iOS 圆角开关圆角大小", "en": "Border radius of switch button in iOS"}}, "switchIosHeight": {"cssKey": "switch-ios-height", "desc": "switch iOS 开关整体高度", "override": "", "value": "~`px<PERSON>m(31)`", "jsValue": "@getRem@31", "staticValue": "0.62rem", "localeDesc": {"ch": "switch iOS 开关整体高度", "en": "Switch height in iOS"}}, "switchIosInnerBorderColor": {"cssKey": "switch-ios-inner-border-color", "desc": "switch iOS 开关边框颜色", "override": "", "value": "rgba(0, 0, 0, .04)", "jsValue": "rgba(0, 0, 0, .04)", "staticValue": "rgba(0, 0, 0, .04)", "localeDesc": {"ch": "switch iOS 开关边框颜色", "en": "Border color of switch button in iOS"}}, "switchIosInnerBoxShadow": {"cssKey": "switch-ios-inner-box-shadow", "desc": "switch iOS 开关阴影", "override": "", "value": "0 3PX 2PX 0 rgba(0, 0, 0, .12)", "jsValue": "0 3PX 2PX 0 rgba(0, 0, 0, .12)", "staticValue": "0 3PX 2PX 0 rgba(0, 0, 0, .12)", "localeDesc": {"ch": "switch iOS 开关阴影", "en": "Switch button box shadow in iOS"}}, "switchIosInnerDiameterSize": {"cssKey": "switch-ios-inner-diameter-size", "desc": "switch iOS 开关直径", "override": "switch-ios-inner-diameter", "value": "~`px<PERSON><PERSON>(27)`", "jsValue": "@getRem@27", "staticValue": "0.54rem", "localeDesc": {"ch": "switch iOS 开关直径", "en": "Switch button diameter in iOS"}}, "switchIosPadding": {"cssKey": "switch-ios-padding", "desc": "switch iOS 开关内边距", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "switch iOS 开关内边距", "en": "Switch padding in iOS"}}, "switchIosSemiBorderRadius": {"cssKey": "switch-ios-semi-border-radius", "desc": "switch iOS 直角开关圆角大小", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "switch iOS 直角开关圆角大小", "en": "Border radius of semi switch button in iOS"}}, "switchIosTextFontSize": {"cssKey": "switch-ios-text-font-size", "desc": "switch iOS 开关文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "switch iOS 开关文字大小", "en": "Switch font size in iOS"}}, "switchIosTextGapSize": {"cssKey": "switch-ios-text-gap-size", "desc": "switch iOS 开关文字左右间距", "override": "switch-ios-text-gap", "value": "~`pxtorem(6)`", "jsValue": "@getRem@6", "staticValue": "0.12rem", "localeDesc": {"ch": "switch iOS 开关文字左右间距", "en": "Left and right spacing of switch in iOS"}}, "switchIosWidth": {"cssKey": "switch-ios-width", "desc": "switch iOS 开关整体宽度", "override": "", "value": "~`px<PERSON><PERSON>(51)`", "jsValue": "@getRem@51", "staticValue": "1.02rem", "localeDesc": {"ch": "switch iOS 开关整体宽度", "en": "Switch width in iOS"}}, "switchTextCheckedColor": {"cssKey": "switch-text-checked-color", "desc": "switch 打开时文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "switch 打开时文字颜色", "en": "Font color when switch is on"}}, "switchTextColor": {"cssKey": "switch-text-color", "desc": "switch 文字颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "switch 文字颜色", "en": "Switch font color"}}, "tabBarActiveColor": {"cssKey": "tab-bar-active-color", "desc": "tabBar 激活状态下的颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tabBar 激活状态下的颜色", "en": "TabBar active color"}}, "tabBarColor": {"cssKey": "tab-bar-color", "desc": "tabBar 默认颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "tabBar 默认颜色", "en": "TabBar default color"}}, "tabBarFontSize": {"cssKey": "tab-bar-font-size", "desc": "tabBar 默认字体大小", "override": "", "value": "~`pxtorem(10)`", "jsValue": "@getRem@10", "staticValue": "0.2rem", "localeDesc": {"ch": "tabBar 默认字体大小", "en": "TabBar default font size"}}, "tabBarHeight": {"cssKey": "tab-bar-height", "desc": "tabBar 高度", "override": "", "value": "~`pxtorem(50)`", "jsValue": "@getRem@50", "staticValue": "1rem", "localeDesc": {"ch": "tabBar 高度", "en": "TabBar height"}}, "tabBarIconSize": {"cssKey": "tab-bar-icon-size", "desc": "tabBar icon的默认大小", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "tabBar icon的默认大小", "en": "TabBar default icon size"}}, "tabBarItemIconMargin": {"cssKey": "tab-bar-item-icon-margin", "desc": "tabBar 子选项图标外边距", "override": "", "value": "~`pxtorem(7)` 0 ~`pxtorem(4)`", "jsValue": "@getRem@7 0 @getRem@4", "staticValue": "0.14rem 0 0.08rem", "localeDesc": {"ch": "tabBar 子选项图标外边距", "en": "TabBar items' icon margin"}}, "tabBarOnlyTitleFontSize": {"cssKey": "tab-bar-only-title-font-size", "desc": "tabBar 只有title时的字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "tabBar 只有title时的字体大小", "en": "TabBar font size when only title"}}, "tabBarOnlyTitleLineHeight": {"cssKey": "tab-bar-only-title-line-height", "desc": "tabBar 只有title时的行高", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "tabBar 只有title时的行高", "en": "TabBar line height when only title"}}, "tabBarTitleLineHeight": {"cssKey": "tab-bar-title-line-height", "desc": "tabBar 标题行高", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "tabBar 标题行高", "en": "TabBar title line height"}}, "tabBarTitleMargin": {"cssKey": "tab-bar-title-margin", "desc": "tabBar 标题外边距", "override": "", "value": "0 0 ~`pxtorem(5)` 0", "jsValue": "0 0 @getRem@5 0", "staticValue": "0 0 0.1rem 0", "localeDesc": {"ch": "tabBar 标题外边距", "en": "TabBar title margin"}}, "tabsTabBarBackground": {"cssKey": "tabs-tab-bar-background", "desc": "tabs tabbar 背景色", "override": "tab-bar-background", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "tabs tabbar 背景色", "en": "Tabs tabbar background color"}}, "tabsTabBarCardBorderRadius": {"cssKey": "tabs-tab-bar-card-border-radius", "desc": "tabs 分段器样式 tabbar 容器圆角值", "override": "tab-bar-card-border-radius", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "tabs 分段器样式 tabbar 容器圆角值", "en": "Border radius of tab container in card tabs"}}, "tabsTabBarCardColor": {"cssKey": "tabs-tab-bar-card-color", "desc": "tabs 分段器样式下的主颜色（边框色、高亮块背景色、非高亮块文字颜色）", "override": "tab-bar-card-color", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tabs 分段器样式下的主颜色（边框色、高亮块背景色、非高亮块文字颜色）", "en": "Primary color of tab in card tabs (border color, active block background color, inactive block font color)"}}, "tabsTabBarCardHeight": {"cssKey": "tabs-tab-bar-card-height", "desc": "tabs 分段器样式下 tabbar 容器高度", "override": "tab-bar-card-height", "value": "~`pxtorem(40)`", "jsValue": "@getRem@40", "staticValue": "0.8rem", "localeDesc": {"ch": "tabs 分段器样式下 tabbar 容器高度", "en": "Height of tabbar container of card tabs"}}, "tabsTabBarCardTextColor": {"cssKey": "tabs-tab-bar-card-text-color", "desc": "tabs 分段器样式下高亮选项卡的文字颜色", "override": "tab-bar-card-text-color", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "tabs 分段器样式下高亮选项卡的文字颜色", "en": "Font color of active tab in card tabs"}}, "tabsTabBarFontSize": {"cssKey": "tabs-tab-bar-font-size", "desc": "tabs tabbar 字体大小", "override": "", "value": "~`pxtorem(15)`", "jsValue": "@getRem@15", "staticValue": "0.3rem", "localeDesc": {"ch": "tabs tabbar 字体大小", "en": "Tabs tabbar font size"}}, "tabsTabBarHeight": {"cssKey": "tabs-tab-bar-height", "desc": "tabs 上下排布时 tabbar 容器高度", "override": "tab-bar-height", "value": "~`px<PERSON><PERSON>(42)`", "jsValue": "@getRem@42", "staticValue": "0.84rem", "localeDesc": {"ch": "tabs 上下排布时 tabbar 容器高度", "en": "Height of the tabbar container when the tabs is vertical"}}, "tabsTabBarHorizontalHeight": {"cssKey": "tabs-tab-bar-horizontal-height", "desc": "tabs 左右排布时每一个选项卡的高度", "override": "tab-bar-horizontal-height", "value": "~`px<PERSON><PERSON>(54)`", "jsValue": "@getRem@54", "staticValue": "1.08rem", "localeDesc": {"ch": "tabs 左右排布时每一个选项卡的高度", "en": "Each tab height in horizontal tabs"}}, "tabsTabBarLineActiveColor": {"cssKey": "tabs-tab-bar-line-active-color", "desc": "tabs 基础样式下高亮选项卡的文字颜色", "override": "tab-bar-line-active-color", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tabs 基础样式下高亮选项卡的文字颜色", "en": "Font color of active tab"}}, "tabsTabBarLineGutter": {"cssKey": "tabs-tab-bar-line-gutter", "desc": "tabs 基础非等分样式下选项卡的间距", "override": "tab-bar-line-gutter", "value": "~`pxtorem(40)`", "jsValue": "@getRem@40", "staticValue": "0.8rem", "localeDesc": {"ch": "tabs 基础非等分样式下选项卡的间距", "en": "Tab gutter of tabs under basic non-equal division style"}}, "tabsTabBarTagActiveBackground": {"cssKey": "tabs-tab-bar-tag-active-background", "desc": "tabs 标签样式下高亮选项卡的背景色", "override": "tab-bar-tag-active-background", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tabs 标签样式下高亮选项卡的背景色", "en": "Background color of active tab in tag tabs"}}, "tabsTabBarTagActiveTextColor": {"cssKey": "tabs-tab-bar-tag-active-text-color", "desc": "tabs 标签样式下高亮选项卡的文字颜色", "override": "tab-bar-tag-active-text-color", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "tabs 标签样式下高亮选项卡的文字颜色", "en": "Font color of active tab in tag tabs"}}, "tabsTabBarTagBackground": {"cssKey": "tabs-tab-bar-tag-background", "desc": "tabs 标签样式下每个选项卡的背景色", "override": "tab-bar-tag-background", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "tabs 标签样式下每个选项卡的背景色", "en": "Background color of each tab in tag tabs"}}, "tabsTabBarTagGutter": {"cssKey": "tabs-tab-bar-tag-gutter", "desc": "tabs 标签非等分样式下选项卡的间距", "override": "tab-bar-tag-gutter", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "tabs 标签非等分样式下选项卡的间距", "en": "Tab gutter of tag tabs under basic non-equal division style"}}, "tabsTabBarTagHeight": {"cssKey": "tabs-tab-bar-tag-height", "desc": "tabs 标签样式下 tabbar 容器高度", "override": "tab-bar-tag-height", "value": "~`pxtorem(60)`", "jsValue": "@getRem@60", "staticValue": "1.2rem", "localeDesc": {"ch": "tabs 标签样式下 tabbar 容器高度", "en": "Tabbar container height in tag tabs"}}, "tabsTabBarTagPadding": {"cssKey": "tabs-tab-bar-tag-padding", "desc": "tabs 标签样式下每个选项卡的内边距", "override": "", "value": "0 ~`px<PERSON><PERSON>(16)`", "jsValue": "0 @getRem@16", "staticValue": "0 0.32rem", "localeDesc": {"ch": "tabs 标签样式下每个选项卡的内边距", "en": "Padding of active tab in tag tabs"}}, "tabsTabBarTagTextColor": {"cssKey": "tabs-tab-bar-tag-text-color", "desc": "tabs 标签样式下每个选项卡的文字颜色", "override": "tab-bar-tag-text-color", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "tabs 标签样式下每个选项卡的文字颜色", "en": "Font color of each tab in tag tabs"}}, "tabsTabBarTagVerticalPadding": {"cssKey": "tabs-tab-bar-tag-vertical-padding", "desc": "tabs 标签样式下，选项卡与容器的垂直间距", "override": "tab-bar-tag-vertical-padding", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "tabs 标签样式下，选项卡与容器的垂直间距", "en": "Vertical padding between the tabbar and the container in tag tabs"}}, "tabsTabBarWidth": {"cssKey": "tabs-tab-bar-width", "desc": "tabs 左右排布时 tabbar 容器宽度", "override": "tab-bar-width", "value": "~`px<PERSON><PERSON>(78)`", "jsValue": "@getRem@78", "staticValue": "1.56rem", "localeDesc": {"ch": "tabs 左右排布时 tabbar 容器宽度", "en": "Height of the tabbar container when the tabs is horizontal"}}, "tabsUnderlineBorderRadius": {"cssKey": "tabs-underline-border-radius", "desc": "tabs 基础样式高亮下划线的圆角值", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "tabs 基础样式高亮下划线的圆角值", "en": "Border radius of active tab in normal tabs"}}, "tabsUnderlineColor": {"cssKey": "tabs-underline-color", "desc": "tabs 基础样式高亮下划线的颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tabs 基础样式高亮下划线的颜色", "en": "Underline color of active tab in normal tabs"}}, "tabsUnderlineSize": {"cssKey": "tabs-underline-size", "desc": "tabs 基础样式高亮下划线的长度", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "tabs 基础样式高亮下划线的长度", "en": "Length of active tab in normal tabs"}}, "tabsUnderlineThick": {"cssKey": "tabs-underline-thick", "desc": "tabs 基础样式高亮下划线的厚度", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "tabs 基础样式高亮下划线的厚度", "en": "Thickness of active tab in normal tabs"}}, "tagBorderRadius": {"cssKey": "tag-border-radius", "desc": "tag 圆角大小", "override": "", "value": "2PX", "jsValue": "2PX", "staticValue": "2PX", "localeDesc": {"ch": "tag 圆角大小", "en": "Tag border radius"}}, "tagFilletedPadding": {"cssKey": "tag-filleted-padding", "desc": "tag 圆角标签内边距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "tag 圆角标签内边距", "en": "Filleted tag padding"}}, "tagFontSize": {"cssKey": "tag-font-size", "desc": "tag 文字大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "tag 文字大小", "en": "Tag text size"}}, "tagHollowBorderColor": {"cssKey": "tag-hollow-border-color", "desc": "tag 空心标签边框颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tag 空心标签边框颜色", "en": "Hollow tag border color"}}, "tagHollowColor": {"cssKey": "tag-hollow-color", "desc": "tag 空心标签字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tag 空心标签字体颜色", "en": "Hollow tag font color"}}, "tagIconCloseMarginLeft": {"cssKey": "tag-icon-close-margin-left", "desc": "tag 内部关闭图标左外边距", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "tag 内部关闭图标左外边距", "en": "Tag close icon margin left"}}, "tagIconFontSize": {"cssKey": "tag-icon-font-size", "desc": "tag 内部图标大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "tag 内部图标大小", "en": "Tag icon size"}}, "tagIconMarginRight": {"cssKey": "tag-icon-margin-right", "desc": "tag 内部图标右外边距", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "tag 内部图标右外边距", "en": "Tag icon margin right"}}, "tagLargeSizeHeight": {"cssKey": "tag-large-size-height", "desc": "tag 大尺寸高度", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "tag 大尺寸高度", "en": "Tag height in large size"}}, "tagLargeSizePadding": {"cssKey": "tag-large-size-padding", "desc": "tag 大尺寸内边距", "override": "", "value": "~`pxtorem(6)`", "jsValue": "@getRem@6", "staticValue": "0.12rem", "localeDesc": {"ch": "tag 大尺寸内边距", "en": "Tag padding in large size"}}, "tagListAddBackground": {"cssKey": "tag-list-add-background", "desc": "tag 标签列表添加标签按钮背景色", "override": "", "value": "@lighter-line-color", "jsValue": "@global@lighterLineColor", "staticValue": "#f2f3f5", "localeDesc": {"ch": "tag 标签列表添加标签按钮背景色", "en": "Add button background color of tag list"}}, "tagListAddBorderColor": {"cssKey": "tag-list-add-border-color", "desc": "tag 标签列表添加标签按钮边框颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "tag 标签列表添加标签按钮边框颜色", "en": "Add button border color of tag list"}}, "tagListAddColor": {"cssKey": "tag-list-add-color", "desc": "tag 标签列表添加标签按钮文字颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "tag 标签列表添加标签按钮文字颜色", "en": "Add button font color of tag list"}}, "tagListHorizontalGutter": {"cssKey": "tag-list-horizontal-gutter", "desc": "tag 标签列表水平间距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "tag 标签列表水平间距", "en": "Tag list horizontal gutter"}}, "tagListVerticalGutter": {"cssKey": "tag-list-vertical-gutter", "desc": "tag 标签列表垂直间距", "override": "", "value": "0", "jsValue": "0", "staticValue": "0", "localeDesc": {"ch": "tag 标签列表垂直间距", "en": "Tag list vertical gutter"}}, "tagMediumSizeHeight": {"cssKey": "tag-medium-size-height", "desc": "tag 中尺寸高度", "override": "", "value": "~`pxtorem(20)`", "jsValue": "@getRem@20", "staticValue": "0.4rem", "localeDesc": {"ch": "tag 中尺寸高度", "en": "Tag height in medium size"}}, "tagMediumSizePadding": {"cssKey": "tag-medium-size-padding", "desc": "tag 中尺寸内边距", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "tag 中尺寸内边距", "en": "Tag padding in medium size"}}, "tagPrimaryBackgroundColor": {"cssKey": "tag-primary-background-color", "desc": "tag 基础背景色", "override": "", "value": "@lighter-primary-color", "jsValue": "@global@lighterPrimaryColor", "staticValue": "#E8F3FF", "localeDesc": {"ch": "tag 基础背景色", "en": "Tag primary background color"}}, "tagPrimaryBorderColor": {"cssKey": "tag-primary-border-color", "desc": "tag 基础边框色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tag 基础边框色", "en": "Tag primary border color"}}, "tagPrimaryColor": {"cssKey": "tag-primary-color", "desc": "tag 基础字体颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tag 基础字体颜色", "en": "Tag primary color"}}, "tagSmallSizeHeight": {"cssKey": "tag-small-size-height", "desc": "tag 小尺寸高度", "override": "", "value": "~`pxtorem(18)`", "jsValue": "@getRem@18", "staticValue": "0.36rem", "localeDesc": {"ch": "tag 小尺寸高度", "en": "Tag height in small size"}}, "tagSmallSizePadding": {"cssKey": "tag-small-size-padding", "desc": "tag 小尺寸内边距", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "tag 小尺寸内边距", "en": "Tag padding in small size"}}, "tagSolidBackgroundColor": {"cssKey": "tag-solid-background-color", "desc": "tag 实心标签背景色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tag 实心标签背景色", "en": "Solid tag background color"}}, "tagSolidBorderColor": {"cssKey": "tag-solid-border-color", "desc": "tag 实心标签边框颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "tag 实心标签边框颜色", "en": "Solid tag border color"}}, "tagSolidColor": {"cssKey": "tag-solid-color", "desc": "tag 实心标签字体颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "tag 实心标签字体颜色", "en": "Solid tag font color"}}, "textareaFontSize": {"cssKey": "textarea-font-size", "desc": "多行文本框 字号", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "多行文本框 字号", "en": "Textarea font size"}}, "textareaHasStatPadding": {"cssKey": "textarea-has-stat-padding", "desc": "多行文本框 有字数统计时的内边距", "override": "", "value": "~`pxtorem(16)` ~`pxtorem(16)` ~`pxtorem(44)`", "jsValue": "@getRem@16 @getRem@16 @getRem@44", "staticValue": "0.32rem 0.32rem 0.88rem", "localeDesc": {"ch": "多行文本框 有字数统计时的内边距", "en": "Textarea  padding of textarea with word count"}}, "textareaLineHeight": {"cssKey": "textarea-line-height", "desc": "多行文本框 行间距", "override": "", "value": "~`px<PERSON>m(22)`", "jsValue": "@getRem@22", "staticValue": "0.44rem", "localeDesc": {"ch": "多行文本框 行间距", "en": "Textarea line height"}}, "textareaPadding": {"cssKey": "textarea-padding", "desc": "多行文本框 内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "多行文本框 内边距", "en": "Textarea padding"}}, "textareaStatisticColor": {"cssKey": "textarea-statistic-color", "desc": "多行文本框 字数统计文字颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "多行文本框 字数统计文字颜色", "en": "Textarea word count font color"}}, "textareaStatisticFontSize": {"cssKey": "textarea-statistic-font-size", "desc": "多行文本框 字数统计字号", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "多行文本框 字数统计字号", "en": "Textareaword count font size"}}, "timeLineAxisColor": {"cssKey": "time-line-axis-color", "desc": "时间轴轴线的颜色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "时间轴轴线的颜色", "en": "Color of the Axis of TimeLine"}}, "timeLineAxisWidth": {"cssKey": "time-line-axis-width", "desc": "时间轴轴线的宽度", "override": "", "value": "~`pxtorem(1)`", "jsValue": "@getRem@1", "staticValue": "0.02rem", "localeDesc": {"ch": "时间轴轴线的宽度", "en": "Width of the Axis of TimeLine"}}, "timeLineContentBackgroundColor": {"cssKey": "time-line-content-background-color", "desc": "时间轴content的背景色", "override": "", "value": "@line-color", "jsValue": "@global@lineColor", "staticValue": "#e5e6eb", "localeDesc": {"ch": "时间轴content的背景色", "en": "Background color of the Content of TimeLine"}}, "timeLineContentBorderRadius": {"cssKey": "time-line-content-border-radius", "desc": "时间轴content的圆角值", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "时间轴content的圆角值", "en": "Border radius of the Content of TimeLine"}}, "timeLineContentColor": {"cssKey": "time-line-content-color", "desc": "时间轴content的字体颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "时间轴content的字体颜色", "en": "Font color of the Content of TimeLine"}}, "timeLineContentFontSize": {"cssKey": "time-line-content-font-size", "desc": "时间轴content的字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "时间轴content的字体大小", "en": "Font size of the content of TimeLine"}}, "timeLineContentMarginBottom": {"cssKey": "time-line-content-margin-bottom", "desc": "时间轴content的下外边距（13 + 12 / 2）", "override": "", "value": "~`px<PERSON><PERSON>(19)`", "jsValue": "@getRem@19", "staticValue": "0.38rem", "localeDesc": {"ch": "时间轴content的下外边距（13 + 12 / 2）", "en": "Bottom margin of the Content of TimeLine"}}, "timeLineContentMarginLeft": {"cssKey": "time-line-content-margin-left", "desc": "时间轴content的左外边距", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "时间轴content的左外边距", "en": "Left margin of the Content of TimeLine"}}, "timeLineContentMarginTop": {"cssKey": "time-line-content-margin-top", "desc": "时间轴content的上外边距（13 - 12 / 2）", "override": "", "value": "~`pxtorem(7)`", "jsValue": "@getRem@7", "staticValue": "0.14rem", "localeDesc": {"ch": "时间轴content的上外边距（13 - 12 / 2）", "en": "Top margin of the Content of TimeLine"}}, "timeLineDotBackgroundColor": {"cssKey": "time-line-dot-background-color", "desc": "时间轴节点的背景色", "override": "", "value": "@background-color", "jsValue": "@global@backgroundColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "时间轴节点的背景色", "en": "Background color of the dot of TimeLine"}}, "timeLineDotBorderColor": {"cssKey": "time-line-dot-border-color", "desc": "时间轴节点的线颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "时间轴节点的线颜色", "en": "Border color of the dot of TimeLine"}}, "timeLineDotWidth": {"cssKey": "time-line-dot-width", "desc": "时间轴节点的宽度", "override": "", "value": "~`pxtorem(9)`", "jsValue": "@getRem@9", "staticValue": "0.18rem", "localeDesc": {"ch": "时间轴节点的宽度", "en": "Width of the dot of TimeLine"}}, "timeLineLabelColor": {"cssKey": "time-line-label-color", "desc": "时间轴label的字体颜色", "override": "", "value": "@sub-info-font-color", "jsValue": "@global@subInfoFontColor", "staticValue": "#86909c", "localeDesc": {"ch": "时间轴label的字体颜色", "en": "Font color of the label of TimeLine"}}, "timeLineLabelFontSize": {"cssKey": "time-line-label-font-size", "desc": "时间轴label的字体大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "时间轴label的字体大小", "en": "Font size of the label of TimeLine"}}, "toastBackground": {"cssKey": "toast-background", "desc": "toast 背景色", "override": "", "value": "rgba(0, 0, 0, 0.8)", "jsValue": "rgba(0, 0, 0, 0.8)", "staticValue": "rgba(0, 0, 0, 0.8)", "localeDesc": {"ch": "toast 背景色", "en": "Toast background color"}}, "toastBorderRadius": {"cssKey": "toast-border-radius", "desc": "toast 圆角值", "override": "", "value": "~`pxtorem(4)`", "jsValue": "@getRem@4", "staticValue": "0.08rem", "localeDesc": {"ch": "toast 圆角值", "en": "Toast border radius"}}, "toastFontSize": {"cssKey": "toast-font-size", "desc": "toast 字体大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "toast 字体大小", "en": "Toast font size"}}, "toastFromBottomPosition": {"cssKey": "toast-from-bottom-position", "desc": "toast direction=bottom 时距离底部的位置", "override": "", "value": "30%", "jsValue": "30%", "staticValue": "30%", "localeDesc": {"ch": "toast direction=bottom 时距离底部的位置", "en": "Toast position from the bottom when toast direction=bottom"}}, "toastFromTopPosition": {"cssKey": "toast-from-top-position", "desc": "toast direction=top 时距离顶部的位置", "override": "", "value": "30%", "jsValue": "30%", "staticValue": "30%", "localeDesc": {"ch": "toast direction=top 时距离顶部的位置", "en": "Toast position from the top when toast direction=top"}}, "toastHorizontalIconContentGutter": {"cssKey": "toast-horizontal-icon-content-gutter", "desc": "toast 内容横向排列时文字与图标的距离", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "toast 内容横向排列时文字与图标的距离", "en": "Distance between the text and the icon when the toast content is horizontally aligned"}}, "toastHorizontalIconSize": {"cssKey": "toast-horizontal-icon-size", "desc": "toast 内容横向排列时的图标大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "toast 内容横向排列时的图标大小", "en": "Icon size when toast content is horizontally aligned"}}, "toastHorizontalPadding": {"cssKey": "toast-horizontal-padding", "desc": "toast 内容横向排列时的内边距", "override": "", "value": "~`pxtorem(8)` ~`pxtorem(16)`", "jsValue": "@getRem@8 @getRem@16", "staticValue": "0.16rem 0.32rem", "localeDesc": {"ch": "toast 内容横向排列时的内边距", "en": "Padding when toast content is horizontally aligned"}}, "toastLineHeight": {"cssKey": "toast-line-height", "desc": "toast 字体行高", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "toast 字体行高", "en": "toast font line height"}}, "toastLoadingArcBackgroundColor": {"cssKey": "toast-loading-arc-background-color", "desc": "toast 加载态默认arc样式下底圈的背景色", "override": "", "value": "#666666", "jsValue": "#666666", "staticValue": "#666666", "localeDesc": {"ch": "toast 加载态默认arc样式下底圈的背景色", "en": "Background color of arc of toast in loading state"}}, "toastLoadingInnerFontSize": {"cssKey": "toast-loading-inner-font-size", "desc": "toast 加载态，处于加载图标内部的文字大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "toast 加载态，处于加载图标内部的文字大小", "en": "Font size inside the loading icon of toast in loading state"}}, "toastSafePadding": {"cssKey": "toast-safe-padding", "desc": "toast 距屏幕边缘的安全距离", "override": "", "value": "0 ~`px<PERSON><PERSON>(16)`", "jsValue": "0 @getRem@16", "staticValue": "0 0.32rem", "localeDesc": {"ch": "toast 距屏幕边缘的安全距离", "en": "Safe distance of toast from the edge of the screen"}}, "toastTextColor": {"cssKey": "toast-text-color", "desc": "toast 文字颜色", "override": "", "value": "@mask-content-color", "jsValue": "@global@maskContentColor", "staticValue": "#FFFFFF", "localeDesc": {"ch": "toast 文字颜色", "en": "Toast text color"}}, "toastVerticalIconContentGutter": {"cssKey": "toast-vertical-icon-content-gutter", "desc": "toast 内容纵向排列时文字与图标的距离", "override": "", "value": "~`pxtorem(8)`", "jsValue": "@getRem@8", "staticValue": "0.16rem", "localeDesc": {"ch": "toast 内容纵向排列时文字与图标的距离", "en": "Distance between the text and the icon when the toast content is vertically aligned"}}, "toastVerticalIconSize": {"cssKey": "toast-vertical-icon-size", "desc": "toast 内容纵向排列时的图标大小", "override": "", "value": "~`pxtorem(24)`", "jsValue": "@getRem@24", "staticValue": "0.48rem", "localeDesc": {"ch": "toast 内容纵向排列时的图标大小", "en": "Icon size when toast content is vertically aligned"}}, "toastVerticalPadding": {"cssKey": "toast-vertical-padding", "desc": "toast 内容纵向排列时的内边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "toast 内容纵向排列时的内边距", "en": "Padding when toast content is vertically aligned"}}, "transitionFadeDuration": {"cssKey": "transition-fade-duration", "desc": "渐隐过渡效果持续时长", "override": "", "value": "300ms", "jsValue": "300ms", "staticValue": "300ms", "localeDesc": {"ch": "渐隐过渡效果持续时长", "en": "Transition fade duration"}}, "uploaderDeleteIconColor": {"cssKey": "uploader-delete-icon-color", "desc": "文件上传组件上传列表删除图标颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "文件上传组件上传列表删除图标颜色", "en": "Color of the delete icon in the upload list"}}, "uploaderDeleteIconFontSize": {"cssKey": "uploader-delete-icon-font-size", "desc": "文件上传组件上传列表删除图标大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "文件上传组件上传列表删除图标大小", "en": "Font size of the delete icon in the upload list"}}, "uploaderDeleteIconPaddingLeft": {"cssKey": "uploader-delete-icon-padding-left", "desc": "文件上传组件上传列表删除图标左内边距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "文件上传组件上传列表删除图标左内边距", "en": "Left padding of the delete icon in the upload list"}}, "uploaderDisabledDeleteIconColor": {"cssKey": "uploader-disabled-delete-icon-color", "desc": "文件上传组件禁用状态删除图标颜色", "override": "", "value": "@disabled-color", "jsValue": "@global@disabledColor", "staticValue": "#c9cdd4", "localeDesc": {"ch": "文件上传组件禁用状态删除图标颜色", "en": "Disabled color of the delete icon in the upload list"}}, "uploaderErrorTextColor": {"cssKey": "uploader-error-text-color", "desc": "文件上传组件上传列表错误文字颜色", "override": "", "value": "@primary-color", "jsValue": "@global@primaryColor", "staticValue": "#165DFF", "localeDesc": {"ch": "文件上传组件上传列表错误文字颜色", "en": "Error color of the text in the uploader list"}}, "uploaderErrorTextFontSize": {"cssKey": "uploader-error-text-font-size", "desc": "文件上传组件上传列表错误文字大小", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "文件上传组件上传列表错误文字大小", "en": "Error font size of the text in the uploader list"}}, "uploaderFileIconColor": {"cssKey": "uploader-file-icon-color", "desc": "文件上传组件上传列表文件图标颜色", "override": "", "value": "@sub-font-color", "jsValue": "@global@subFontColor", "staticValue": "#4e5969", "localeDesc": {"ch": "文件上传组件上传列表文件图标颜色", "en": "Color of the file icon in the uploader list"}}, "uploaderFileIconFontSize": {"cssKey": "uploader-file-icon-font-size", "desc": "文件上传组件上传列表文件图标大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "文件上传组件上传列表文件图标大小", "en": "Font size of the file icon in the uploader list"}}, "uploaderFileIconMarginRight": {"cssKey": "uploader-file-icon-margin-right", "desc": "文件上传组件上传列表文件图标右边距", "override": "", "value": "~`pxtorem(12)`", "jsValue": "@getRem@12", "staticValue": "0.24rem", "localeDesc": {"ch": "文件上传组件上传列表文件图标右边距", "en": "Margin right of the file icon in the uploader list"}}, "uploaderItemBackgroundColor": {"cssKey": "uploader-item-background-color", "desc": "文件上传组件上传列表背景颜色", "override": "", "value": "@card-background-color", "jsValue": "@global@cardBackgroundColor", "staticValue": "#F7F8FA", "localeDesc": {"ch": "文件上传组件上传列表背景颜色", "en": "Background color of the uploader list"}}, "uploaderItemBorderRadius": {"cssKey": "uploader-item-border-radius", "desc": "文件上传组件上传列表外边框圆角", "override": "", "value": "~`pxtorem(2)`", "jsValue": "@getRem@2", "staticValue": "0.04rem", "localeDesc": {"ch": "文件上传组件上传列表外边框圆角", "en": "Border radius of the uploader list item"}}, "uploaderItemHeight": {"cssKey": "uploader-item-height", "desc": "文件上传组件上传列表高度", "override": "", "value": "~`px<PERSON><PERSON>(36)`", "jsValue": "@getRem@36", "staticValue": "0.72rem", "localeDesc": {"ch": "文件上传组件上传列表高度", "en": "Height of the uploader list item"}}, "uploaderItemMarginTop": {"cssKey": "uploader-item-margin-top", "desc": "文件上传组件上传列表上边距", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "文件上传组件上传列表上边距", "en": "Margin top of the uploader list item"}}, "uploaderItemPadding": {"cssKey": "uploader-item-padding", "desc": "文件上传组件上传列表内边距", "override": "", "value": "0 ~`px<PERSON><PERSON>(12)`", "jsValue": "0 @getRem@12", "staticValue": "0 0.24rem", "localeDesc": {"ch": "文件上传组件上传列表内边距", "en": "Padding of the uploader list item"}}, "uploaderItemTextColor": {"cssKey": "uploader-item-text-color", "desc": "文件上传组件上传列表文字颜色", "override": "", "value": "@font-color", "jsValue": "@global@fontColor", "staticValue": "#1d2129", "localeDesc": {"ch": "文件上传组件上传列表文字颜色", "en": "Color of the text in the uploader list"}}, "uploaderItemTextErrorColor": {"cssKey": "uploader-item-text-error-color", "desc": "文件上传组件上传列表上传失败文字颜色", "override": "", "value": "@danger-color", "jsValue": "@global@dangerColor", "staticValue": "#F53F3F", "localeDesc": {"ch": "文件上传组件上传列表上传失败文字颜色", "en": "Error status color of the text in the uploader list"}}, "uploaderItemTextFontSize": {"cssKey": "uploader-item-text-font-size", "desc": "文件上传组件上传列表文字大小", "override": "", "value": "~`px<PERSON><PERSON>(14)`", "jsValue": "@getRem@14", "staticValue": "0.28rem", "localeDesc": {"ch": "文件上传组件上传列表文字大小", "en": "Font size of the text in the uploader list"}}, "uploaderLoadedIconColor": {"cssKey": "uploader-loaded-icon-color", "desc": "文件上传组件上传列表加载成功图标颜色", "override": "", "value": "@success-color", "jsValue": "@global@successColor", "staticValue": "#00B42A", "localeDesc": {"ch": "文件上传组件上传列表加载成功图标颜色", "en": "Color of the loaded icon in the uploader list"}}, "uploaderLoadedIconFontSize": {"cssKey": "uploader-loaded-icon-font-size", "desc": "文件上传组件上传列表加载成功图标大小", "override": "", "value": "~`pxtorem(16)`", "jsValue": "@getRem@16", "staticValue": "0.32rem", "localeDesc": {"ch": "文件上传组件上传列表加载成功图标大小", "en": "Font size of the loaded icon in the uploader list"}}, "warningColor": {"cssKey": "warning-color", "desc": "基础警告态颜色", "override": "", "value": "#FF7D00", "jsValue": "#FF7D00", "staticValue": "#FF7D00", "localeDesc": {"ch": "基础警告态颜色", "en": "Base warning state color"}}}