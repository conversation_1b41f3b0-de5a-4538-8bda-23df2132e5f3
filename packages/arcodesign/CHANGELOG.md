# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [2.34.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.33.0...@arco-design/mobile-react@2.34.0) (2024-11-21)


### Bug Fixes

* `Popover` custom show transition animation ([#297](https://github.com/arco-design/arco-design-mobile/issues/297)) ([c3ceaf2](https://github.com/arco-design/arco-design-mobile/commit/c3ceaf2a7571626c298132b2c486d7128a748007))
* fix `useMountedState` in `hooks.ts` not correct init leavingRef in StrictMode. ([#292](https://github.com/arco-design/arco-design-mobile/issues/292)) ([af2bf5d](https://github.com/arco-design/arco-design-mobile/commit/af2bf5d5428eec252cac565ef708b2af6e0106a2))


### Features

* add a FAQ in the `Dialog`. ([#302](https://github.com/arco-design/arco-design-mobile/issues/302)) ([55f6940](https://github.com/arco-design/arco-design-mobile/commit/55f69406a3411faf5e67b2ef05a552121a9d3c04))
* optimize `Divider` hairline style ([99b1604](https://github.com/arco-design/arco-design-mobile/commit/99b16041aa8e4b71a82ffb5744f180ee9273ef3b))
* optimize `NoticeBar` close ([#294](https://github.com/arco-design/arco-design-mobile/issues/294)) ([a717540](https://github.com/arco-design/arco-design-mobile/commit/a7175403158c43c209b58c27cd994643f822aeeb))





# [2.33.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.32.0...@arco-design/mobile-react@2.33.0) (2024-10-24)


### Bug Fixes

* fix carousel scrolling bug when trigger click ([8b62ee3](https://github.com/arco-design/arco-design-mobile/commit/8b62ee3dfe489fc1890f099cc26c527d94f4ddbd))


### Features

* `Steps` support reverse order ([#287](https://github.com/arco-design/arco-design-mobile/issues/287)) ([8ed5614](https://github.com/arco-design/arco-design-mobile/commit/8ed5614d385d20bfb5fa1ada83d7f56e9f1c5150))
* add custom class support to `Avatar` component ([#285](https://github.com/arco-design/arco-design-mobile/issues/285)) ([8a37b7c](https://github.com/arco-design/arco-design-mobile/commit/8a37b7cbd05bacb457a3c2692a66a6a02cd7646f))





# [2.32.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.31.6...@arco-design/mobile-react@2.32.0) (2024-08-16)


### Features

* build output support esnext ([0308755](https://github.com/arco-design/arco-design-mobile/commit/030875530d1068e801962c6fc57b9b3e1916772e))
* export helpers render func ([9c0213b](https://github.com/arco-design/arco-design-mobile/commit/9c0213bc3a548b379491ada5fc747657a4a20a85))





## [2.31.6](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.31.5...@arco-design/mobile-react@2.31.6) (2024-07-09)


### Bug Fixes

* change `Stepper` flex to inline-flex ([e191b93](https://github.com/arco-design/arco-design-mobile/commit/e191b9319c918c249ebba24f4f6b336e6220da88))





## [2.31.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.31.4...@arco-design/mobile-react@2.31.5) (2024-07-01)


### Bug Fixes

* add `Stepper` width ([#274](https://github.com/arco-design/arco-design-mobile/issues/274)) ([b76f2ca](https://github.com/arco-design/arco-design-mobile/commit/b76f2cae577402979708bba07e864b9d2dca4f93))





## [2.31.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.31.3...@arco-design/mobile-react@2.31.4) (2024-06-26)


### Bug Fixes

* stepper controlled value ([4f8d060](https://github.com/arco-design/arco-design-mobile/commit/4f8d0603c3e292a337b0be09266a360d7e91e943))





## [2.31.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.31.2...@arco-design/mobile-react@2.31.3) (2024-05-11)


### Bug Fixes

* `Form` optimize "initialValues" and picker form item logic & add `DatePicker` default linked container ([#261](https://github.com/arco-design/arco-design-mobile/issues/261)) ([790ba3a](https://github.com/arco-design/arco-design-mobile/commit/790ba3a9dc4b289a20ddd18b5c11e2855bc6a8bd))





## [2.31.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.31.1...@arco-design/mobile-react@2.31.2) (2024-04-19)


### Bug Fixes

* add "@babel/runtime" to dependencies ([#257](https://github.com/arco-design/arco-design-mobile/issues/257)) ([c3f0f6c](https://github.com/arco-design/arco-design-mobile/commit/c3f0f6cf281b146c75f14622f1e454f165af4c01))





## [2.31.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.31.0...@arco-design/mobile-react@2.31.1) (2024-04-18)


### Bug Fixes

* support codeSandBox preview & adjust rtl mixins ([#254](https://github.com/arco-design/arco-design-mobile/issues/254)) ([5a7fb3a](https://github.com/arco-design/arco-design-mobile/commit/5a7fb3a4dd0ca8b1721036e5696b39e212905b5c))





# [2.31.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.10...@arco-design/mobile-react@2.31.0) (2024-04-09)


### Bug Fixes

* `Form` support picker components ([#249](https://github.com/arco-design/arco-design-mobile/issues/249)) ([3b1f6f7](https://github.com/arco-design/arco-design-mobile/commit/3b1f6f7460ac2417fd7c099c8f26904be972750e))
* `Tabs` do not execute forceupdate for the first time ([#248](https://github.com/arco-design/arco-design-mobile/issues/248)) ([e05cc9f](https://github.com/arco-design/arco-design-mobile/commit/e05cc9fd5df64a1b5a5d8054fc58b5e428997711))
* add icons ([#250](https://github.com/arco-design/arco-design-mobile/issues/250)) ([46ddefc](https://github.com/arco-design/arco-design-mobile/commit/46ddefc48fb39a51f2767ff384c535c19934b16f))
* optimize "autoFocus" of `SearchBar`, `Input`, `Textarea` & support search QA & optimize `Tabs` document ([#253](https://github.com/arco-design/arco-design-mobile/issues/253)) ([3684d09](https://github.com/arco-design/arco-design-mobile/commit/3684d09cdbde0f1c3f41ae0ca4aa6a03be830364))


### Features

* add component `Uploader` & abstract common logic of `ImagePicker` ([#251](https://github.com/arco-design/arco-design-mobile/issues/251)) ([9f6a795](https://github.com/arco-design/arco-design-mobile/commit/9f6a79573cfe5f74589ae244732db8c13ef07f0b))





## [2.30.10](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.9...@arco-design/mobile-react@2.30.10) (2024-03-04)


### Bug Fixes

* `Carousel` reset transforms when loop status changes ([#243](https://github.com/arco-design/arco-design-mobile/issues/243)) ([3983669](https://github.com/arco-design/arco-design-mobile/commit/398366920556ea4eb91ee87035f62443ad8db1d1))
* `Picker` onOk method adds new parameters ([#237](https://github.com/arco-design/arco-design-mobile/issues/237)) ([ef2e800](https://github.com/arco-design/arco-design-mobile/commit/ef2e8001c390174a12ca8b815e96c71436420e94))
* `Tabs` tab-cell support "updateLayout" method ([#234](https://github.com/arco-design/arco-design-mobile/issues/234)) ([1f17383](https://github.com/arco-design/arco-design-mobile/commit/1f17383592be7917a3f8392025ada60c788738f4))





## [2.30.9](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.8...@arco-design/mobile-react@2.30.9) (2024-02-27)


### Bug Fixes

* export type from `Skeleton` ([#233](https://github.com/arco-design/arco-design-mobile/issues/233)) ([7514ed9](https://github.com/arco-design/arco-design-mobile/commit/7514ed9baceccaf8f46c90b046bfe06594a66a59))
* warning for repeatedly calling createRoot in react18 of masking related components ([#231](https://github.com/arco-design/arco-design-mobile/issues/231)) ([6879ad7](https://github.com/arco-design/arco-design-mobile/commit/6879ad7f6225128b87bbf82aad34ba5836d4ac18))





## [2.30.8](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.7...@arco-design/mobile-react@2.30.8) (2024-01-31)


### Bug Fixes

*  `SwipeLoad` supports RTL ([#225](https://github.com/arco-design/arco-design-mobile/issues/225)) ([e39d09e](https://github.com/arco-design/arco-design-mobile/commit/e39d09e69c72c6e3ca65addabd1d90d96ac91eb1))
* `Ellipsis` optimize line height caculation ([#227](https://github.com/arco-design/arco-design-mobile/issues/227)) ([c791a8e](https://github.com/arco-design/arco-design-mobile/commit/c791a8e36309ce4887297fe711348f427e12954d))
* `Ellipsis` use ellipsisValueRef to reduce reflow ([#226](https://github.com/arco-design/arco-design-mobile/issues/226)) ([cbb761f](https://github.com/arco-design/arco-design-mobile/commit/cbb761fd05788e3cf191c94fd5870474e990cfa1))
* `Tabs` trigger "onChange" before distance is set to 0 ([#223](https://github.com/arco-design/arco-design-mobile/issues/223)) ([0f91c5b](https://github.com/arco-design/arco-design-mobile/commit/0f91c5b8e77d1b4688b47d3fd68e231ad964b214))





## [2.30.7](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.6...@arco-design/mobile-react@2.30.7) (2023-12-27)


### Bug Fixes

* `Ellipsis` html height calculation ([#218](https://github.com/arco-design/arco-design-mobile/issues/218)) ([6a73c43](https://github.com/arco-design/arco-design-mobile/commit/6a73c43c95d6bf6591c59fbbd67fa650a7059f91))
* style problem caused by the "tabIndex" attribute of lower versions of iOS ([#217](https://github.com/arco-design/arco-design-mobile/issues/217)) ([6a9406e](https://github.com/arco-design/arco-design-mobile/commit/6a9406ea0dfbdb50075a4c036757e6d7988c7d92))





## [2.30.6](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.5...@arco-design/mobile-react@2.30.6) (2023-12-26)


### Bug Fixes

* optimize `Ellipsis` height calculation ([#216](https://github.com/arco-design/arco-design-mobile/issues/216)) ([555723f](https://github.com/arco-design/arco-design-mobile/commit/555723f0091b6f0e28523a0aa37394bceacd6dc5))
* remove system default outline on iOS low-end machine of `Radio` & `Checkbox` ([#215](https://github.com/arco-design/arco-design-mobile/issues/215)) ([76dc29a](https://github.com/arco-design/arco-design-mobile/commit/76dc29ad26a1dcd37ab8935d8fbcebaa9ebe134f))
* resolve `DatePicker` rangeTs computed error ([#214](https://github.com/arco-design/arco-design-mobile/issues/214)) ([3dede0b](https://github.com/arco-design/arco-design-mobile/commit/3dede0bca7f0214d5e2b802038cafcc9390b2727))





## [2.30.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.4...@arco-design/mobile-react@2.30.5) (2023-12-19)


### Bug Fixes

* `ImagePreview` disable duplicate preventDefault ([#212](https://github.com/arco-design/arco-design-mobile/issues/212)) ([d91d251](https://github.com/arco-design/arco-design-mobile/commit/d91d251a64bd64ae914e88c119dda442bd72d722))
* `Slider` support custom "Popover" element ([#211](https://github.com/arco-design/arco-design-mobile/issues/211)) ([716dda1](https://github.com/arco-design/arco-design-mobile/commit/716dda1d273d3b77aa5072d2b3849776790c01e3))
* rtl for `Progress`, `Pagination`, `Picker`, `SearchBar`, `Dialog`, `IndexBar` ([#213](https://github.com/arco-design/arco-design-mobile/issues/213)) ([498a777](https://github.com/arco-design/arco-design-mobile/commit/498a777992fc44410944ddc17bd2090c9b06a2f2))





## [2.30.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.3...@arco-design/mobile-react@2.30.4) (2023-12-14)


### Bug Fixes

* `ImagePreview` fix problem that long image cannot scroll ([#207](https://github.com/arco-design/arco-design-mobile/issues/207)) ([78cf5a6](https://github.com/arco-design/arco-design-mobile/commit/78cf5a6f225f40a637368e85f998575be8148ba4))
* export `Skeleton` type ([#208](https://github.com/arco-design/arco-design-mobile/issues/208)) ([067907f](https://github.com/arco-design/arco-design-mobile/commit/067907f5432746690b5f8fa4a87a5560e9730d82))
* rtl for `PullRefresh`, `Avatar`, `Collapse`, `ImagePicker`, `Steps`, `Tag` ([#201](https://github.com/arco-design/arco-design-mobile/issues/201)) ([213a003](https://github.com/arco-design/arco-design-mobile/commit/213a003134d4e43539db1772b8b8dbd1dd37c907))
* support rtl for `DropdownMenu` ([#204](https://github.com/arco-design/arco-design-mobile/issues/204)) ([453dddb](https://github.com/arco-design/arco-design-mobile/commit/453dddb60b8f64e02654a8c3ece79bdc90e08fa6))
* support rtl for `Grid`, `Divider` ([#203](https://github.com/arco-design/arco-design-mobile/issues/203)) ([55a1f61](https://github.com/arco-design/arco-design-mobile/commit/55a1f619e430801d6cdb29f8fe90fe391eb26014))





## [2.30.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.2...@arco-design/mobile-react@2.30.3) (2023-11-24)


### Bug Fixes

* `ImagePreview` support shrinking animation before closed ([#195](https://github.com/arco-design/arco-design-mobile/issues/195)) ([7420bd5](https://github.com/arco-design/arco-design-mobile/commit/7420bd533a6e22a38a6fbd64407918580f64e095))
* support rtl for `Slider` & `Popover` ([#200](https://github.com/arco-design/arco-design-mobile/issues/200)) ([1756c8a](https://github.com/arco-design/arco-design-mobile/commit/1756c8a875f5e6fb3f8bdee7222d69c32a142ec3))





## [2.30.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.1...@arco-design/mobile-react@2.30.2) (2023-11-09)


### Bug Fixes

* `NavBar` add aria-label to back arrow ([#196](https://github.com/arco-design/arco-design-mobile/issues/196)) ([ad97b65](https://github.com/arco-design/arco-design-mobile/commit/ad97b652d142a4d2331fbc4699c7d15d61153e50))
* `Picker` fix the issue that value=0 cannot be selected ([#199](https://github.com/arco-design/arco-design-mobile/issues/199)) ([acb97ac](https://github.com/arco-design/arco-design-mobile/commit/acb97ac158995cea86f99755e6d0d60b166717fd))





## [2.30.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.30.0...@arco-design/mobile-react@2.30.1) (2023-11-07)


### Bug Fixes

* `Form` put error types to classname & i18n usage description ([#192](https://github.com/arco-design/arco-design-mobile/issues/192)) ([d3a611a](https://github.com/arco-design/arco-design-mobile/commit/d3a611ab368a32c98495e1601a5e16e4d6f4f86f))
* adjust dark mode style ([#193](https://github.com/arco-design/arco-design-mobile/issues/193)) ([919d661](https://github.com/arco-design/arco-design-mobile/commit/919d66120c1b2e445e3d0f95cf4d34e8c84e9998))





# [2.30.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.29.6...@arco-design/mobile-react@2.30.0) (2023-10-19)


### Features

* support dark mode ([#163](https://github.com/arco-design/arco-design-mobile/issues/163)) ([1693b1f](https://github.com/arco-design/arco-design-mobile/commit/1693b1f13bde86a075b6059dae290b9832c5cb99))
* support tool function & mixin analysis ([#147](https://github.com/arco-design/arco-design-mobile/issues/147)) ([f5b4dec](https://github.com/arco-design/arco-design-mobile/commit/f5b4decfb9b4ceadce8277be694f7f5a9a399d66))





## [2.29.6](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.29.5...@arco-design/mobile-react@2.29.6) (2023-10-09)


### Bug Fixes

* `Image` support loading & error area when using "staticLabel" ([#181](https://github.com/arco-design/arco-design-mobile/issues/181)) ([552a37d](https://github.com/arco-design/arco-design-mobile/commit/552a37d5a29cbbbe84107c0ba7cddeb1c685f2be))
* `Tabs` duplicate scrolling when "activeTab" is controlled in scroll mode ([7632138](https://github.com/arco-design/arco-design-mobile/commit/7632138f8d1164d8be6b55f5a65cc543b3b22684))
* incorrectly mounted elements when calling masking components using methods ([5740648](https://github.com/arco-design/arco-design-mobile/commit/5740648160fb6ff6d97a40da71c28d16034b4346))





## [2.29.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.29.4...@arco-design/mobile-react@2.29.5) (2023-10-07)


### Bug Fixes

* `ImagePicker` the image will show up again if deleted when uploading ([#182](https://github.com/arco-design/arco-design-mobile/issues/182)) ([f26512a](https://github.com/arco-design/arco-design-mobile/commit/f26512a165e3c8ca082f716a64bdc8e7e86517b7))
* `ImagePreview` refresh problem when onload of multiple pictures is triggered at the same time ([#173](https://github.com/arco-design/arco-design-mobile/issues/173)) ([c71c526](https://github.com/arco-design/arco-design-mobile/commit/c71c526e81a2851df6653682bfad9b9b84704462))
* `LoadMore` support "getDataWithEndReachCheck" ([#180](https://github.com/arco-design/arco-design-mobile/issues/180)) ([25baa3c](https://github.com/arco-design/arco-design-mobile/commit/25baa3cae665edb7fa4f316d80886f497c38f7a3))
* `PullRefresh` optimize transform performance & fix decimal point deviation in ifShouldHandle ([#179](https://github.com/arco-design/arco-design-mobile/issues/179)) ([a826ed2](https://github.com/arco-design/arco-design-mobile/commit/a826ed26096cec3a6487d2bd58345113a5d17dfd))
* `Tabs` scroll offset in ssr when defaultActiveTab is greater than 0 ([#175](https://github.com/arco-design/arco-design-mobile/issues/175)) ([98aa1d7](https://github.com/arco-design/arco-design-mobile/commit/98aa1d7c5abe8851778f2aa5c2308fe807d5e4d9))





## [2.29.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.29.3...@arco-design/mobile-react@2.29.4) (2023-09-13)


### Bug Fixes

* `LoadMore` support the prop "getDataWhenNoScrollAtFirst" ([#170](https://github.com/arco-design/arco-design-mobile/issues/170)) ([7a4881d](https://github.com/arco-design/arco-design-mobile/commit/7a4881d92cb9fbb1a345240a2aaa940af0afbe63))
* `Picker` support default values when click confirm button ([c6f12ee](https://github.com/arco-design/arco-design-mobile/commit/c6f12ee54131235470c380c3bd59a22983247622))
* `SearchBar` adjust clear icon ([4f540fc](https://github.com/arco-design/arco-design-mobile/commit/4f540fcb255553fb59d0b972a74edf23a7224a16))
* `Tabs` prevent jumping when no touchmove event ([df01464](https://github.com/arco-design/arco-design-mobile/commit/df014640dc220af7f911fe1c2cb1e504c463a37b))





## [2.29.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.29.2...@arco-design/mobile-react@2.29.3) (2023-09-12)


### Bug Fixes

* `ImagePreview` remove duplicate setTimeout for double-click judgment ([0a5565b](https://github.com/arco-design/arco-design-mobile/commit/0a5565bf06c5d5737d730a59227dd239feaf50d1))
* `PickerView` adjust animation duration ([#164](https://github.com/arco-design/arco-design-mobile/issues/164)) ([6c7f020](https://github.com/arco-design/arco-design-mobile/commit/6c7f020baeb8b2a7ba00d08bf9feaf8f8ebcca3f))
* `Popover` fix the problem that popover.menu does not show when defaultVisible=true ([b9c7397](https://github.com/arco-design/arco-design-mobile/commit/b9c73975f4333482152b207bb6bee8dfd7f0b7e3))





## [2.29.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.29.1...@arco-design/mobile-react@2.29.2) (2023-08-28)


### Bug Fixes

* `LoadMore` support the prop "disabled" ([b1881b2](https://github.com/arco-design/arco-design-mobile/commit/b1881b2821debb3b94c66285d712e8f7522e6be7))
* `Tabs` the prop "tabBarFixed" supports passing in specific fixed values ([ae3158f](https://github.com/arco-design/arco-design-mobile/commit/ae3158f7f7d5d1eebca8921d6d4802aa53b31fbe))





## [2.29.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.29.0...@arco-design/mobile-react@2.29.1) (2023-08-21)


### Bug Fixes

* fix wrong logic after click clear icon of `Input`, `Textarea` and `SearchBar` ([#156](https://github.com/arco-design/arco-design-mobile/issues/156)) ([b741263](https://github.com/arco-design/arco-design-mobile/commit/b741263fd1a1b9c3da9c106f7487714e33e042f2))





# [2.29.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.28.2...@arco-design/mobile-react@2.29.0) (2023-08-17)


### Bug Fixes

* compatibility between grid and image picker ([#151](https://github.com/arco-design/arco-design-mobile/issues/151)) ([28ae5b9](https://github.com/arco-design/arco-design-mobile/commit/28ae5b9a8eb6dd91422d50d3f2cfe8b0d7ccdfa4))


### Features

* add new component `Skeleton` ([#136](https://github.com/arco-design/arco-design-mobile/issues/136)) ([49351f6](https://github.com/arco-design/arco-design-mobile/commit/49351f639b7a3272b55f189447d935946933ddd6))





## [2.28.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.28.1...@arco-design/mobile-react@2.28.2) (2023-08-07)


### Bug Fixes

* `Form` bind this in form-item ([#148](https://github.com/arco-design/arco-design-mobile/issues/148)) ([5616d53](https://github.com/arco-design/arco-design-mobile/commit/5616d537b921b009df61addccf966c5e9363a0cb))
* `PickerView` flicker issue with picker off and on when momentum scrolling is not over ([#143](https://github.com/arco-design/arco-design-mobile/issues/143)) ([e91557f](https://github.com/arco-design/arco-design-mobile/commit/e91557fc1545dd6179329dc342de4967301a7d73))
* `Tabs` use boundingRect instead of offset when calc tabs wrap size ([#146](https://github.com/arco-design/arco-design-mobile/issues/146)) ([7ece3a9](https://github.com/arco-design/arco-design-mobile/commit/7ece3a9eb76abb010aa88678af47c15a0bd319dc))





## [2.28.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.28.0...@arco-design/mobile-react@2.28.1) (2023-08-02)


### Bug Fixes

* `Avatar` make the prop "src" optional ([f99bbc8](https://github.com/arco-design/arco-design-mobile/commit/f99bbc812223ae6c8960b1d712040f1b221efb72))
* add [@types](https://github.com/types) to peerDependencies ([42f3d5a](https://github.com/arco-design/arco-design-mobile/commit/42f3d5ab19144702d7c371c6cbd1aa031a690abe))





# [2.28.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.27.5...@arco-design/mobile-react@2.28.0) (2023-07-14)


### Bug Fixes

* `Collapse` update with the latest opened ([#140](https://github.com/arco-design/arco-design-mobile/issues/140)) ([b963787](https://github.com/arco-design/arco-design-mobile/commit/b96378761557f4d90f09f789f662e9d3588c3cbd))
* `ImagePreview` fix scroll through ([ea3f9bc](https://github.com/arco-design/arco-design-mobile/commit/ea3f9bc5d0980f70c81e2de99084e0a11187b3c1))
* `Sticky` support the method "updatePlaceholderLayout" ([842b4fe](https://github.com/arco-design/arco-design-mobile/commit/842b4fe6084b3ae282bd54850e381fbe34bd826f))


### Features

* RTL support for `Badge`, `Button`, `Cell`, `Checkbox`, `Form`, `Radio`, `Rate`, `Switch` and `Tabs` ([#135](https://github.com/arco-design/arco-design-mobile/issues/135)) ([97de976](https://github.com/arco-design/arco-design-mobile/commit/97de976ba514ec0f48103bd4f0c535ebceb8981a))





## [2.27.5](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.27.4...@arco-design/mobile-react@2.27.5) (2023-07-04)


### Bug Fixes

* `ImagePreview` fix long picture display problem ([#137](https://github.com/arco-design/arco-design-mobile/issues/137)) ([3df1f71](https://github.com/arco-design/arco-design-mobile/commit/3df1f71a8b1f63ed5e1986284f2746438de59d70))
* `LoadMore` fix the inaccurate scrollheight when using multiple loadmore ([#130](https://github.com/arco-design/arco-design-mobile/issues/130)) ([da76f12](https://github.com/arco-design/arco-design-mobile/commit/da76f125734579a1921a3ace0964d5ff845cd545))
* `Stepper` disable status ([#134](https://github.com/arco-design/arco-design-mobile/issues/134)) ([71dabe7](https://github.com/arco-design/arco-design-mobile/commit/71dabe71f3508ac8cb9a800d8ac1be01df509f28))
* error caught when using "getComputedStyle" ([#129](https://github.com/arco-design/arco-design-mobile/issues/129)) ([daa8f67](https://github.com/arco-design/arco-design-mobile/commit/daa8f67961d9d2751a14c0c3f7759b54fe0579cb))
* transition in StrictMode ([#131](https://github.com/arco-design/arco-design-mobile/issues/131)) ([084448b](https://github.com/arco-design/arco-design-mobile/commit/084448b0e4b43ea7d8632e232c1665ce00449c3d))





## [2.27.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.27.3...@arco-design/mobile-react@2.27.4) (2023-05-19)


### Bug Fixes

* `IndexBar` remove redundant children judgement ([#123](https://github.com/arco-design/arco-design-mobile/issues/123)) ([cbba80d](https://github.com/arco-design/arco-design-mobile/commit/cbba80dcdb7d7b58e893ab9bd08cc3388008a6f3))
* `ShowMonitor` fix disable observe when remount ([#125](https://github.com/arco-design/arco-design-mobile/issues/125)) ([73a5572](https://github.com/arco-design/arco-design-mobile/commit/73a5572961c7c2d6956c6c9406e486412b99e1c8))





## [2.27.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.27.2...@arco-design/mobile-react@2.27.3) (2023-05-16)


### Bug Fixes

* `Tabs` support swipe energy-saving mode ([50a440b](https://github.com/arco-design/arco-design-mobile/commit/50a440bc808c418a01160d8dcbaa2a870a5a4b97))





## [2.27.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.27.1...@arco-design/mobile-react@2.27.2) (2023-05-10)


### Bug Fixes

* `LoadMore` status reset time for controlled mode ([#121](https://github.com/arco-design/arco-design-mobile/issues/121)) ([f141e7f](https://github.com/arco-design/arco-design-mobile/commit/f141e7f8e91944f5d68bb5fbf59238d5d48b8eda))
* `NavBar` fix warning in nextjs ssr ([#118](https://github.com/arco-design/arco-design-mobile/issues/118)) ([57c2838](https://github.com/arco-design/arco-design-mobile/commit/57c283852f26e06fb4d38dc8753cff7a34232a98))
* `ShowMonitor` support observer singleton ([#113](https://github.com/arco-design/arco-design-mobile/issues/113)) ([7099b30](https://github.com/arco-design/arco-design-mobile/commit/7099b307935357e496b902f6148ea0854da2912a))
* `SwipeLoad` support bounce animate ([#29](https://github.com/arco-design/arco-design-mobile/issues/29)) ([cad1a6b](https://github.com/arco-design/arco-design-mobile/commit/cad1a6ba19e6d45a33233281fba863434f553ce2))
* `Tabs` enable auto scroll when activeTab changed in scroll mode ([#116](https://github.com/arco-design/arco-design-mobile/issues/116)) ([07374f1](https://github.com/arco-design/arco-design-mobile/commit/07374f105f99418d447fc494c90d99d876601f9b))





## [2.27.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.27.0...@arco-design/mobile-react@2.27.1) (2023-04-26)


### Bug Fixes

* `IndexBar` modify utils deps ([#115](https://github.com/arco-design/arco-design-mobile/issues/115)) ([b322d71](https://github.com/arco-design/arco-design-mobile/commit/b322d716e8dc6a107afb2a8b84edfcc0f5daddc2))





# [2.27.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.26.0...@arco-design/mobile-react@2.27.0) (2023-04-25)


### Bug Fixes

* `PickerView` support the complete data currently selected in "onPickerChange" & code optimization ([#60](https://github.com/arco-design/arco-design-mobile/issues/60)) ([b90bbd2](https://github.com/arco-design/arco-design-mobile/commit/b90bbd24bd1fee554ef095144b25b6f36132fa0f))
* import style from dependent components ([8d6e0b0](https://github.com/arco-design/arco-design-mobile/commit/8d6e0b0a892b9cbd4ab521c5ef1b91439825ae18))


### Features

* add new component `IndexBar` ([#63](https://github.com/arco-design/arco-design-mobile/issues/63)) ([a76e57a](https://github.com/arco-design/arco-design-mobile/commit/a76e57a6fdeeb10255fe99ae37689f186d6ce471))
* add new component `Keyboard` ([#79](https://github.com/arco-design/arco-design-mobile/issues/79)) ([44cea7f](https://github.com/arco-design/arco-design-mobile/commit/44cea7fe8e4febde454a83edfda9a546409213ed))





# [2.26.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.25.4...@arco-design/mobile-react@2.26.0) (2023-04-11)


### Bug Fixes

* `Carousel` fix confused rendering due to missing keys ([565b51e](https://github.com/arco-design/arco-design-mobile/commit/565b51ef84382fec04d9ecfd4264600a332913bc))
* `Image` border radius ([#107](https://github.com/arco-design/arco-design-mobile/issues/107)) ([6f1fe3e](https://github.com/arco-design/arco-design-mobile/commit/6f1fe3e4d72330d7e65ea95a58e79231779ae2f7))
* `Progress` state error when updating progress before animation done ([#105](https://github.com/arco-design/arco-design-mobile/issues/105)) ([a8d2398](https://github.com/arco-design/arco-design-mobile/commit/a8d2398f9139316985bd3c8ea1fd6760412bea5b))
* `Tabs` support the prop "underlineAdaptive" ([b81b535](https://github.com/arco-design/arco-design-mobile/commit/b81b535e4933e0d51e0bcf8d7135b39d706f0b24))
* add prop "blockChangeWhenCompositing" for input type components ([a082a02](https://github.com/arco-design/arco-design-mobile/commit/a082a02143cd53f61e757be7bd26391f8d04e6f6))


### Features

* `Carousel` & `NoticeBar` & `Input` & `Cell` rtl adaption ([bbd2fbb](https://github.com/arco-design/arco-design-mobile/commit/bbd2fbb3689a807f0d37b7ac51ac131e37224ae6))
* add new component `Divider` ([#102](https://github.com/arco-design/arco-design-mobile/issues/102)) ([2ad2830](https://github.com/arco-design/arco-design-mobile/commit/2ad2830b3851cb1e7e8d96edda3a75f27edf2e7a))





## [2.25.4](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.25.3...@arco-design/mobile-react@2.25.4) (2023-02-27)


### Bug Fixes

* `Ellipsis` optimize click event type ([#94](https://github.com/arco-design/arco-design-mobile/issues/94)) ([e0e0369](https://github.com/arco-design/arco-design-mobile/commit/e0e03691d48f5a0f78e3be339a3f28b6f70c9798))
* `SearchBar` disable default appearance of search input ([#97](https://github.com/arco-design/arco-design-mobile/issues/97)) ([648ccb3](https://github.com/arco-design/arco-design-mobile/commit/648ccb3024c092dd48c4c79c0c36b3b9ef61fb08))





## [2.25.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.25.2...@arco-design/mobile-react@2.25.3) (2023-02-10)


### Bug Fixes

* `ImagePreview` fix display problems when passing fit layout ([#91](https://github.com/arco-design/arco-design-mobile/issues/91)) ([244107f](https://github.com/arco-design/arco-design-mobile/commit/244107fa4764141e8e57e871cad60f19f0592706))
* `NavBar` support the prop 'onScrollChange' ([46f2a5c](https://github.com/arco-design/arco-design-mobile/commit/46f2a5cf6afa5abec235d9d6311f40c19f8c1bc0))





## [2.25.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.25.1...@arco-design/mobile-react@2.25.2) (2023-02-08)


### Bug Fixes

* wrap style division in parentheses ([ebe7a8f](https://github.com/arco-design/arco-design-mobile/commit/ebe7a8f8ba6f51ac75b45488b672e48ca9c2e0bb))





## [2.25.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.25.0...@arco-design/mobile-react@2.25.1) (2023-02-01)


### Bug Fixes

* checkbox & radio exported type ([901681c](https://github.com/arco-design/arco-design-mobile/commit/901681ccfd4788b3e50954dbde6e4475ac1dbdc4))





# [2.25.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.24.2...@arco-design/mobile-react@2.25.0) (2023-02-01)


### Bug Fixes

* `Carousel` optimize the method changeIndex ([0088985](https://github.com/arco-design/arco-design-mobile/commit/00889858f2826987db4930a84e297ac0399443c5))
* `ImagePreview` add image prop extraNode ([#83](https://github.com/arco-design/arco-design-mobile/issues/83)) ([61006f9](https://github.com/arco-design/arco-design-mobile/commit/61006f96a81261c40b7984d03b008c091d5a8f9b))
* `PullRefresh` optimize default value of the prop 'type' & add prop 'useIosOptimize' ([31b0ed1](https://github.com/arco-design/arco-design-mobile/commit/31b0ed12837b77d2b41cfff80879bbab83084846))


### Features

* add new component `Form` ([#77](https://github.com/arco-design/arco-design-mobile/issues/77)) ([54b5bda](https://github.com/arco-design/arco-design-mobile/commit/54b5bda1c66b8318b59c9031fb0634c93dd94c7b))





## [2.24.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.24.1...@arco-design/mobile-react@2.24.2) (2023-01-10)


### Bug Fixes

*  `Ellipsis` innerHTML keep wrap ([#80](https://github.com/arco-design/arco-design-mobile/issues/80)) ([29afadc](https://github.com/arco-design/arco-design-mobile/commit/29afadc571bba16fd2b8641622de1f2d0fbf5ba4))





## [2.24.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.24.0...@arco-design/mobile-react@2.24.1) (2023-01-04)


### Bug Fixes

* `Ellipsis` support floatEllipsisNode ([#76](https://github.com/arco-design/arco-design-mobile/issues/76)) ([cc8e3d6](https://github.com/arco-design/arco-design-mobile/commit/cc8e3d6df1eec5a528a47c255c49ca94a326ca18))





# [2.24.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.23.0...@arco-design/mobile-react@2.24.0) (2022-12-20)


### Bug Fixes

* support accessibility mode ([#65](https://github.com/arco-design/arco-design-mobile/issues/65)) ([fa7789c](https://github.com/arco-design/arco-design-mobile/commit/fa7789c1866341244cbffcbef1d1be375880bd82))


### Features

* component function support custom context ([#52](https://github.com/arco-design/arco-design-mobile/issues/52)) ([e5bd260](https://github.com/arco-design/arco-design-mobile/commit/e5bd260f6df128f8cd44e26464f1b5e5e6deff11))





# [2.23.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.22.3...@arco-design/mobile-react@2.23.0) (2022-11-29)


### Bug Fixes

* `ImagePicker` add click event ([#67](https://github.com/arco-design/arco-design-mobile/issues/67)) ([0f76fda](https://github.com/arco-design/arco-design-mobile/commit/0f76fda245ac9a171525800c1d170c93411f2820))


### Features

* add new component `Stepper` ([#46](https://github.com/arco-design/arco-design-mobile/issues/46)) ([a6ce795](https://github.com/arco-design/arco-design-mobile/commit/a6ce7958345ca5170cf00b7efa5cf74d02ebe93d))





## [2.22.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.22.2...@arco-design/mobile-react@2.22.3) (2022-11-23)


### Bug Fixes

* `Carousel` supports autoPlayDirection ([#72](https://github.com/arco-design/arco-design-mobile/issues/72)) ([527aaf4](https://github.com/arco-design/arco-design-mobile/commit/527aaf4bdf86fe4cf995bd8e4bd2580e9db8c942))
* flexible.js support umd ([#73](https://github.com/arco-design/arco-design-mobile/issues/73)) ([d3f35c0](https://github.com/arco-design/arco-design-mobile/commit/d3f35c093a579bb404fff91dbf6437cdd93f19f7))





## [2.22.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.22.0...@arco-design/mobile-react@2.22.2) (2022-11-09)


### Bug Fixes

* `PullRefresh` support custom style ([edac495](https://github.com/arco-design/arco-design-mobile/commit/edac4954526b8f2e3a86c9742752222e19f899f8))
* `Steps` optimize style & support custom alignment ([617c570](https://github.com/arco-design/arco-design-mobile/commit/617c57003135215787897b48c07d2b674c482828))
* `Tabs` fix touch event on foldable screen ([a67fa71](https://github.com/arco-design/arco-design-mobile/commit/a67fa71c8944e27aa43d6353e4a24e4ba676cec3))
* optimize .text-medium mixin ([ab62ab2](https://github.com/arco-design/arco-design-mobile/commit/ab62ab2cdb51174cbd2106467bbf1a3db4694982))





# [2.22.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.21.3...@arco-design/mobile-react@2.22.0) (2022-10-28)


### Bug Fixes

*  `SwipeAction` prevent touchmove by gesture ([#59](https://github.com/arco-design/arco-design-mobile/issues/59)) ([c9ef474](https://github.com/arco-design/arco-design-mobile/commit/c9ef474312eb9f9af736431f16147cfb56dee623))
* `Tabs` supports tabBarStopPropagation ([#62](https://github.com/arco-design/arco-design-mobile/issues/62)) ([ba24d17](https://github.com/arco-design/arco-design-mobile/commit/ba24d178320e12b0aa95e5d75feb34e5e5163af4))


### Features

* add support for react18 ([#57](https://github.com/arco-design/arco-design-mobile/issues/57)) ([7cbb4fc](https://github.com/arco-design/arco-design-mobile/commit/7cbb4fcd5bd8c2809141bf0546f42ecf3367fd73))





## [2.21.3](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.21.2...@arco-design/mobile-react@2.21.3) (2022-09-30)


### Bug Fixes

* `Ellipsis` special case for truncateHTML ([fa9db03](https://github.com/arco-design/arco-design-mobile/commit/fa9db03baecbb378e38d2217eddb85dec59d27d4))
* `ImagePicker` import grid style ([e257ce0](https://github.com/arco-design/arco-design-mobile/commit/e257ce00dc3ecd4c61934149751fb817d3242159))
* `Tabs` tab bar underline optimize ([20c9621](https://github.com/arco-design/arco-design-mobile/commit/20c962113e8a0dc43995813dda328239a1807a50))





## [2.21.2](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.21.1...@arco-design/mobile-react@2.21.2) (2022-09-07)


### Bug Fixes

* `Button` fix style when use reset.less ([bb05f2c](https://github.com/arco-design/arco-design-mobile/commit/bb05f2c279089115f73fc39c523c9767ed37a8c4))





## [2.21.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.21.0...@arco-design/mobile-react@2.21.1) (2022-09-01)


### Bug Fixes

* `Ellipsis` duplicate reflow ([41fc0a2](https://github.com/arco-design/arco-design-mobile/commit/41fc0a237c3737e0ad2db09623db3577ca243348))
* `SearchBar` export type ([5ee5ebf](https://github.com/arco-design/arco-design-mobile/commit/5ee5ebf9923b0d1582a52a646f6daea168b89946))
* `SwipeAction` import optimize ([5060d25](https://github.com/arco-design/arco-design-mobile/commit/5060d2570e0eb85c6f6b344e9ac9f0b50212f486))





# [2.21.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.19.1...@arco-design/mobile-react@2.21.0) (2022-08-29)


### Bug Fixes

* `Button` support coverIconWhenLoading ([#33](https://github.com/arco-design/arco-design-mobile/issues/33)) ([4213959](https://github.com/arco-design/arco-design-mobile/commit/421395918c8dc8ec8e6cb742aeed46503eaef925))
* `Dropdown` optimize animation when direction changed ([2022692](https://github.com/arco-design/arco-design-mobile/commit/20226924759486d66ecae7a8e466bc1c676cd923))
* `Dropdown` support customize select icon & value of string type ([9eb6f5d](https://github.com/arco-design/arco-design-mobile/commit/9eb6f5db76d2a1975e61a478d897522cf8bcbf8e))
* `DropdownMenu` optimize renderSelectLabel params ([1c685fd](https://github.com/arco-design/arco-design-mobile/commit/1c685fd85851f6735d1d91dc49c6f28ade0a858c))
* `ImagePicker` convert event.target.files to array ([dec36f1](https://github.com/arco-design/arco-design-mobile/commit/dec36f1edf313e34bb1fd290ce314206639a5717))
* `ImagePicker` fix uploaded images change ([#44](https://github.com/arco-design/arco-design-mobile/issues/44)) ([7511837](https://github.com/arco-design/arco-design-mobile/commit/75118376f7b08f39a2c438737075a0af0d39004d))
* `ImagePicker` optimize display when uploading ([a99f2f3](https://github.com/arco-design/arco-design-mobile/commit/a99f2f3e2927ce2f263a496da09e391395a8b545))
* `NavBar` trigger caculation when showOffset changes ([06119ce](https://github.com/arco-design/arco-design-mobile/commit/06119ceeefb286ac963dae0f8b49a1112063d1b9))
* `PopupSwiper` optimize moving direction ([fe1d100](https://github.com/arco-design/arco-design-mobile/commit/fe1d100fb3aba069ad66255c93654823beebf249))
* `ShowMonitor` support flush visible status ([#31](https://github.com/arco-design/arco-design-mobile/issues/31)) ([bdad151](https://github.com/arco-design/arco-design-mobile/commit/bdad151e8a466f20e3e033ccec9a5d458ff4fbf4))
* `Steps` converge style to token ([6cf4f26](https://github.com/arco-design/arco-design-mobile/commit/6cf4f267a271dc0d03f559c4aca8199f9fcae2ec))
* `Tabs` limit max scroll distance close ([#37](https://github.com/arco-design/arco-design-mobile/issues/37)) ([e716d01](https://github.com/arco-design/arco-design-mobile/commit/e716d014dfa34c4159d4011d2a1ccd5b7d133b4c)), closes [#36](https://github.com/arco-design/arco-design-mobile/issues/36)
* components classname optimize: `Avatar` & `Button` & `Badge` ([e6b70dd](https://github.com/arco-design/arco-design-mobile/commit/e6b70dd5eebb23646f2e7639b8a8a73bd51e57c3))


### Features

* add new component `ImagePicker` ([#24](https://github.com/arco-design/arco-design-mobile/issues/24)) ([73f6156](https://github.com/arco-design/arco-design-mobile/commit/73f615651f46dc670f29733f6b99ec56795fc48b))
* add new component `SearchBar` ([#22](https://github.com/arco-design/arco-design-mobile/issues/22)) ([36cf4d4](https://github.com/arco-design/arco-design-mobile/commit/36cf4d4d61506358cd84b41738d817db8399c04f))
* add new component `SwipeAction` ([221dc57](https://github.com/arco-design/arco-design-mobile/commit/221dc5776a508fcccd27de992e1ae58d137b7fb6))





# [2.20.0](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.19.1...@arco-design/mobile-react@2.20.0) (2022-08-12)


### Bug Fixes

* `Button` support coverIconWhenLoading ([#33](https://github.com/arco-design/arco-design-mobile/issues/33)) ([2ef79f5](https://github.com/arco-design/arco-design-mobile/commit/2ef79f5f77081682075d00beb8639c87515aa4b3))
* `Dropdown` support customize select icon & value of string type ([9eb6f5d](https://github.com/arco-design/arco-design-mobile/commit/9eb6f5db76d2a1975e61a478d897522cf8bcbf8e))
* `DropdownMenu` optimize renderSelectLabel params ([1c685fd](https://github.com/arco-design/arco-design-mobile/commit/1c685fd85851f6735d1d91dc49c6f28ade0a858c))
* `ImagePicker` optimize display when uploading ([eba85a4](https://github.com/arco-design/arco-design-mobile/commit/eba85a46bfadddc5924f8cd89f614416bdb0e3c1))
* `NavBar` trigger caculation when showOffset changes ([06119ce](https://github.com/arco-design/arco-design-mobile/commit/06119ceeefb286ac963dae0f8b49a1112063d1b9))
* `ShowMonitor` support flush visible status ([#31](https://github.com/arco-design/arco-design-mobile/issues/31)) ([b5e12b2](https://github.com/arco-design/arco-design-mobile/commit/b5e12b27cd464cc137be810bea828850934072b0))
* `Steps` converge style to token ([6cf4f26](https://github.com/arco-design/arco-design-mobile/commit/6cf4f267a271dc0d03f559c4aca8199f9fcae2ec))
* components classname optimize: `Avatar` & `Button` & `Badge` ([cba4ac4](https://github.com/arco-design/arco-design-mobile/commit/cba4ac4bfafbe4edd53019b2c2ee314e092e409b))


### Features

* add new component `ImagePicker` ([#24](https://github.com/arco-design/arco-design-mobile/issues/24)) ([73f6156](https://github.com/arco-design/arco-design-mobile/commit/73f615651f46dc670f29733f6b99ec56795fc48b))





## [2.19.1](https://github.com/arco-design/arco-design-mobile/compare/@arco-design/mobile-react@2.19.0...@arco-design/mobile-react@2.19.1) (2022-07-22)


### Bug Fixes

* `Notify` export types ([0a096ba](https://github.com/arco-design/arco-design-mobile/commit/0a096bac78eeadf60dbfcf6be58f8f51e24f5343))
* `Steps` style optimize ([e4a0343](https://github.com/arco-design/arco-design-mobile/commit/e4a0343c5258f69d0d0fee97047b78011f54d50d))
* button add disableWhenLoading options ([#13](https://github.com/arco-design/arco-design-mobile/issues/13)) ([0c21f35](https://github.com/arco-design/arco-design-mobile/commit/0c21f350d5ad9af167cf1604acd19df5618daeda))





# 2.19.0 (2022-07-07)


### Features

* first publish for open source ([c11f528](https://github.com/arco-design/arco-design-mobile/commit/c11f528880afe3807f8d96e7667fd5b630a47f7e))
