{"name": "@arco-design/mobile-react", "version": "2.34.0", "description": "", "main": "cjs/index.js", "module": "esm/index.js", "scripts": {"prebuild": "node ../../scripts/build/pre-build.js", "build": "rimraf esm/* umd/* cjs/* && gulp build && npm run dist", "dist": "rimraf dist/* && cd ../../ && node packages/arcodesign/rollup.lib.js", "prepublishOnly": "npm run build", "build:bd": "npm run build", "test": "jest"}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"@arco-design/mobile-utils": "2.20.1", "@arco-design/transformable": "^1.0.0", "@babel/runtime": "^7", "lodash.throttle": "^4.1.1", "resize-observer-polyfill": "^1.5.1"}, "devDependencies": {"babel-jest": "^25.3.0", "jest": "^25.3.0"}, "peerDependencies": {"@types/react": ">=16.9.0", "@types/react-dom": ">=16.9.0", "@types/react-transition-group": ">=4.3.0", "react": ">=16.9.0", "react-dom": ">=16.9.0", "react-transition-group": ">=4.3.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}, "@types/react-transition-group": {"optional": true}}, "publishConfig": {"access": "public"}, "gitHead": "7b0a8e3446dd2429a7a14a4a4d86688aa726887f"}