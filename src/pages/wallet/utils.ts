import {
  createDebugger,
  DebugConfigBuilder,
  type DebugScenario as BaseDebugScenario_Type
} from '@/pages/debug';

// Wallet特定的调试场景
export enum WalletDebugScenario {
  PAYMENT_UNAVAILABLE = 'payment_unavailable', // 支付不可用
  BALANCE = 'balance',               // 有余额'
  NOBALANCE = 'nobalance',               // 有余额'
  EMPTY_WITHDRAWALS = 'empty_withdrawals', // 空提现列表
  FULL_WITHDRAWALS = 'full_withdrawals', // 完整提现列表
  WITHDRAWAl_COMFIRM = 'withdrawal_confirm', // 提现确认
  WITHDRAWAl_SUCCESS = 'withdrawal_success', // 提现成功
  WITHDRAWAl_FAILED = 'withdrawal_failed', // 提现失败
  EMPTY_TRANSACTIONS = 'empty_transactions', // 空交易列表
  FULL_TRANSACTIONS = 'full_transactions', // 完整交易列表
  // ... 其他场景
}

// 合并基础调试场景和Wallet特定场景
export type DebugScenario = BaseDebugScenario_Type | WalletDebugScenario;

// 创建Wallet调试器配置
const walletDebugConfig = new DebugConfigBuilder('Wallet')
  .addScenarios({
    [WalletDebugScenario.PAYMENT_UNAVAILABLE]: {
      displayName: '支付不可用',
      icon: '',
      description: '支付不可用',
      color: 'primary'
    },
    [WalletDebugScenario.BALANCE]: {
      displayName: '有余额',
      icon: '',
      description: '有余额',
      color: 'primary'
    },[WalletDebugScenario.NOBALANCE]: {
      displayName: '无余额',
      icon: '',
      description: '无余额',
      color: 'primary'
    },
  })
  .addMockDataGenerators({
    [WalletDebugScenario.PAYMENT_UNAVAILABLE]: () => generateMockUserData(WalletDebugScenario.PAYMENT_UNAVAILABLE),
    [WalletDebugScenario.BALANCE]: () => generateMockBalanceData(WalletDebugScenario.BALANCE),
    [WalletDebugScenario.NOBALANCE]: () => generateMockBalanceData(WalletDebugScenario.NOBALANCE),
  })
  .build();

// 创建Wallet调试器
export const walletDebugger = createDebugger(walletDebugConfig);

// Mock数据生成函数
function generateMockUserData(_scenario: WalletDebugScenario) {
  return {
    id: 1,
    name: '测试用户',
    // 根据场景返回不同的用户数据
  };
}

function generateMockBalanceData(scenario: WalletDebugScenario) {
  switch (scenario) {
    case WalletDebugScenario.BALANCE:
      return {
        availableAmount: 100.50,
        totalAmount: 150.00,
        pendingAmount: 49.50
      };
    case WalletDebugScenario.NOBALANCE:
      return {
        availableAmount: 0,
        totalAmount: 0,
        pendingAmount: 0
      };
    default:
      return {
        availableAmount: 100.50,
        totalAmount: 150.00,
        pendingAmount: 49.50
      };
  }
}

// 导出调试器的方法
export const isDebugMode = walletDebugger.isDebugMode;
export const getDebugScenario = walletDebugger.getDebugScenario;
export const debugLog = walletDebugger.debugLog;
export const mockApiResponse = walletDebugger.mockApiResponse;