import { View, Text } from "@tarojs/components";
import "./history.less";
import Taro from "@tarojs/taro";
import { useState, useEffect, useCallback } from "react";
import { 
  Cell, 
  Toast, 
  Collapse,
  LoadMore
} from "@arco-design/mobile-react";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

// API
import { getTransactionList } from "@/utils/api/common/common_user";

// 类型定义
interface TransactionRecord {
  id: number;
  userId: number;
  userIdBy: number | null;
  userNameBy: string | null;
  income: number | null;
  expenses: number | null;
  type: number;
  merchantName: string;
  createTime: number;
}

interface TransactionGroup {
  date: number[];
  records: TransactionRecord[];
}

interface TransactionListData {
  list: TransactionGroup[];
  total: number;
}

interface PageState {
  loading: boolean;
  refreshing: boolean;
  data: TransactionGroup[];
  hasMore: boolean;
  page: number;
  pageSize: number;
  expandedKeys: string[];
}

// 交易类型映射
const TRANSACTION_TYPES = {
  10: "转账收入",
  20: "提现",
  30: "交易手续费", 
  40: "订单收入",
  50: "平台手续费"
};

export default function TransactionHistory() {
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<PageState>({
    loading: false,
    refreshing: false,
    data: [],
    hasMore: true,
    page: 1,
    pageSize: 20,
    expandedKeys: []
  });

  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    setPageState(prev => ({ ...prev, loading: true }));
    try {
      await loadData(1, true);
    } catch (error) {
      console.error('页面初始化失败:', error);
      Toast.error('页面加载失败');
    } finally {
      setPageState(prev => ({ ...prev, loading: false }));
    }
  };

  // ==================== 数据加载 ====================
  const loadData = async (page: number = 1, isRefresh: boolean = false) => {
    if (pageState.loading && !isRefresh) return;

    setPageState(prev => ({ 
      ...prev, 
      loading: true,
      refreshing: isRefresh 
    }));

    try {
      const userInfo = Taro.getStorageSync('userInfo');
      if (!userInfo?.id) {
        console.error('[交易明细] 获取用户信息失败');
        return;
      }
    
      const response = await getTransactionList({
        pageNo: page,
        pageSize: pageState.pageSize,
        userId: userInfo.id
      });

      if (response.code === 0) {
        const newData = response.data.list || [];
        setPageState(prev => ({
          ...prev,
          data: isRefresh ? newData : [...prev.data, ...newData],
          hasMore: newData.length === prev.pageSize,
          page: page,
          // 默认展开第一个分组
          expandedKeys: isRefresh && newData.length > 0 ? [formatDateKey(newData[0].date)] : prev.expandedKeys
        }));
      } else {
        Toast.error(response.msg || '加载失败');
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      Toast.error('网络错误，请重试');
    } finally {
      setPageState(prev => ({ 
        ...prev, 
        loading: false,
        refreshing: false 
      }));
    }
  };

  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  const handleLoadMore = useCallback(() => {
    if (pageState.hasMore && !pageState.loading) {
      loadData(pageState.page + 1);
    }
  }, [pageState.hasMore, pageState.loading, pageState.page]);

  const handleRefresh = useCallback(() => {
    loadData(1, true);
  }, []);

  // 处理折叠面板展开/收起
  const handleCollapseChange = (keys: string[]) => {
    setPageState(prev => ({ ...prev, expandedKeys: keys }));
  };

  // 处理交易记录点击
  const handleRecordClick = (record: TransactionRecord) => {
    // 根据交易类型跳转到不同的详情页
    const detailType = getDetailType(record.type);
    const params = {
      id: record.id,
      type: detailType,
      amount: record.income || record.expenses || 0,
      createTime: record.createTime,
      merchantName: record.merchantName,
      userNameBy: record.userNameBy
    };
    
    const url = `/pages/wallet/transactions/detail/index?${Object.entries(params)
      .map(([k, v]) => `${k}=${encodeURIComponent(v || '')}`)
      .join('&')}`;
    
    Taro.navigateTo({ url });
  };

  // ==================== 工具函数 ====================
  const formatDateKey = (date: number[]): string => {
    return `${date[0]}-${String(date[1]).padStart(2, '0')}-${String(date[2]).padStart(2, '0')}`;
  };

  const formatDate = (date: number[]): string => {
    return `${date[0]}-${String(date[1]).padStart(2, '0')}-${String(date[2]).padStart(2, '0')}`;
  };

  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  const formatAmount = (amount: number): string => {
    return amount.toFixed(2);
  };

  const getTransactionTypeName = (type: number): string => {
    return TRANSACTION_TYPES[type] || "未知类型";
  };

  const getDetailType = (type: number): string => {
    // 映射到对应的详情页类型
    switch (type) {
      case 10: return "income"; // 订单收入
      case 20: return "withdraw_success"; // 提现成功
      case 30: return "transaction_fee"; // 交易手续费
      case 40: return "income"; // 订单收入
      case 50: return "platform_fee"; // 平台手续费
      default: return "income";
    }
  };

  const calculateDayTotal = (records: TransactionRecord[]) => {
    const income = records.reduce((sum, record) => sum + (record.income || 0), 0);
    const expenses = records.reduce((sum, record) => sum + (record.expenses || 0), 0);
    return { income, expenses };
  };

  // ==================== 渲染 ====================
  return (
    <View className="transactionHistoryPage">
      <YkNavBar title="收支明细" onClickLeft={handleBack} />
      
      <View className="content">
        {pageState.data.length > 0 ? (
          <Collapse 
            activeKey={pageState.expandedKeys}
            onChange={handleCollapseChange}
            className="transaction-collapse"
          >
            {pageState.data.map((group) => {
              const dateKey = formatDateKey(group.date);
              const dayTotal = calculateDayTotal(group.records);
              
              return (
                <Collapse.Item 
                  key={dateKey} 
                  itemKey={dateKey}
                  title={
                    <View className="collapse-header">
                      <Text className="date-text">{formatDate(group.date)}</Text>
                      <Text className="summary-text">
                        收入￥{formatAmount(dayTotal.income)}    支出￥{formatAmount(dayTotal.expenses)}
                      </Text>
                    </View>
                  }
                >
                  <View className="transaction-list">
                    {group.records.map((record) => (
                      <Cell
                        key={record.id}
                        className="transaction-item"
                        onClick={() => handleRecordClick(record)}
                        showArrow
                        label={
                          <View className="transaction-info">
                            <Text className="transaction-title">
                              {getTransactionTypeName(record.type)}-{record.merchantName}
                            </Text>
                            <Text className="transaction-time">
                              {formatTime(record.createTime)}
                            </Text>
                          </View>
                        }
                        append={
                          <Text className={`transaction-amount ${record.income ? 'income' : 'expense'}`}>
                            {record.income ? `+${formatAmount(record.income)}` : `-${formatAmount(record.expenses || 0)}`}
                          </Text>
                        }
                      />
                    ))}
                  </View>
                </Collapse.Item>
              );
            })}
          </Collapse>
        ) : !pageState.loading ? (
          <View className="empty-state">
            <Text>暂无交易记录</Text>
          </View>
        ) : null}

        <LoadMore
          loading={pageState.loading}
          hasMore={pageState.hasMore}
          onLoadMore={handleLoadMore}
          onRefresh={handleRefresh}
          refreshing={pageState.refreshing}
        />
      </View>
    </View>
  );
}
