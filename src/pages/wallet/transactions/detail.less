@import '@arco-design/mobile-react/style/mixin.less';

// 页面根容器样式
[id^="/pages/wallet/transactions/detail/index"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

// 页面主容器
.transactionDetailPage {
  position: relative;
  width: 100%;
  min-height: 100vh;
  
  // 内容区域
  .content {
    padding: 30px 0px 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }
  
  // 金额展示区域
  .amount-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    
    .transaction-type {
      font-size: 15px;
      font-weight: bold;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: var(--dark-font-color) !important;
      });
    }
    
    .amount-text {
      font-size: 24px;
      font-weight: bold;
      opacity: 0.9;
      
      &.income {
        .use-var(color, success-color);
        .use-dark-mode-query({
          color: var(--dark-success-color) !important;
        });
      }
      
      &.expense {
        color: #EB483F;
        .use-dark-mode-query({
          color: #F76965 !important;
        });
      }
    }
  }
  
  // 详情信息区域
  .detail-section {
    width: 100%;
    padding: 0px 16px;
    
    .detail-group {
      .use-var(background-color, background-color);
      .use-dark-mode-query({
        background-color: var(--dark-container-background-color) !important;
      });
      border-radius: 4px;
      overflow: hidden;
      
      .detail-item {
        padding: 15px 0px;
        border-bottom: 0.5px solid var(--line-color);
        .use-dark-mode-query({
          border-bottom-color: var(--dark-line-color) !important;
        });
        
        &:last-child {
          border-bottom: none;
        }
        
        .arco-cell-content {
          padding: 0;
        }
        
        // 标签样式
        .arco-cell-label {
          font-size: 14px;
          font-weight: 500;
          .use-var(color, sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
          min-width: 56px;
          margin-right: 15px;
        }
        
        // 值样式
        .detail-value {
          font-size: 14px;
          font-weight: 500;
          .use-var(color, font-color);
          .use-dark-mode-query({
            color: var(--dark-font-color) !important;
          });
          text-align: right;
          flex: 1;
        }
        
        // 银行信息特殊样式
        .bank-info {
          display: flex;
          flex-direction: column;
          gap: 5px;
          align-items: flex-end;
          
          .bank-name {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
          
          .bank-card {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
        }
      }
    }
  }
  
  // 加载状态
  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    
    text {
      font-size: 15px;
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-info-font-color) !important;
      });
    }
  }
}
