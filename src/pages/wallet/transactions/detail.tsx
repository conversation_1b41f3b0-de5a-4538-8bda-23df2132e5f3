import { View, Text } from "@tarojs/components";
import "./detail.less";
import Taro, { useRouter } from "@tarojs/taro";
import { useState, useEffect } from "react";
import { 
  Cell, 
  Toast
} from "@arco-design/mobile-react";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

// 类型定义
interface DetailParams {
  id: string;
  type: string;
  amount: string;
  createTime: string;
  merchantName: string;
  userNameBy?: string;
}

interface DetailConfig {
  title: string;
  amountColor: 'income' | 'expense';
  fields: Array<{
    label: string;
    key: string;
    formatter?: (value: any) => string;
  }>;
}

export default function TransactionDetail() {
  const router = useRouter();
  const [params, setParams] = useState<DetailParams | null>(null);

  // ==================== 生命周期 ====================
  useEffect(() => {
    const routerParams = router.params;
    if (routerParams.id) {
      setParams({
        id: routerParams.id,
        type: routerParams.type || 'income',
        amount: routerParams.amount || '0',
        createTime: routerParams.createTime || '',
        merchantName: routerParams.merchantName || '',
        userNameBy: routerParams.userNameBy
      });
    } else {
      Toast.error('参数错误');
      Taro.navigateBack({ delta: 1 });
    }
  }, [router.params]);

  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  // ==================== 工具函数 ====================
  const formatTime = (timestamp: string): string => {
    const date = new Date(parseInt(timestamp));
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
  };

  const formatAmount = (amount: string): string => {
    const num = parseFloat(amount);
    return num.toFixed(2);
  };

  const generateOrderNumber = (id: string): string => {
    // 生成模拟订单号
    return `**************************`;
  };

  const generateFlowNumber = (id: string): string => {
    // 生成模拟流水号
    return `**************************`;
  };

  const maskBankCard = (merchantName: string): string => {
    // 模拟银行卡号脱敏
    return "6391****3333";
  };

  // ==================== 配置映射 ====================
  const getDetailConfig = (type: string): DetailConfig => {
    switch (type) {
      case 'withdraw_success':
        return {
          title: '提现',
          amountColor: 'expense',
          fields: [
            { label: '提现时间', key: 'createTime', formatter: formatTime },
            { label: '收款账户', key: 'merchantName' },
            { label: '银行卡号', key: 'merchantName', formatter: maskBankCard },
            { label: '提现单号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber }
          ]
        };
      
      case 'withdraw_failed':
        return {
          title: '提现失败退回',
          amountColor: 'income',
          fields: [
            { label: '退回时间', key: 'createTime', formatter: formatTime },
            { label: '退回账户', key: 'merchantName' },
            { label: '银行卡号', key: 'merchantName', formatter: maskBankCard },
            { label: '提现单号', key: 'id', formatter: generateOrderNumber }
          ]
        };
      
      case 'transaction_fee':
        return {
          title: '交易手续费',
          amountColor: 'expense',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '交易说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品的交易手续费` }
          ]
        };
      
      case 'platform_fee':
        return {
          title: '平台手续费',
          amountColor: 'expense',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '交易说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品的平台手续费` }
          ]
        };
      
      case 'income':
        return {
          title: '订单收入',
          amountColor: 'income',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '交易说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品` }
          ]
        };
      
      case 'refund':
        return {
          title: '订单退款',
          amountColor: 'expense',
          fields: [
            { label: '退款时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '退款说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品退款` }
          ]
        };
      
      default:
        return {
          title: '交易详情',
          amountColor: 'income',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '交易说明', key: 'merchantName' }
          ]
        };
    }
  };

  // ==================== 渲染 ====================
  if (!params) {
    return (
      <View className="transactionDetailPage">
        <YkNavBar title="交易详情" onClickLeft={handleBack} />
        <View className="loading">
          <Text>加载中...</Text>
        </View>
      </View>
    );
  }

  const config = getDetailConfig(params.type);
  const amount = parseFloat(params.amount);
  const isIncome = config.amountColor === 'income';

  return (
    <View className="transactionDetailPage">
      <YkNavBar title="交易详情" onClickLeft={handleBack} />
      
      <View className="content">
        {/* 金额展示区域 */}
        <View className="amount-section">
          <Text className="transaction-type">{config.title}</Text>
          <Text className={`amount-text ${config.amountColor}`}>
            {isIncome ? '+' : '-'}{formatAmount(params.amount)}
          </Text>
        </View>

        {/* 详情信息区域 */}
        <View className="detail-section">
          <Cell.Group bordered={false} className="detail-group">
            {config.fields.map((field, index) => {
              let value = params[field.key as keyof DetailParams] || '';
              if (field.formatter) {
                value = field.formatter(value);
              }
              
              return (
                <Cell
                  key={index}
                  label={field.label}
                  append={
                    field.key === 'merchantName' && field.label === '银行卡号' ? (
                      <View className="bank-info">
                        <Text className="bank-name">{params.merchantName}</Text>
                        <Text className="bank-card">{value}</Text>
                      </View>
                    ) : (
                      <Text className="detail-value">{value}</Text>
                    )
                  }
                  className="detail-item"
                />
              );
            })}
          </Cell.Group>
        </View>
      </View>
    </View>
  );
}
