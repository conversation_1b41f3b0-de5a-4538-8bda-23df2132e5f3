import React, { useState } from 'react';
import { View, Text, Input } from '@tarojs/components';
import { <PERSON><PERSON>, Picker, Cell } from '@arco-design/mobile-react';
import YkNavBar from '@/components/ykNavBar';
import './address.less';
import { addressData } from './addressData';
import { addAddress } from '@/utils/api/common/common_user';
import Taro from "@tarojs/taro";

const AddressPage = () => {
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [region, setRegion] = useState([]);
  const [regionText, setRegionText] = useState('');
  const [detail, setDetail] = useState('');
  const [visible, setVisible] = React.useState(false);
  const [pickerValue, setPickerValue] = React.useState([]);
  const pickerRef = React.useRef();

  // 地区数据已单独引入 addressData


  const addAddressData = () => {


    const data = {
      userId:Taro.getStorageSync('userInfo').id,
      name: name,
      phone: phone,
      area: pickerValue.join('-'),
      detailedAddress: detail,
    }
    addAddress(data).then(res => {
      console.log('res', res);
      if(res.code == 0){
        Taro.showToast({
          title: '添加成功',
          icon: 'success',
          duration: 2000,
        })
        setTimeout(() => {
          Taro.navigateBack();
        }, 1500);
      }
    })
  }

  return (
    <View className="address-page">
      <YkNavBar title="添加地址" />
      <View className="address-section-title">收货信息</View>
      <View className="address-form">
        <Cell label={<Text className="address-label" style={{paddingRight: '26px'}}>姓名</Text>} bordered={false}>
          <Input
            className="address-input"
            placeholder="请输入姓名"
            value={name}
            onInput={e => setName(e.detail.value)}
            maxlength={20}
          />
        </Cell>
        <Cell label={<Text className="address-label">手机号码</Text>} bordered={false}>
          <Input
            className="address-input"
            placeholder="请输入手机号"
            value={phone}
            onInput={e => setPhone(e.detail.value)}
            maxlength={11}
          />
        </Cell>
        <Picker
                ref={pickerRef}
                title="选择省市区"
                visible={visible}
                cascade={true}
                data={addressData}
                maskClosable={true}
                hideEmptyCols={true}
                onHide={() => {
                    setVisible(false);
                }}
                onOk={(value, data) => {
                    console.log('on ok', value, data);
                    setPickerValue(value)
                }}
                onPickerChange={() => {
                    if (pickerRef.current) {
                    }
                }}
                value={pickerValue}
                cols={3}
                needBottomOffset={true}
            />
        <Cell  label="所在地区"   showArrow    onClick={() => {setVisible(true);}}  >
            <View className="address-input address-region-value">
                {pickerValue.join('-') || <Text className="address-placeholder">省/市/区/县</Text>}
            </View>
        </Cell>
        
        <Cell label={<Text className="address-label">详细地址</Text>} bordered={false}>
          <Input
            className="address-input"
            placeholder="小区楼栋/乡村名称"
            value={detail}
            onInput={e => setDetail(e.detail.value)}
            maxlength={50}
          />
        </Cell>
      </View>
      <View className="address-bottom-bar">
        <Button className="address-btn" type="primary"
        onClick={() => {
          addAddressData()
        }}
        >确定</Button>
      </View>
    </View>
  );
};

export default AddressPage;
