import React, { useState } from "react";
import { View, Image, Text, Input } from "@tarojs/components";
import { <PERSON>ton, Stepper, Cell, Avatar, Switch, Picker } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar";
import "./index.less";
import Taro from "@tarojs/taro";

const goodsList = [
  {
    shop: "尚品百货",
    goods: [
      {
        img: "https://img.alicdn.com/imgextra/i1/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nk SB Dunk Low LV联名-高端定制 很帮 休闲鞋大厂纯原品质细节完美纯白新款...",
        price: 230,
        size: "36.5",
        count: 1,
      },
      {
        img: "https://img.alicdn.com/imgextra/i1/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nk SB Dunk Low LV联名-高端定制 很帮 休闲鞋大厂纯原品质细节完美纯白新款...",
        price: 230,
        size: "38",
        count: 1,
      },
    ],
  },
];

export default function PayOrderPage() {
  const [goods, setGoods] = useState(goodsList[0].goods);
  const [remark, setRemark] = useState("");
  const [customSender, setCustomSender] = useState(false);
  const [senderName, setSenderName] = useState('');
  const [senderPhone, setSenderPhone] = useState('');

  // 数量加减
  const handleCountChange = (idx, value) => {
    const newGoods = [...goods];
    newGoods[idx].count = value;
    setGoods(newGoods);
  };

  const totalCount = goods.reduce((sum, g) => sum + g.count, 0);
  const totalPrice = goods.reduce((sum, g) => sum + g.price * g.count, 0);
  const freight = 10;
  const payPrice = totalPrice + freight;
  const single = React.useMemo(() => {
    return [
          [
              {label: <span className="demo-picker-color"><i  /><span>快递</span></span>, value: '快递'},
              {label: <span className="demo-picker-color"><i  /><span>顺丰到付</span></span>, value: '顺丰到付'},
              {label: <span className="demo-picker-color"><i  /><span>自提</span></span>, value: '自提'},
              {label: <span className="demo-picker-color"><i  /><span>其他</span></span>, value: '其他'},
          ]
      ];
    }, []);

  const [singleValue, setSingleValue] = React.useState(['快递']);


  return (
    <View className="pay-order-page">
      <YkNavBar title="确认订单" />
      <View className="pay-list-section">
        <Cell.Group bordered={false}>
          
          <Cell label={<div className="demo-cell-avatar-label">
                          <Avatar src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/small_image_5.jpg" />
                          <span>添加收货地址</span>
                       </div>} showArrow  
                onClick={() => Taro.navigateTo({url: '/pages/order/pay/address'})}
          />

          <Picker
            title="配送方式"
            cascade={false}
            data={single}
            maskClosable={true}
            value={singleValue}
            onChange={val => setSingleValue(val as any as string[]  )}
            mountOnEnter={false}
            unmountOnExit={false}
            renderLinkedContainer={(_, data) => (
              <Cell label={<Text>配送方式</Text>} className="pay-list-cell" text={singleValue[0] as string} showArrow  />
            )}
          />

          
          <Cell label={<Text>自定义发件人</Text>} className="pay-list-cell">
            <Switch platform="android" checked={customSender} onChange={setCustomSender} />
          </Cell>
          {customSender && (
            <View className="custom-sender-panel">
              <View className="custom-sender-row">
                <Text className="custom-sender-label">发件人</Text>
                <Input
                  className="custom-sender-input"
                  placeholder="默认为商家，可修改"
                  value={senderName}
                  disabled={!customSender}
                  onInput={e => setSenderName(e.detail.value)}
                />
              </View>
              <View className="custom-sender-row">
                <Text className="custom-sender-label">手机号</Text>
                <Input
                  className="custom-sender-input"
                  placeholder="默认为商家手机号，可修改"
                  value={senderPhone}
                  disabled={!customSender}
                  onInput={e => setSenderPhone(e.detail.value)}
                />
              </View>
            </View>
          )}


        </Cell.Group>
      </View>
      <View className="pay-goods-section">
        <View className="shop-title-row">
          <Avatar src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/small_image_5.jpg" />
          <Text className="shop-title">尚品百货</Text>
          <Text className="shop-cert" />
        </View>
        {goods.map((g, idx) => (
          <View className="goods-row" key={idx}>             
                <View className="goods-info">
                    <Image className="goods-img" src={g.img} />
                    <Text className="goods-title">{g.name}</Text>
                    <View style={{display: 'flex', justifyContent:'flex-start', alignItems: 'center',flexDirection: 'column'}}>
                    <Text className="goods-price">￥{g.price}</Text>
                    <Text className="goods-count">x{g.count}</Text>
                    </View>
                </View>        
                <View className="goods-bottom-row">
                    <Text className="goods-size">{g.size}</Text>
                    <Stepper
                    value={g.count}
                    min={1}
                    onChange={v => handleCountChange(idx, v || 1)}
                    />
                </View>
          </View>
        ))}
        <View className="pay-summary-list">
          <View className="pay-summary-row">
            <Text>商品数量</Text>
            <Text>{totalCount}</Text>
          </View>
          <View className="pay-summary-row">
            <Text>商品总金额</Text>
            <Text>¥{totalPrice.toFixed(2)}</Text>
          </View>
          <View className="pay-summary-row">
            <Text>运费</Text>
            <Text>¥{freight.toFixed(2)}</Text>
          </View>
          <View className="pay-summary-row pay-summary-total">
            <Text>实付金额</Text>
            <Text className="pay-price">¥{payPrice.toFixed(2)}</Text>
          </View>
        </View>
        <View className="pay-remark-row">
          <Input
            placeholder="留言(选填)"
            value={remark}
            onChange={(_, val) => setRemark(val)}
            className="pay-remark-input"
            clearable
          />
        </View>
      </View>
      <View className="pay-bottom-bar">
        <Text className="pay-bottom-count">共{totalCount}件 ¥{totalPrice}</Text>
        <Button type="primary" className="pay-bottom-btn">
          立即支付 ¥{payPrice}
        </Button>
      </View>
    </View>
  );
}
