{"name": "arco-design-mobile-react-github", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"init": "lerna bootstrap -- --legacy-peer-deps", "add": "node scripts/init/init-component.js", "add-composite": "node scripts/init/init-composite-component.js", "add-faq": "node scripts/init/init-faq.js", "start": "cross-env NODE_ENV=development webpack-dev-server --progress --config scripts/sites/webpack.dev.mobile.js", "start:vite": "vite --config scripts/sites/vite.mobile.config.ts --host", "start:pc": "cross-env NODE_ENV=development webpack-dev-server --progress --config scripts/sites/webpack.dev.pc.js", "start:pc-vite": "vite --config scripts/sites/vite.pc.config.ts --host", "start:home": "cross-env NODE_ENV=development webpack-dev-server --progress --config scripts/sites/webpack.dev.home.js", "start:home-vite": "vite --config scripts/sites/vite.home.config.ts --host", "start:all": "npm start & npm run start:pc & npm run start:home", "build": "lerna run build", "eslint": "eslint packages/ sites/ --fix", "test": "jest --config packages/arcodesign/jest.config.js", "prepub": "node scripts/publish/pre-publish.js", "pub": "lerna publish --no-git-reset --registry=https://registry.npmjs.org/", "postpub": "node scripts/publish/post-publish.js", "postinstall": "npm run init && lerna run build --scope @arco-design/mobile-utils", "pub-canary": "node scripts/publish/pub-canary.js", "lint-staged": "lint-staged", "site:home": "cross-env NODE_ENV=production webpack --progress --config scripts/sites/webpack.prod.home.js", "site:mobile": "cross-env NODE_ENV=production webpack --progress --config scripts/sites/webpack.prod.mobile.js", "site:pc": "cross-env NODE_ENV=production webpack --progress --config scripts/sites/webpack.prod.pc.js", "dev:demo": "node scripts/dev/dev-demo.js $@"}, "author": "<EMAIL>", "license": "MIT", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/eslint-plugin": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.10.2", "@babel/preset-react": "^7.8.3", "@babel/preset-typescript": "^7.8.3", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@rollup/plugin-alias": "^3.1.1", "@rollup/plugin-babel": "^5.2.0", "@rollup/plugin-commonjs": "^15.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "@rollup/plugin-replace": "^2.3.3", "@rollup/plugin-typescript": "^5.0.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^12.0.0", "@types/estree": "^1.0.0", "@types/jest": "^26.0.24", "@types/lodash.debounce": "^4.0.6", "@types/lodash.throttle": "^4.1.6", "@types/marked": "^4.0.3", "@types/node": "^17.0.41", "@types/react": "^16.9.23", "@types/react-dom": "^16.9.5", "@types/react-router-dom": "^5.1.3", "@types/react-transition-group": "^4.2.4", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "@vitejs/plugin-react-refresh": "^1.3.6", "autoprefixer": "^10.4.12", "babel-jest": "^26.6.3", "babel-loader": "^8.2.5", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "eslint": "^7.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.3.0", "eslint-plugin-arco-mobile": "^1.0.3", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "fs-extra": "^10.1.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-file": "^0.4.0", "gulp-less": "^4.0.1", "gulp-postcss": "^9.0.1", "gulp-replace": "^1.0.0", "gulp-typescript": "^6.0.0-alpha.1", "hasha": "^5.2.2", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.0", "husky": "^4.2.3", "jest": "^26.6.3", "lerna": "^4.0.0", "less": "^3.13.1", "less-loader": "^11.1.0", "less-plugin-npm-import": "^2.1.0", "lint-staged": "^10.0.8", "lz-string": "^1.5.0", "marked": "^4.0.17", "mini-css-extract-plugin": "^2.6.1", "postcss": "^8.4.18", "postcss-loader": "^7.0.1", "postcss-url": "^8.0.0", "prettier": "^2.0.5", "rollup": "^2.26.10", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "schema-utils": "^4.0.0", "through2": "^4.0.2", "ts-loader": "^9.4.1", "tslib": "^2.0.1", "typescript": "4.1.6", "vconsole-webpack-plugin": "^1.5.2", "vite": "^3.1.8", "vite-plugin-externals": "^0.6.2", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.6.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@arco-design/transformable": "1.0.2", "@babel/runtime": "^7.24.4", "axios": "^0.27.2", "es6-promise": "^4.2.8", "lodash.debounce": "^4.0.8", "lodash.kebabcase": "^4.1.1", "lodash.throttle": "^4.1.1", "qrcode.react": "^3.1.0", "react": "^16.14.0", "react-dom": "^16.14.0", "react-router-dom": "^5.1.2", "react-transition-group": "^4.3.0"}, "resolutions": {"@types/react": "^16"}}